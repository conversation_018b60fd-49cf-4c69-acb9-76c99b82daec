<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="账号信息"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list flex-between" @click="goAccountSecurity">
        <view class="list-item-text text-32rpx">账号安全</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goResignation">
        <view class="list-item-text text-32rpx">离开公司</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goDeregisterAccount">
        <view class="list-item-text text-32rpx">注销公司</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>

      <view class="setting-list flex-between" @click="goLoginDevice">
        <view class="list-item-text text-32rpx">登陆设备管理</view>
        <wd-icon
          class="arrow-right-icon"
          color="#888888"
          name="chevron-right"
          size="20px"
        ></wd-icon>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 账号安全
const goAccountSecurity = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/accountSecurity/index',
  })
}

// 离开公司
const goResignation = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/Resignation/index',
  })
}

// 注销账号
const goDeregisterAccount = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/deregisterAccount/index',
  })
}

// 登陆设备管理
const goLoginDevice = () => {
  uni.navigateTo({
    url: '/setting/loginDevice/index',
  })
}
</script>
<style lang="scss" scoped>
.setting {
  padding: 20rpx 20rpx;
  // margin-bottom: 320rpx;
  .setting-list {
    padding: 30rpx 20rpx;

    .list-item-text {
      color: #333;
    }
  }
}
</style>
