<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="通知与提醒"></CustomNavBar>
    <view class="setting">
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">消息免打扰</view>
          <view class="setting-left-subName subText">开启后，在制定时间内将不再接受消息推送</view>
        </view>
        <wd-switch
          v-model="checkedStatus[0].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>

      <!-- <view class="setting-list flex-between m-t-30rpx m-b-30rpx">
        <view class="text-32rpx font-w-500">切换身份</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">牛人</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view> -->

      <view class="line-h"></view>
      <view class="setting-list flex-between m-t-40rpx">
        <view class="setting-left">
          <view class="setting-left-name">接收聊天消息通知</view>
          <view class="setting-left-subName subText">聊天消息推送</view>
        </view>
        <wd-switch
          v-model="checkedStatus[1].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <view class="setting-list flex-between m-t-40rpx m-b-40rpx">
        <view class="setting-left">
          <view class="setting-left-name">短信通知</view>
          <view class="setting-left-subName subText">一些重要消息会通过短信的方式通知</view>
        </view>
        <wd-switch
          v-model="checkedStatus[2].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
      <view class="line-h"></view>
      <view class="setting-list m-t-40rpx ts-text m-b-40rpx">消息提示方式</view>
      <view class="m-b-40rpx">
        <view class="setting-list flex-between">
          <view class="setting-left">
            <view class="setting-left-name">声音与震动</view>
          </view>
          <wd-switch
            v-model="checkedStatus[3].checked"
            active-color="#13CE66"
            inactive-color="#777777"
            size="40rpx"
          />
        </view>
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">APP顶部横幅提醒</view>
          <view class="setting-left-subName subText">在使用APP过程中，重要消息会收到顶部提醒</view>
        </view>
        <wd-switch
          v-model="checkedStatus[4].checked"
          active-color="#13CE66"
          inactive-color="#777777"
          size="40rpx"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}

const checkedStatus = ref([
  {
    name: '消息免打扰',
    checked: false,
  },
  {
    name: '接收聊天消息通知',
    checked: true,
  },
  {
    name: '短信通知',
    checked: true,
  },
  {
    name: '声音与震动',
    checked: false,
  },
  {
    name: 'APP顶部横幅提醒',
    checked: true,
  },
])
</script>
<style scoped lang="scss">
.setting {
  padding: 40rpx 0rpx;
}
.swich-btn {
  width: 120rpx;
  height: 50rpx;
}
.setting-list {
  padding: 0rpx 40rpx;

  .setting-left {
    .setting-left-name {
      font-size: 32rpx;
      color: #333333;
    }
  }
}

.line-h {
  width: 100%;
  height: 10rpx;
  background-color: #e8e8e8;
}

.ts-text {
  font-size: 22rpx;
  color: #333;
}

::v-deep .u-switch__node {
  width: 46rpx !important;
  height: 32rpx !important;
  background-color: #3e9cff;
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx 40rpx;
}

::v-deep .u-cell__value {
  font-size: 20rpx;
  color: #888888;
}

::v-deep .u-cell__label--large {
  font-size: 22rpx;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}

.setting-text {
  font-size: 22rpx;
  color: #888888;
}
</style>
