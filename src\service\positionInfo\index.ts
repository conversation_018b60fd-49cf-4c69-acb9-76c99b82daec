import { POST } from '../index'
import {
  positionInfoQueryIMCardInfoByIdDataInt,
  positionInfoQueryIMCardInfoByIdInt,
  positionInfoBatchSendPositionListDataInt,
  positionInfoBatchSendPositionListInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** 通过双方id查询IM框卡片信息接口 */
export const positionInfoQueryIMCardInfoById = (
  data: positionInfoQueryIMCardInfoByIdDataInt,
  config?: HttpRequestConfig,
) =>
  POST<positionInfoQueryIMCardInfoByIdInt>(
    '/easyzhipin-api/positionInfo/queryIMCardInfoById',
    data,
    config,
  )

/** 一键投递岗位列表查询接口 */
export const positionInfoBatchSendPositionList = (
  data: positionInfoBatchSendPositionListDataInt,
  config?: HttpRequestConfig,
) =>
  POST<positionInfoBatchSendPositionListInt[]>(
    '/easyzhipin-api/positionInfo/batchSendPositionList',
    data,
    config,
  )
