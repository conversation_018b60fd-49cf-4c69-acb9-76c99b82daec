import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useLoginStore = defineStore(
  'login',
  () => {
    const cityObj = ref({}) // 城市
    const positionObj = ref<AnyObject>({}) // 岗位
    const jobObj = ref<AnyArray>([]) // 行业
    // 行业
    const industryObj = ref<AnyObject>({}) // 行业
    // 首页岗位下标
    const homeJobAvtive = ref(null)
    // 首页地址选择
    const homeCity1 = ref<AnyObject>({})
    const homeCity2 = ref<AnyObject>({})
    const homeCity3 = ref<AnyObject>({})
    // 首页竞争力本地生成的列表
    const myjobList = ref([])
    // 去重后的竞争力列表
    const myjobFillterList = ref([])
    const setCity = (data: any) => {
      cityObj.value = data
    }
    const setpositionData = (data: any) => {
      positionObj.value = data
    }
    const setjobArry = (data: any) => {
      jobObj.value = data
    }
    const setindustryObj = (data: any) => {
      industryObj.value = data
    }
    // 存储首页地址
    const sethomeJobAvtive = (data: any) => {
      homeJobAvtive.value = data
    }
    // 存储首页地址
    const sethomeCity1 = (data: any) => {
      homeCity1.value = data
    }
    const sethomeCity2 = (data: any) => {
      homeCity2.value = data
    }
    const sethomeCity3 = (data: any) => {
      homeCity3.value = data
    }
    const setmyjobList = (data: any) => {
      if (!data || !Array.isArray(data) || data.length === 0) return

      // 创建现有数据的id集合，用于快速查找
      const existingIds = new Set(myjobFillterList.value.map((item) => item.id))

      // 只添加不重复的新数据
      const newUniqueData = data.filter((item) => {
        // 确保item是对象且有id属性，并且id不存在于现有数据中
        return item && typeof item === 'object' && item.id && !existingIds.has(item.id)
      })

      // 将新的不重复数据添加到去重列表中
      if (newUniqueData.length > 0) {
        myjobFillterList.value.push(...newUniqueData)
        console.log(
          `添加了 ${newUniqueData.length} 条新数据，总数据量: ${myjobFillterList.value.length}`,
        )
      }

      // 同时更新原始数据列表（保持兼容性）
      myjobList.value.push(...data)

      console.log(myjobFillterList.value, 'myjobFillterList.value')
    }

    // 清理岗位列表数据（仅在退出登录等场景使用）
    const clearmyjobList = () => {
      myjobList.value = []
      myjobFillterList.value = []
    }
    return {
      cityObj,
      positionObj,
      jobObj,
      setCity,
      setpositionData,
      setjobArry,
      setindustryObj,
      industryObj,
      homeCity1,
      homeCity2,
      homeCity3,
      sethomeCity1,
      sethomeCity2,
      sethomeCity3,
      homeJobAvtive,
      sethomeJobAvtive,
      myjobList,
      setmyjobList,
      clearmyjobList,
      myjobFillterList,
    }
  },
  {
    persist: true,
  },
)
