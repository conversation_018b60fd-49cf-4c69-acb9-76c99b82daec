import { POST } from '../index'
import { exchangeCodeExchangeDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** C端用户发起交换微信号的请求接口 */
export const exchangeCodeExchange = (
  data: exchangeCodeExchangeDataInt,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/exchangeCode/exchange', data, config)

/** C端用户同意HR发送的交换微信请求处理接口 */
export const exchangeCodeDoExchange = (
  data: Api.IM.CustomMessage.ModifyCustomMessage,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/exchangeCode/doExchange', data, config)
