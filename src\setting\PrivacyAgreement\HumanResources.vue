<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title=""></CustomNavBar>
    </template>

    <view class="protocol-container">
      <!-- 协议头部 -->
      <view class="protocol-header">
        <text class="title">易直聘人力资源协议</text>
        <view class="meta-info">
          <text class="version">版本：ver202505</text>
          <text class="date">生效日期：2025年05月24日</text>
        </view>
      </view>

      <!-- 目录导航 -->
      <view class="content-nav">
        <view class="nav-list">
          <view
            class="nav-item"
            v-for="(item, index) in navItems"
            :key="index"
            @click="scrollToSection(index)"
          >
            <!-- <text class="nav-icon">•</text> -->
            <text class="nav-text">{{ item }}</text>
          </view>
        </view>
      </view>

      <!-- 协议正文 -->
      <view class="article" id="section-0">
        <text class="article-title">一、人力资源机构准入条件</text>
        <view class="clause">
          <text class="clause-title">1.资质要求</text>
          <text class="clause-text">
            1.1需持续保持有效的《人力资源服务许可证》或《劳务派遣许可证》（如涉及派遣业务），并每年向易直聘提交更新核验材料；
          </text>
          <text class="clause-text">
            1.2
            登记机关核定的经营范围需明确包含“职业中介”或“人力资源服务”，且处于有效存续期；如为分支机构，需额外提供总公司授权与资质；如为其他新类型机构，需平台审核同意后方可入驻。
          </text>
          <text class="clause-text">1.3境外猎头公司需额外提供合法经营证明。</text>
        </view>
        <view class="clause">
          <text class="clause-title">2.合规审核</text>
          <text class="clause-text">
            易直聘有权对机构信用记录及风险进行背调，黑名单机构（包括但不限于曾有劳务纠纷、诈骗记录、重大行政处罚、虚假信息披露、严重失信行为等情形，经易直聘独立判断并有权决定是否列入黑名单）禁止入驻。黑名单认定标准与平台公示的最新行业风险数据库挂钩，易直聘有权根据自身风控标准和平台管理需要，制定、调整黑名单认定细则，并以公告或书面通知方式告知合作机构。易直聘有权每12个月对已入驻机构进行资质及合规性复核，发现不符时可暂停或终止合作。平台对黑名单认定有最终解释权，机构如有异议，可在收到通知后3个工作日内提交申诉材料，平台将在合理期限内进行复核并作出决定。
          </text>
        </view>
      </view>
      <view class="article" id="section-1">
        <text class="article-title">二、合作模式与权限</text>
        <view class="clause">
          <text class="clause-title">1.账号权限</text>
          <text class="clause-text">
            1.1人力资源机构账号可发布“外包岗位”或“代招职位”，但需明确、真实、完整地标注雇主企业名称及合作关系。若存在虚假、遗漏、误导性标注，易直聘有权立即下架相关职位、暂停账号、追究违约责任，并视情节严重程度采取进一步措施。同时，因标注瑕疵导致的法律纠纷由人力资源机构独立承担责任。
          </text>
          <text class="clause-text">
            1.2禁止以雇主企业名义直接招聘（需获得雇主书面授权并在平台备案，经过7日审核期）。人力资源机构在发布代招岗位时，须同步上传雇主授权书至平台区块链存证系统备案，并标明人力资源机构身份。对于冒用雇主名义的行为，易直聘有权立即暂停账号，保留进一步追责权利，包括但不限于经济赔偿和法律追究。
          </text>
        </view>
        <view class="clause">
          <text class="clause-title">2.人才推荐规则</text>
          <text class="clause-text">
            推荐的求职者简历需获得其本人明确书面授权（包括但不限于电子签名、短信、邮件等可追溯方式），并由人力资源机构妥善保存授权记录，建立完整的候选人授权文件存档体系。易直聘有权随时查验授权证明，若发现授权证明不实，由机构承担全部法律责任及平台因此遭受的损失。不得买卖、转让简历数据；
          </text>
          <text class="clause-text">
            成功推荐入职后，需按平台规则结算服务费。机构应依法为费用支付开具合规、合法发票，否则平台有权延后结算或暂停相关服务。
          </text>
        </view>
      </view>
      <view class="article" id="section-2">
        <text class="article-title">三、费用与结算</text>
        <view class="clause">
          <text class="clause-title">1.平台服务费</text>
          <text class="clause-text">
            通常按职位发布数量、推荐成功人数或年度合作套餐收费，具体费率以合同约定为准。
          </text>
        </view>
        <view class="clause">
          <text class="clause-title">2.佣金结算</text>
          <text class="clause-text">
            企业与人力资源机构之间的佣金结算由双方自行约定，易直聘不参与分成（除非平台明确提供担保交易服务）。
            因佣金结算产生的任何争议，与易直聘无关，机构、企业应自行协商解决，不得要求平台承担连带责任。
          </text>
        </view>
      </view>
      <view class="article" id="section-3">
        <text class="article-title">四、数据与隐私保护</text>
        <view class="clause">
          <text class="clause-title">1.信息使用限制</text>
          <text class="clause-text">
            1.1仅可将求职者信息用于本次招聘目的，禁止将简历数据用于其他商业用途（如电话营销）。
          </text>
          <text class="clause-text">
            1.2必须遵守《个人信息保护法》，自候选人明确拒绝求职期望或雇佣关系终止之日起15个工作日内彻底删除可识别个人信息，并在离职后30日内删除所有相关信息，保留删除证明备查。
          </text>
        </view>
        <view class="clause">
          <text class="clause-title">2.平台监管</text>
          <text class="clause-text">
            易直聘有权巡查人力资源机构的招聘行为，发现违规可暂停账号并追责。
          </text>
          <text class="clause-text">
            人力资源机构对履职过程中知悉的平台技术数据、运营规则等商业秘密承担永久保密义务，未经平台书面同意不得向任何第三方披露、使用。
          </text>
        </view>
      </view>
      <view class="article" id="section-4">
        <text class="article-title">五、违规与处罚</text>
        <view class="clause">
          <text class="clause-title">1.禁止行为</text>
          <text class="clause-text">1.1虚构岗位、隐瞒真实雇主信息。</text>
          <text class="clause-text">1.2向求职者收取费用（如报名费、培训费）。</text>
          <text class="clause-text">
            1.3未经目标企业书面同意，主动联系该企业近12个月内在职员工并成功促成离职的行为，或明知对方员工存在有效竞业限制协议且机构主动协助违约跳槽的行为，均视为恶意挖角（需遵守竞业限制）。如就“恶意挖角”产生争议，由机构承担举证责任。
          </text>
        </view>
        <view class="clause">
          <text class="clause-title">2.处罚措施</text>
          <text class="clause-text">2.1首次违规：警告并下架相关职位。</text>
          <text class="clause-text">
            2.2累计出现两次（含）以上违规情形：冻结账号余额、终止合作，并可向中国人力资源服务行业协会备案违规记录。冻结账户同时，人力资源机构需在5个工作日内提供完整业务台账备查。被处罚机构如有异议，可在收到处罚通知之日起3个工作日内向易直聘提交书面申诉，平台将在合理期限内进行复核并作出决定。
          </text>
        </view>
      </view>
      <view class="article" id="section-5">
        <text class="article-title">六、协议终止</text>
        <view class="clause">
          <text class="clause-title">1.主动退出</text>
          <text class="clause-text">需提前30天书面通知，并结清所有费用。</text>
        </view>
        <view class="clause">
          <text class="clause-title">2.平台终止权</text>
          <text class="clause-text">
            若机构涉嫌违法或重大违约，易直聘可立即终止服务并保留索赔权利。
          </text>
        </view>
      </view>
      <view class="footer">
        <text class="footer-text">生效日期：2025年05年24日</text>
        <text class="footer-text">版本：ver202505</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const navItems = ref([
  '一、人力资源机构准入条件',
  '二、合作模式与权限',
  '三、费用与结算',
  '四、数据与隐私保护',
  '五、违规与处罚',
  '六、协议终止',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 80rpx;
  left: 15%;
  width: 70%;
  margin: auto;
}

::v-deep .u-button {
  height: 80rpx;
  border-radius: 30rpx;
}

::v-deep .u-button__text {
  font-size: 28rpx !important;
  font-weight: bold;
  color: #fff !important;
}
.protocol-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
}

.protocol-header {
  padding: 20rpx;
  text-align: center;
  border-radius: 12rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.meta-info {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.content-nav {
  padding: 20rpx;
  border-radius: 12rpx;
}

.nav-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.nav-item {
  display: flex;
  align-items: center;
  padding: 10rpx;
  //   width: 50%;
  margin-bottom: 12rpx;
  cursor: pointer;
  border-radius: 8rpx;
}

.nav-icon {
  margin-right: 10rpx;
}

.nav-text {
  font-size: 26rpx;
  color: #007aff;
}

.protocol-content {
  flex: 1;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.clause {
  margin-bottom: 20rpx;
}

.clause-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.clause-text {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}

.action-buttons {
  position: sticky;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}

.confirm-btn,
.cancel-btn {
  width: 48%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.confirm-btn {
  color: #fff;
}

.cancel-btn {
  color: #666;
}
</style>
