import { http } from '@/interceptors/request'
import { HttpRequestConfig } from 'luch-request'

/** POST请求 */
export const POST = <D = any, T = Record<string, any>>(
  url: string,
  data?: T,
  config?: HttpRequestConfig,
) => http.post<D>(url, data, config)
/** POST请求分页 */
export const POSTPaging = <D = any, T = Record<string, any>>(
  url: string,
  data?: T,
  config?: HttpRequestConfig,
) => http.post<Api.Request.IResPagingData<D>>(url, data, config)

/** GET请求 */
export const GET = <D = any>(url: string, config?: HttpRequestConfig) => http.get<D>(url, config)
/** GET请求带body */
export const GET_WITH_BODY = <D = any, T = Record<string, any>>(
  url: string,
  data?: T,
  config?: HttpRequestConfig,
) => http.get<D>(url, { ...config, data })
/** GET请求分页 */
export const GETPaging = <D = any>(url: string, config?: HttpRequestConfig) =>
  http.get<Api.Request.IResPagingData<D>>(url, config)

/** UPLOAD请求 */
export const UPLOAD = <D = any>(url: string, config?: HttpRequestConfig<UniApp.UploadTask>) =>
  http.upload<D>(url, config)
