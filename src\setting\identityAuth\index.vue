<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="身份认证"></CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="flex-c border-b p-b-40rpx p-t-40rpx">
        <view class="text-30rpx m-r-10rpx w-40 font-500">姓名</view>
        <wd-input v-model="userName" no-border placeholder="请输入姓名"></wd-input>
      </view>
      <view class="flex-c border-b p-b-40rpx p-t-40rpx">
        <view class="text-30rpx m-r-10rpx w-40 font-500">身份证号码</view>
        <wd-input v-model="identifyNum" no-border placeholder="请输入身份证号"></wd-input>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed" v-if="!idCard.length">
        <view class="btn_box" @click="submit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { id2MetaVerify } from '@/interPost/home'
import { getUserInfo } from '@/interPost/common'
import { idCardNum } from '@/utils/rule'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { setUserIntel } = useUserInfo()
// 用戶名
const userName = ref('')
// 身份证号
const identifyNum = ref('')
// 身份证号
const idCard = ref('')

onLoad((options) => {
  userName.value = options.trueName ? options.trueName : ''
  identifyNum.value = options.idCard ? options.idCard : ''
  idCard.value = options.idCard ? options.idCard : ''
})
// 更新状态
const userInfoUpdata = async () => {
  const res: any = await getUserInfo()
  if (res.code === 0) {
    setUserIntel(res.data)
    console.log(res.data, '============')
  }
}
const submit = async () => {
  // 用户名
  if (!userName.value) {
    uni.showToast({
      title: '请输入姓名',
      icon: 'none',
    })
    return
  }
  // 身份证
  if (!idCardNum.test(identifyNum.value)) {
    uni.showToast({
      title: '请输入正确的身份证格式',
      icon: 'none',
    })
    return
  }
  const res: any = await id2MetaVerify({ userName: userName.value, identifyNum: identifyNum.value })
  if (res.code === 0) {
    userInfoUpdata()
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  background-color: transparent;
}

.labelName {
  width: 200rpx;
  text-align: left;
}
.position-r {
  position: absolute;
  top: 10rpx;
  right: 0rpx;
}

.pageContaner {
  padding: 0rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
