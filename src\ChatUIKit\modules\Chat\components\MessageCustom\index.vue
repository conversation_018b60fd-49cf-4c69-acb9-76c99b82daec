<template>
  <view
    v-if="menuDisplayed"
    class="fixed inset-0 bg-black/20 z-8"
    :class="[isMenuVisible ? 'animate-fade-in' : 'animate-fade-out']"
    @tap="hideMenu"
    @animationend="handleOverlayAnimationEnd"
  />
  <view
    class="fixed right-0 z-20"
    :class="{
      'transition-all duration-200 ease-out': !isDragging,
      'drag-shadow': isDragging,
    }"
    :style="{
      bottom: currentBottom + 'rpx',
      transform: isDragging ? 'scale(1.05)' : 'scale(1)',
    }"
  >
    <view class="relative">
      <view
        @tap="handleIconTap"
        class="relative z-11 select-none transition-all duration-300"
        :class="{
          'drag-active': isDragging,
        }"
        @touchstart="handleIconTouchStart"
        @touchmove="handleIconTouchMove"
        @touchend="handleIconTouchEnd"
      >
        <wd-icon :name="infoCustomLogo" size="140rpx" />
      </view>
      <view
        v-if="menuDisplayed"
        class="absolute top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-9"
        :class="[isMenuVisible ? 'animate-slide-in' : 'animate-slide-out']"
        @animationend="handleAnimationEnd"
      >
        <view
          v-for="(item, index) in menuUserItems"
          :key="index"
          class="absolute center gap-16rpx w-194rpx h-66rpx text-white bg-black/70 rounded-full menu-item"
          :style="{
            animationDelay: isMenuVisible ? `${index * 0.05 + 0.1}s` : '0s',
            top: `${-(menuUserItems.length - index - menuUserItems.length / 2) * 86}rpx`,
            left: `${-Math.sin((index * Math.PI) / (menuUserItems.length - 1)) * 120 - 100}rpx`,
            transformOrigin: index < menuUserItems.length / 2 ? 'center bottom' : 'center top',
          }"
          @tap="handleAction(item.action)"
        >
          <wd-icon :name="item.icon" size="42rpx" />
          <text class="text-24rpx whitespace-nowrap">{{ item.label }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { autorun } from 'mobx'
import { USER_TYPE, EMIT_EVENT } from '@/enum'
import { getChatToast } from '@/ChatUIKit/hooks/useChatMessage'
import { userGetWxCode } from '@/service/user'
import { unInterestPositionAdd } from '@/service/unInterestPosition'
import { hrUnInterestUserAdd } from '@/service/hrUnInterestUser'
import infoCustomLogo from '@/ChatUIKit/static/info-custom.png'
import resumeImg from '@/ChatUIKit/static/message-custom/resume-white.png'
import wxImg from '@/ChatUIKit/static/message-custom/wx-white.png'
import phoneImg from '@/ChatUIKit/static/message-custom/phone-white.png'
import uninterestedImg from '@/ChatUIKit/static/message-custom/uninterested.png'
import infoInterviewImg from '@/ChatUIKit/static/message-custom/info-interview.png'

interface MenuItem {
  label: string
  action: Api.IM.CustomMessage.ExtType
  icon: string
  permission: USER_TYPE[]
}
const toast = getChatToast()
const { userIntel, userRoleIsBusiness } = useUserInfo()
const {
  sendCustomPhoneMessage,
  sendCustomResumeMessage,
  sendCustomWeChatCodeMessage,
  getIMUserInfo,
  customCardInfo,
  getCoversationInfo,
  deleteConversation,
} = useIMConversation()
const { bool: isMenuVisible, setTrue: showMenu, setFalse: hideMenu } = useBoolean()
const { bool: menuDisplayed, setTrue: showMenuDOM, setFalse: hideMenuDOM } = useBoolean()
const extUserInfo = ref<Api.IM.UserBusinessExtInfo>({})
const menuItems: MenuItem[] = [
  {
    label: '发简历',
    action: 'resume',
    icon: resumeImg,
    permission: [USER_TYPE.APPLICANT, USER_TYPE.HR],
  },
  {
    label: '换微信',
    action: 'exchange_wechat',
    icon: wxImg,
    permission: [USER_TYPE.APPLICANT, USER_TYPE.HR],
  },
  {
    label: '约面试',
    action: 'interview_appointment',
    icon: infoInterviewImg,
    permission: [USER_TYPE.HR],
  },
  {
    label: '换电话',
    action: 'exchange_phone',
    icon: phoneImg,
    permission: [USER_TYPE.APPLICANT, USER_TYPE.HR],
  },
  {
    label: '不感兴趣',
    action: 'uninterested',
    icon: uninterestedImg,
    permission: [USER_TYPE.APPLICANT, USER_TYPE.HR],
  },
]
let cachedBoundaries: { min: number; max: number } | null = null
const currentBottom = ref(660) // 初始位置
const isDragging = ref(false)
const startY = ref(0)
const startBottom = ref(0)
const moveThreshold = 3 // 降低阈值，更容易触发拖拽
const hasMoved = ref(false)

const menuUserItems = computed(() => {
  return menuItems
    .filter((item) => item.permission.includes(userIntel.value.type))
    .map((item) => {
      if (item.action === 'resume' && userRoleIsBusiness.value) {
        return { ...item, label: '要简历' }
      }
      if (item.action === 'uninterested' && userRoleIsBusiness.value) {
        return { ...item, label: '不合适' }
      }
      return item
    })
})
const imCustomUserId = computed(() => {
  return userRoleIsBusiness.value ? extUserInfo.value.cUserId : extUserInfo.value.hrUserId
})
const getBoundaries = () => {
  if (!cachedBoundaries) {
    const systemInfo = uni.getSystemInfoSync()
    const screenHeight = systemInfo.screenHeight * (750 / systemInfo.screenWidth)
    cachedBoundaries = {
      min: 200,
      max: screenHeight - 118,
    }
  }
  return cachedBoundaries
}
const toggleMenu = () => {
  if (!isMenuVisible.value) {
    showMenu()
    showMenuDOM()
  } else {
    hideMenu()
  }
}
const handleAnimationEnd = () => {
  if (!isMenuVisible.value) {
    hideMenuDOM()
  }
}

const handleOverlayAnimationEnd = () => {
  if (!isMenuVisible.value) {
    hideMenuDOM()
  }
}

const handleAction = async (action: Api.IM.CustomMessage.ExtType) => {
  const { conversationId } = getCoversationInfo.value
  if (!conversationId) {
    toast.show('请稍后重试')
    return
  }
  const actionHandlers: Record<Api.IM.CustomMessage.ExtType, () => void> = {
    resume: async () => {
      try {
        await sendCustomResumeMessage(conversationId, {
          id: customCardInfo.value.positionInfoId,
          hxUserInfoVO: {
            userId: imCustomUserId.value,
          },
        })
      } catch {
        if (userRoleIsBusiness.value) return
        uni.$emit(EMIT_EVENT.PROMPT_COMPLETE_RESUME)
      }
    },
    exchange_wechat: async () => {
      let wechatCode: string | null = null
      const { data: wxCode } = await userGetWxCode()
      wechatCode = wxCode
      if (!wechatCode) {
        uni.$emit(EMIT_EVENT.PROMPT_ADD_WECHAT)
        return
      }
      sendCustomWeChatCodeMessage(conversationId, {
        wechatCode,
        userId: imCustomUserId.value,
      })
    },
    exchange_phone: () => {
      sendCustomPhoneMessage(conversationId, imCustomUserId.value)
    },
    interview_appointment: async () => {
      uni.navigateTo({
        url: CommonUtil.buildUrlWithParams('/sub_business/pages/interview/Initiate', {
          positionId: `${customCardInfo.value.positionInfoId}`,
          receiveUserId: `${imCustomUserId.value}`,
          to: `${conversationId}`,
        }),
      })
    },
    uninterested: async () => {
      if (userRoleIsBusiness.value) {
        await hrUnInterestUserAdd({
          userId: imCustomUserId.value,
        })
      } else {
        await unInterestPositionAdd({
          positionId: customCardInfo.value.positionInfoId,
        })
      }
      await deleteConversation(getCoversationInfo.value.conversationId)
      uni.navigateBack()
    },
  }
  const handler = actionHandlers[action] || (() => console.log('未知操作类型:', action))
  hideMenu()
  await CommonUtil.pause(300)
  handler()
}

const handleIconTouchStart = (e: TouchEvent) => {
  startY.value = e.touches[0].clientY
  startBottom.value = currentBottom.value
  hasMoved.value = false
  isDragging.value = false
}

const handleIconTouchMove = (e: TouchEvent) => {
  const currentY = e.touches[0].clientY
  const deltaY = currentY - startY.value

  if (!isDragging.value) {
    if (Math.abs(deltaY) > moveThreshold) {
      isDragging.value = true
      hasMoved.value = true
    } else {
      return
    }
  }

  // 提高灵敏度，直接使用 1:1 的比例
  const boundaries = getBoundaries()
  const newBottom = startBottom.value - deltaY
  currentBottom.value = Math.max(boundaries.min, Math.min(boundaries.max, newBottom))
}

const handleIconTouchEnd = () => {
  if (isDragging.value) {
    isDragging.value = false
    const boundaries = getBoundaries()
    const current = currentBottom.value
    const snapDistance = 30
    if (current - boundaries.min < snapDistance) {
      currentBottom.value = boundaries.min
    } else if (boundaries.max - current < snapDistance) {
      currentBottom.value = boundaries.max
    }
  }
}

const handleIconTap = () => {
  if (!hasMoved.value) {
    toggleMenu()
  }
  nextTick(() => {
    hasMoved.value = false
  })
}
const unwatchUserInfo = autorun(() => {
  const { conversationId, conversationType } = getCoversationInfo.value
  if (conversationType === 'singleChat') {
    const { ext } = getIMUserInfo(conversationId)
    extUserInfo.value = JSON.parse(ext || '{}') as Api.IM.UserBusinessExtInfo
  }
})
onUnmounted(() => {
  unwatchUserInfo()
})
</script>

<style lang="scss" scoped>
@keyframes expand-in {
  from {
    transform-origin: top right;
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform-origin: top right;
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes expand-out {
  from {
    transform-origin: top right;
    transform: scale(1);
    opacity: 1;
  }
  to {
    transform-origin: top right;
    transform: scale(0);
    opacity: 0;
  }
}

.animate-slide-in {
  animation: expand-in 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

.animate-slide-out {
  animation: expand-out 0.3s cubic-bezier(0.6, -0.28, 0.735, 0.045) forwards;
  pointer-events: none;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.menu-item {
  animation: fade-in 0.3s ease forwards;
  opacity: 0;
}

@keyframes fade-in-overlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade-out-overlay {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.animate-fade-in {
  animation: fade-in-overlay 0.2s ease-out forwards;
}

.animate-fade-out {
  animation: fade-out-overlay 0.2s ease-in forwards;
}

.transition-none {
  transition: none !important;
}

.drag-shadow {
  filter: drop-shadow(0 8rpx 16rpx rgba(0, 0, 0, 0.15));
  will-change: transform, filter;
}

.drag-active {
  opacity: 0.9;
  will-change: opacity;
}

.select-none {
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  touch-action: none;
}

.transition-all {
  will-change: transform, bottom;
}
</style>
