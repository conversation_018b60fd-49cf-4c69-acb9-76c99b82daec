<template>
  <wd-popup
    v-model="show"
    position="right"
    custom-style="m-t-100rpx;w-100"
    @close="handleClose"
    safe-area-inset-bottom
  >
    <view class="w-600rpx">
      <z-paging
        ref="pagingRef"
        v-model="pageData"
        @query="queryList"
        :paging-style="pageStyle"
        safe-area-inset-bottom
        :fixed="false"
      >
        <template #top>
          <view class="">
            <view class="c-#333 text-30rpx text-center">我的收藏</view>
          </view>
        </template>
        <!-- <view class="p-20rpx">1111111111111</view> -->
      </z-paging>
    </view>
  </wd-popup>
</template>

<script setup lang="ts">
// 初始化分页
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
    height: '100vh',
    padding: '40rpx',
  },
})
const show = defineModel('show', {
  type: Boolean,
  default: false,
})

function queryList(page: number, size: number) {
  pageSetInfo(page, size)
}

const handleClose = () => {
  show.value = false
}
</script>

<style lang="scss" scoped></style>
