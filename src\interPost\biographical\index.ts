import { POST } from '@/service'
// 用户首次登录选择身份时更新接口
export const updateChooseType = (data: any) => {
  return POST('/easyzhipin-api/user/updateChooseType', data)
}

// 添加我的个人信息
export const userResumeBaseInfo = (data: any) => {
  return POST('/easyzhipin-api/resumeBaseInfo/addAndUpdate', data)
}

// 更新我的个人信息
export const updateBaseInfo = (data: any) => {
  return POST('/easyzhipin-api/resumeBaseInfo/update', data)
}

// 获取
export const resumeBaseInfoList = (data: any) => {
  return POST('/easyzhipin-api/resumeBaseInfo/queryById', data)
}

// 添加求职期望
export const resumeJobIntention = (data: any) => {
  return POST('/easyzhipin-api/userJobIntention/addAndUpdate', data)
}
