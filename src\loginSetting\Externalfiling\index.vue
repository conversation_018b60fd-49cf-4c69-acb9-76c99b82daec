<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="ICP/IP地址" class="base_header"></CustomNavBar>
    </template>
    <web-view :src="url" :fullscreen="false"></web-view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const url = ref(null)
onLoad((options) => {
  url.value = decodeURIComponent(options.url)
})
onMounted(() => {
  const instance = getCurrentInstance()
  const query = uni.createSelectorQuery().in(instance)
  const { windowHeight } = uni.getSystemInfoSync() // 屏幕高度（单位：px）
  console.log('屏幕高度:', windowHeight)
  if (instance && instance.proxy) {
    const currentWebview = instance.proxy.$scope?.$getAppWebview()
    if (currentWebview) {
      nextTick(() => {
        setTimeout(() => {
          const closeHeight = 0
          let baseHeaderHeight = 0

          query
            .select('.base_header')
            .boundingClientRect((res) => {
              if (res && res.height) {
                baseHeaderHeight = res.height
              } else {
                baseHeaderHeight = 50 // 默认高度
              }
            })
            .exec(() => {
              const totalTop = closeHeight + baseHeaderHeight
              console.log('Calculated totalTop:', totalTop)

              const wv = currentWebview.children()?.[0]
              if (wv) {
                wv.setStyle({
                  top: `${totalTop}px`,
                  height: `${windowHeight - totalTop + 30}px`,
                  zIndex: -1,
                })
              }
            })
        }, 300)
      })
    }
  }
})
</script>

<style lang="scss"></style>
