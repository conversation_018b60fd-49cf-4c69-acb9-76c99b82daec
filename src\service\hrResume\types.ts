import { Gender, SeekStatus } from '@/enum'

export interface hrResumeResumeUserSeniorListDataInt {
  /** 年龄起始 */
  ageBegin?: number
  /** 年龄截止 */
  ageEnd?: number
  /** 性别1男2女 */
  gender?: Gender
  /** HR当前所选的岗位id */
  positionInfoId: number
  /** 学历等级 */
  qualification?: number
  /** 期望薪资截止(元)，选不限时别送 */
  salaryExpectationEnd?: number
  /** 期望薪资开始(元),选不限时别送 */
  salaryExpectationStart?: number
  /** 查询类型0推荐1最新 */
  searchType?: number
  /** 求职状态0离职找工作1在职找工作2不找工作 */
  seekStatus?: SeekStatus
}
export interface WorkExperiencesListInt {
  /** 公司名称 */
  company?: string
  /** 所属部门 */
  department?: string
  /** 当前工作的工资 */
  money?: number
  /** 岗位名称 */
  positionName?: string
  /** 工作年限 */
  workYear?: number
}
export interface hrResumeSeniorPostModelInt
  extends Omit<hrResumeResumeUserSeniorListDataInt, 'positionInfoId'> {}

export interface hrResumeQueryIMCardInfoByIdDataInt {
  userId: number
}
export interface hrResumeQueryIMCardInfoByIdInt {
  /** 第四块信息，学校学历 */
  infoFour?: string
  /** 第一块信息，岗位 */
  infoOne?: string
  /** 第三块信息，年龄年限 */
  infoThree?: string
  /** 第二块信息，期望薪资 */
  infoTwo?: string
  /** 岗位id */
  positionId?: number
}

export interface hrResumeQuickCardInfoDataInt {
  userId: number
}
export interface hrResumeQuickCardInfoInt
  extends Pick<
    hrResumeResumeUserSeniorListDataInt,
    'gender' | 'qualification' | 'salaryExpectationEnd' | 'salaryExpectationStart' | 'seekStatus'
  > {
  /** 年龄 */
  age?: number
  /** 用户头像url */
  headImgUrl?: string
  /** 所学专业 */
  major?: string
  /** 我的亮点 */
  myLights?: string
  /** 期望职位名称 */
  positionName?: string
  /** 学校名称 */
  school?: string
  /** 真实名字 */
  trueName?: string
  /** 工作经历 */
  workExperiencesList?: WorkExperiencesListInt[]
  /** 工作经验 */
  workYear?: number
  /** 学历标签 */
  qualificationLabel?: string
  /** 求职状态标签 */
  seekStatusLabel?: string
  /** 用户id */
  cUserId?: number
}
