<template>
  <wd-config-provider :themeVars="themeVars">
    <view class="mt-60rpx px-66rpx flex flex-col gap-106rpx">
      <view class="flex flex-col gap-38rpx">
        <view class="flex items-center gap-26rpx">
          <wd-input
            type="text"
            placeholder="自定义标签"
            v-model="labelCustomVal"
            no-border
            @confirm="handleAddCustomLabel"
          />
          <view
            class="center size-90rpx bg-[#5378FF] shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx"
            @click="handleAddCustomLabel"
          >
            <wd-icon name="add" size="26rpx" color="#ffffff" />
          </view>
        </view>
        <view class="flex items-center flex-wrap gap-30rpx" v-if="labelActiveList.length">
          <view
            class="center gap-10rpx h-56rpx px-10rpx rounded-10rpx border-1px border-dashed border-[#5378FF]"
            v-for="(item, key) in labelActiveList"
            :key="`label-active-${key}`"
          >
            <text class="c-#5378FF text-24rpx font-400">{{ item }}</text>
            <wd-icon
              name="close-circle"
              size="24rpx"
              color="#5378FF"
              @click="handleDeleteLabel(key)"
            />
          </view>
        </view>
      </view>
      <view class="flex flex-col gap-38rpx" v-if="labelList.length">
        <text class="c-#333333 font-bold text-40rpx">可选标签</text>
        <view class="flex items-center flex-wrap gap-30rpx">
          <view
            class="center h-56rpx px-10rpx rounded-10rpx border-1px border-dashed border-[#888888]"
            v-for="(item, key) in labelList.filter((label) => !labelActiveList.includes(label))"
            :key="`label-${key}`"
            @click="handleSelectLabel(item)"
          >
            <text class="c-#888888 text-24rpx font-400">{{ item }}</text>
          </view>
        </view>
      </view>
    </view>
  </wd-config-provider>
</template>

<script lang="ts" setup>
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { ReleasePositionInfo } from '@/sub_business/types/release'
import { commonDataPositionKeyword } from '@/service/commonData'

const props = defineProps<{
  type: ReleasePositionInfo
}>()

const { releasePostModel, releasePositionKey } = useReleasePost()

const themeVars: ConfigProviderThemeVars = {
  inputInnerHeightNoBorder: '90rpx',
}
const salaryArray = [
  '年终奖',
  '定期团建',
  '零食下午茶',
  '节日福利',
  '生日福利',
  '五险一金',
  '带薪年假',
  '员工食堂',
  '定期体检',
  '交通补贴',
  '通讯补贴',
  '餐饮补贴',
  '住房补贴',
  '免费工作餐',
  '提供住宿',
  '免费班车',
  '岗位晋升',
  '年度调薪',
  '高温补贴',
  '弹性工作',
]
const labelCustomVal = ref('')
const labelActiveList = ref<string[]>([])
const labelList = ref<string[]>([])
function handleSelectLabel(label: string) {
  if (!labelActiveList.value.includes(label)) {
    labelActiveList.value.push(label)
  }
}
function handleDeleteLabel(key: number) {
  labelActiveList.value.splice(key, 1)
}
function handleAddCustomLabel() {
  if (labelCustomVal.value && !labelActiveList.value.includes(labelCustomVal.value)) {
    labelActiveList.value.push(labelCustomVal.value)
    labelCustomVal.value = ''
  }
}
const updateLabelList = async () => {
  switch (props.type) {
    case ReleasePositionInfo.SALARY:
      labelList.value = salaryArray
      labelActiveList.value = releasePostModel.value.positionBenefitList || []
      break
    case ReleasePositionInfo.KEYWORDS:
      if (releasePositionKey.value) {
        try {
          const { data } = await commonDataPositionKeyword(
            {
              searchKey: releasePositionKey.value,
            },
            {
              custom: {
                catch: true,
              },
            },
          )
          labelList.value = data
        } catch (error) {
          labelList.value = []
        }
      }
      labelActiveList.value = releasePostModel.value.positionKeyList || []
      break
    default:
      labelList.value = []
  }
}
const handleConfirm = () => {
  switch (props.type) {
    case ReleasePositionInfo.SALARY:
      releasePostModel.value.positionBenefitList = [...labelActiveList.value]
      break
    case ReleasePositionInfo.KEYWORDS:
      releasePostModel.value.positionKeyList = [...labelActiveList.value]
      break
  }
}

watch(() => props.type, updateLabelList, { immediate: true })

defineExpose({
  submitData: handleConfirm,
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  flex: 1;
  padding: 0 22rpx;
  border-radius: 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
}
</style>
