import { POST } from '@/service'
// 学校
export const highSchoolData = (data: any) => {
  return POST('/easyzhipin-api/commonData/highSchoolDataList', data)
}
// 公司
export const companyOptionList = (data: any) => {
  return POST('/easyzhipin-api/commonData/companyOptionList', data)
}
// 切换登录C切B接口
export const changeToBAuth = () => {
  return POST('/easyzhipin-api/login/changeToBAuth')
}
// 切换登录B切C接口
export const changeToCAuth = () => {
  return POST('/easyzhipin-api/login/changeToCAuth')
}
// 字典
export const queryById = (data: any) => {
  return POST('/easyzhipin-api/dictionary/queryById', data)
}
// 坐标换地址
export const geographicToAddress = (data: any) => {
  return POST('/easyzhipin-api/map/geographicToAddress', data)
}
//
export const addressToGeographic = (data: any) => {
  return POST('/easyzhipin-api/map/addressToGeographic', data)
}
// 查询最新的sessionUser信息接口

export const getUserInfo = () => {
  return POST('/easyzhipin-api/user/getUserInfo')
}
