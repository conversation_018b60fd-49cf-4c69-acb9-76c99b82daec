<template>
  <view class="pdf-upload-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <view class="left" @click="handleBack">
          <text class="back-icon">←</text>
        </view>
        <view class="title">PDF上传</view>
        <view class="right"></view>
      </view>
    </view>

    <!-- WebView容器 -->
    <view class="webview-container">
      <web-view
        :src="webviewUrl"
        @message="handleWebViewMessage"
        @error="handleWebViewError"
        @load="handleWebViewLoad"
        class="pdf-webview"
        :webview-styles="{
          progress: {
            color: '#007aff',
          },
        }"
      />
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-content">
        <text class="loading-text">正在加载PDF上传页面...</text>
      </view>
    </view>

    <!-- 错误提示 -->
    <view v-if="error" class="error-overlay">
      <view class="error-content">
        <text class="error-text">{{ error }}</text>
        <button @click="retryLoad" class="retry-btn">重试</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { usePdfUpload } from './usePdfUpload'

// 响应式数据
const loading = ref(true)
const error = ref('')
const webviewUrl = ref('')

// 使用PDF上传逻辑
const {
  initWebView,
  handleWebViewMessage,
  handleWebViewError,
  handleWebViewLoad,
  retryLoad,
  cleanup,
  setUploadConfig,
} = usePdfUpload({
  onLoadingChange: (isLoading) => {
    loading.value = isLoading
  },
  onErrorChange: (errorMsg) => {
    error.value = errorMsg
  },
  onUrlChange: (url) => {
    webviewUrl.value = url
  },
})

// 返回处理
const handleBack = () => {
  uni.navigateBack()
}

// 生命周期
onMounted(() => {
  initWebView()

  // 监听配置
  uni.$on('pdfUploadConfig', (config: any) => {
    console.log('收到PDF上传配置:', config)
    setUploadConfig(config)
  })
})

onUnmounted(() => {
  cleanup()
  // 移除监听器
  uni.$off('pdfUploadConfig')
})
</script>

<style lang="scss" scoped>
.pdf-upload-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.custom-navbar {
  z-index: 1000;
  background-color: white;
  border-bottom: 1px solid #e5e5e5;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 10px 15px;
}

.left {
  display: flex;
  align-items: center;
  width: 60px;
}

.back-icon {
  font-size: 20px;
  color: #007aff;
}

.title {
  flex: 1;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  text-align: center;
}

.right {
  width: 60px;
}

.webview-container {
  position: relative;
  flex: 1;
}

.pdf-webview {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.error-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.95);
}

.error-content {
  padding: 20px;
  text-align: center;
}

.error-text {
  display: block;
  margin-bottom: 20px;
  font-size: 16px;
  color: #ff4757;
}

.retry-btn {
  padding: 10px 20px;
  font-size: 14px;
  color: white;
  background-color: #007aff;
  border: none;
  border-radius: 6px;
}
</style>
