<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
    'app-plus': {
      softinputMode: 'adjustResize',
    },
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="公司介绍">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-textarea
        :auto-focus="true"
        clear-trigger="focus"
        :adjust-position="false"
        v-model="profile"
        :maxlength="1000"
        clearable
        show-word-limit
        placeholder="请填写公司介绍"
        custom-class="custom-class"
        custom-textarea-container-class="custom-textarea-container-class"
        custom-textarea-class="custom-textarea-class"
      />
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="addSubmit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { updateProfile } from '@/service/hrCompany'
import { useMessage } from 'wot-design-uni'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const profile = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
// id
onLoad(async (options) => {
  await nextTick()
  profile.value = options.profile
  initNane.value = options.profile
})
// 完成
const addSubmit = async () => {
  const res: any = await updateProfile({ profile: profile.value })
  if (res.code === 0) {
    uni.navigateBack({
      delta: 1,
      success() {
        uni.$emit('welfare-updated')
      },
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 返回
const back = () => {
  if (profile.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
.corporateName {
  padding: 40rpx 40rpx;
}
::v-deep .wd-textarea__placeholder {
  font-size: 26rpx;
}
::v-deep .custom-class {
  border-radius: 20rpx !important;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
::v-deep .custom-textarea-container-class {
  height: calc(100vh - 400rpx);
}
::v-deep .wd-textarea__inner {
  height: calc(100vh - 480rpx);
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
