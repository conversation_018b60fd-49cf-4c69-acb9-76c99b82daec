<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="专业">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view @click="submit">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-input no-border v-model="major" placeholder="请输入专业名称" focus />
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
import { useMessage } from 'wot-design-uni'
// 公司
const resumeStore = useResumeStore()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const major = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
onLoad(async (options) => {
  await nextTick()
  major.value = options.major
  initNane.value = options.major
})
const submit = () => {
  resumeStore.setMajor(major.value)
  uni.navigateBack()
}
// 返回
const back = () => {
  if (major.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        resumeStore.setMajor(initNane.value)
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}
.corporateName {
  padding-bottom: 40rpx;
  margin: 40rpx 40rpx;
  border-bottom: 1rpx solid #c0bfbf;
}
</style>
