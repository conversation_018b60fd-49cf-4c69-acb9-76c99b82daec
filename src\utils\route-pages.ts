import { pages, subPackages } from '@/pages.json'

export const getLastPage = () => {
  const pages = getCurrentPages()
  return pages[pages.length - 1]
}

/** 获取当前页面路由的 path 路径和 redirectPath 路径 */
export const currRoute = () => {
  const lastPage = getLastPage()
  const currRoute = (lastPage as any).$page
  const { fullPath } = currRoute as { fullPath: string }
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/** 解析 url 得到 path 和 query */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  if (!queryStr) {
    return {
      path,
      query: {},
    }
  }
  const query: Record<string, string> = {}
  queryStr.split('&').forEach((item) => {
    const [key, value] = item.split('=')
    query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
  })
  return { path, query }
}
/** 得到所有的需要登录的 pages，包括主包和分包的 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const mainPages = [
    ...pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  subPackages.forEach((subPageObj) => {
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...mainPages, ...subPages]
  return result
}

/** 得到所有的需要登录的 pages，包括主包和分包的 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/** 得到所有的需要登录的 pages，包括主包和分包的 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)
