<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="paging" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar />
    </template>
    <view class="protocol-content"></view>
    <template #bottom>
      <view class="flex justify-center items-center p-t-40rpx" v-if="isAgree">
        <view
          class="w-300rpx bg-#ff0000 text-center py-20rpx rounded-60rpx c-#fff m-r-40rpx"
          @click="handleClose"
        >
          拒绝
        </view>
        <view
          @click="handleAgree"
          class="w-300rpx bg-#4d8fff text-center py-20rpx rounded-60rpx c-#fff"
        >
          同意
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
</script>

<style lang="scss" scoped></style>
