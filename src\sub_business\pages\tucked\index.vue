<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="被收藏"></CustomNavBar>
    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    >
      <view class="py-40rpx flex flex-col">
        <mylist
          v-for="(item, index) in pageData"
          :key="index"
          :list="item"
          :position="{}"
          class="border-b border-#E0E0E0 border-b-solid"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { beenCollectList } from '@/service/hrBiographical'
import mylist from '@/sub_business/components/mylist.vue'
import { getCustomBar } from '@/utils/storage'
const customBar = ref(null)
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    padding: '0rpx 0rpx 0rpx',
    marginTop: '40rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
  },
})
const list = ref({})
const params = reactive({
  entity: {},
  orderBy: {},
})

const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  const res: any = await beenCollectList({
    ...params,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  console.log(res, 'res====================hahah')
  if (res.code === 0) {
    pagingRef.value.complete(res.data?.list)
  }
}

onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped></style>
