<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <component :is="useComponent.component" ref="componentRef" :key="`home-${useComponent.type}`" />
</template>

<script setup lang="ts">
import { USER_TYPE } from '@/enum'
import business from './module/business.vue'
import personal from './module/personal.vue'

interface UseComponent {
  type: USER_TYPE
  component: Component
}

defineOptions({
  name: 'Home',
})

const { userIntel } = useUserInfo()
const componentRef = ref<InstanceType<typeof personal> | InstanceType<typeof business>>()
const userRoleComponents: UseComponent[] = [
  {
    type: USER_TYPE.APPLICANT,
    component: personal,
  },
  {
    type: USER_TYPE.HR,
    component: business,
  },
]
const useComponent = computed(() =>
  userRoleComponents.find((item) => item.type === (userIntel.value?.type ?? USER_TYPE.APPLICANT)),
)

onShow(async () => {
  try {
    await uni.$onLaunched
    await nextTick()
    componentRef.value?.homeShow()
  } catch (error) {
    console.error('Error during home show:', error)
  }
})
uni.hideTabBar()
</script>

<style lang="scss" scoped></style>
