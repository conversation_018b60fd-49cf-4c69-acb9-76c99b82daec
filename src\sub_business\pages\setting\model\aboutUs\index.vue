<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="关于我们"></CustomNavBar>
    <view class="resignation-wrap">
      <image src="@/static/images/login/logo.png" mode="widthFix" class="resignation-img" />
      <view>
        <view class="resignation-text">易直聘</view>
        <view class="resignation-text-sub" v-if="versionInfo.versionName">
          V{{ versionInfo.versionName }}
        </view>
      </view>

      <view class="content-text">
        <span class="content-text-item">
          易直聘（重庆中誉易职网络信息技术有限公司）是面向创新型企业的人力资源服务平台，人工智能、AI技术、云数据等技术驱动与“以人为本”的用户服务理念的融合，持续引领招聘行业变革。
        </span>
      </view>
      <view class="setting">
        <view class="setting-list flex-between border-b">
          <view class="list-item-text text-32rpx">公司网址</view>
          <view class="flex items-center">
            <view class="text-26rpx c-#888">www.easyzhipin.com</view>
          </view>
        </view>
        <view class="setting-list border-b flex-between">
          <view class="list-item-text text-32rpx">热线服务</view>
          <view class="text-26rpx c-#888">400-965-9675</view>
        </view>
        <view class="setting-list border-b flex-between" @tap="goQualifications">
          <view class="list-item-text text-32rpx">资质公示</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>

        <view class="setting-list border-b flex-between" @tap="goUserServiceProtocol">
          <view class="list-item-text text-32rpx">用户服务协议</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="copyright">
        <span class="copyright-text">&copy; 易直聘版权所有</span>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { usePhoneVersion } from '@/hooks/common/usePhoneInfo'
const { userRoleIsBusiness } = useUserInfo()
const { getAppVersion } = usePhoneVersion()
const versionInfo = ref<{ versionName?: string; versionCode?: string }>({})
onLoad(async () => {
  versionInfo.value = await getAppVersion()
})
const goUserServiceProtocol = () => {
  console.log(userRoleIsBusiness.value, 'userRoleIsBusiness.value==========')
  if (userRoleIsBusiness.value) {
    uni.navigateTo({
      url: '/sub_business/pages/setting/model/PrivacyAgreement',
    })
  } else {
    uni.navigateTo({
      url: '/setting/PrivacyAgreement/index',
    })
  }
}
const goQualifications = () => {
  uni.navigateTo({
    url: '/sub_business/pages/setting/model/qualifications/index',
  })
}
</script>

<style lang="scss" scoped>
.resignation-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 80rpx;

  .resignation-img {
    width: 200rpx;
    height: 200rpx;
  }
  .resignation-text {
    margin-top: 30rpx;
    font-size: 32rpx;
    color: #000000;
    text-align: center;
  }
  .resignation-text-sub {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #333333;
    text-align: center;
  }
  .content-text {
    width: 85%;
    padding: 20rpx 0;
    margin-top: 30rpx;
    font-size: 28rpx;
    color: #333333;
    // border: 1px solid #333333;
    border-radius: 14rpx 14rpx 0 0;
    .content-text-item {
      display: block;
      margin-bottom: 10rpx;
    }
  }
  .setting {
    width: 100%;
    padding: 0rpx 40rpx;
    .setting-list {
      padding: 30rpx 20rpx;
      .list-item-text {
        color: #333;
      }
    }
  }
  .copyright {
    padding-top: 80rpx;
    text-align: center;
    .copyright-text {
      font-size: 24rpx;
      color: #9e9e9e;
    }
  }
}
</style>
