<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘企业端用户规则</text>
        <text class="subtitle">版本：ver202505 生效日期：2025年05年24日</text>
      </view>
      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-text">{{ item }}</text>
        </view>
      </view>
      <view class="content">
        <view class="section" v-for="(section, idx) in sections" :id="`section-${idx}`" :key="idx">
          <text class="section-title">{{ section.title }}</text>
          <view v-for="(p, pidx) in section.paragraphs" :key="pidx">
            <text class="paragraph">{{ p }}</text>
          </view>
        </view>
        <view class="footer">
          <text class="footer-text">生效日期：2025年05年24日</text>
          <text class="footer-text">版本：ver202505</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const pagingRef = ref(null)
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const contentList = ref([
  '一、注册与认证规则',
  '二、职位发布与管理规则',
  '三、人才沟通与互动规则',
  '四、隐私与数据安全',
  '五、违规处理与争议解决',
  '六、费用与服务说明',
  '七、免责条款',
])

const sections = ref([
  {
    title: '一、注册与认证规则',
    paragraphs: [
      '1.企业资质要求',
      '1.1仅限合法注册的企业、个体工商户或机构使用，需提供加盖公章的营业执照复印件、法定代表人身份证复印件等经平台认可的有效证明文件；',
      '1.2教育机构需提供办学许可证，人力资源服务机构需提供劳务派遣许可证等行业资质；',
      '1.3境外企业需提供当地注册证明、授权人身份证明及其他能证明其合法主体资格和授权关系的文件。所有材料须为原件或经公证、认证的复印件，并按易直聘平台要求提供中文翻译件。平台有权根据相关法律法规及业务需要，要求企业补充、更新或重新认证相关资质材料。',
      '2.认证流程',
      '2.1提交资料后，平台将在1-3个工作日内完成人工审核；但对于特殊行业或存疑资料，审核周期可延长至5个工作日,未通过需重新提交或补充材料。平台仅对表面材料的真实性进行形式审查，对于企业提供信息的真实性、合法性、准确性不承担实质审核或担保责任,如因企业提供虚假材料导致的任何纠纷或损失，平台不承担责任。',
      '2.2企业信息变更（如名称、法人、地址）需在30日内更新资料并重新认证。未按要求更新的，平台有权暂停或终止相关账号服务，由此产生的法律责任及后果由企业自行承担。',
      '3.账号权限',
      '3.1主账号由企业管理员持有，可创建子账号并分配权限（如发布职位、查看简历、沟通等）；',
      '3.2子账号需绑定员工真实姓名及联系方式，禁止共享账号。子账号操作视为企业官方行为，企业需承担连带责任。企业应确保子账号信息真实、唯一，如因账号共享或信息不实导致的法律责任及损失，由企业自行承担。',
    ],
  },
  {
    title: '二、职位发布与管理规则',
    paragraphs: [
      '1.发布权限',
      '仅认证企业可发布职位，可选择基础岗位和高级岗位发布；职位需明确分类（全职/兼职/实习）、薪资范围（禁止“面议”）、工作地点及职责描述。',
      '2.职位要求',
      '2.1禁止虚假信息、薪资误导、歧视性要求（如性别、年龄、地域）；',
      '2.2不得发布兼职刷单、传销、色情等违法岗位；',
      '2.3薪资范围不得超过实际标准的30%浮动；',
      '2.4职位需通过系统审核，违规内容将被驳回或下架。',
      '3.更新与编辑',
      '用户可以更新已发布职位，更新后职位原有权益不变；但因系统升级导致的展示形式调整不视为权益变更。',
      '用户在更新已发布职位时，不得随意修改核心信息（包括但不限于薪资、岗位类型）。如确需修改核心信息，须提交修改申请并接受平台重新审核。在重新审核期间，相关职位将自动暂停展示，审核结果可能导致职位被驳回。平台有权根据实际情况制定并调整审核标准，企业应积极配合相关审核流程。',
    ],
  },
  {
    title: '三、人才沟通与互动规则',
    paragraphs: [
      '1.主动沟通限制',
      '禁止发送骚扰、广告、虚假承诺等内容。对于上述沟通违规行为，平台有权根据情节轻重，采取包括但不限于警告、限制沟通功能、暂时停用或封禁账号等措施。平台有权对违规行为进行调查核实，并结合证据和违规情节独立决定处理方式。',
      '2.面试邀约',
      '2.1需明确面试时间、地点、联系人及方式。',
      '2.2如求职者投诉企业面试流程不符，须提交包括但不限于书面邀约记录、通话录音等客观证据，平台将根据实质性证据进行核查。经查证属实的，平台有权视情节轻重对企业账号采取包括但不限于警告、岗位下架等处理措施。',
      '3.人才库管理',
      '3.1可收藏求职者简历，禁止批量导出或外传。若发现企业有批量导出、外传等行为，平台有权立即采取封禁、追责等措施，并保留追究法律责任的权利。',
      '3.2人才标签需合规使用，禁止添加侮辱性、隐私性备注（如“已婚未育”）。如有违反，平台有权立即删除相关内容，并对企业账号采取限制、封禁等措施。',
    ],
  },
  {
    title: '四、隐私与数据安全',
    paragraphs: [
      '1.信息保密',
      '1.1企业不得泄露求职者联系方式、身份证号等敏感信息。若企业违反本规定，平台有权立即对其账号采取永久封禁措施，并按每条泄露信息处以人民币500元违约金；如因此造成的实际损失高于上述金额的，企业应按照实际损失全额赔偿，并承担相应法律责任。平台有权在发现数据泄露风险时，及时介入调查并配合有关监管部门处理。',
      '1.2简历下载需获得求职者授权同意并承诺不得对外传播。',
      '2.数据使用',
      '2.1企业仅可将平台数据用于招聘目的，禁止用于商业推广、倒卖等行为。平台有权通过技术手段对企业数据使用行为进行监控、核查，发现异常可采取相应措施。',
      '2.2账号注销后，企业需在7日内删除本地存储的求职者数据，并通过平台后台系统工具验证删除结果。平台有权要求企业提供数据删除证明或相关操作记录，并进行抽查，企业应予以配合。',
    ],
  },
  {
    title: '五、违规处理与争议解决',
    paragraphs: [
      '1.违规行为界定',
      '1.1包括但不限于：虚假认证、职位欺诈、骚扰求职者、恶意竞争、数据滥用，以及其他违反《网络安全法》《个人信息保护法》等强制性规定的行为。平台有权根据违规行为的性质和影响程度，量化处罚标准并动态调整。',
      '1.2违规处罚分等级：提醒、警告、限制功能（如冻结沟通权限）、暂停账号或永久封禁。',
      '2.申诉机制',
      '企业对处罚有异议，可在3个工作日内通过客服提交申诉材料，平台将在合理期限内进行复核并作出决定。',
    ],
  },
  {
    title: '六、费用与服务说明',
    paragraphs: [
      '1.付费服务',
      '1.1发布岗位、增值服务（职位置顶、急聘标识等）需单次付费。',
      '1.2费用支付后不支持退款（因平台故障除外）。增值服务效果受市场因素影响，企业用户不得以招聘效果未达预期主张费用返还。平台故障指因易直聘系统自身原因导致服务无法正常使用，且经平台确认的情形，不包括因企业自身网络、设备等原因造成的服务中断。服务有效期届满未使用视为自动放弃。',
      '2.发票与合同',
      '支付完成后可申请电子发票，申请人须提供注册账户对应的发票信息。因企业违约产生的违约金、扣费等不在开票范围内。',
    ],
  },
  {
    title: '七、免责条款',
    paragraphs: [
      '企业需对自身发布的信息真实性负责，易直聘不承担因企业用户发布信息不实引发的任何投诉、索赔及法律后果，同时，平台有权在接到司法或监管机构要求时，配合调查、提供相关信息。',
      '因系统维护、网络故障、自然灾害、政府行为、电力故障等导致服务中断，易直聘不承担损失，但需提前24小时公告。',
    ],
  },
])

const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`
  const query = uni.createSelectorQuery().in(pagingRef.value)
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150
            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }
            const targetScrollTop = targetRect.top - offsetTop
            if (pagingRef.value) {
              try {
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.content-list {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
}

.list-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.list-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;
  .list-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
</style>
