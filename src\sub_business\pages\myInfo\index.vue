<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img-h">
    <CustomNavBar title="个人信息" />
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="mainText labelName">头像</view>
        <view>
          <view
            class="text-32rpx font-w-500 flex-1 text-r flex justify-right items-center flex-col"
          >
            <wd-upload
              v-model:file-list="fileList"
              :action="baseUrl"
              :before-upload="beforeUpload"
              :header="header"
              :limit="1"
              accept="image"
              custom-class="custom-class-img"
              image-mode="aspectFill"
              reupload
              @success="successFun"
            ></wd-upload>
          </view>
          <view v-if="myInfo.headImgStatus === 0" class="c-#888 text-24rpx text-center">
            审核中
          </view>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">姓名</view>
        <view class="text-32rpx font-w-500 flex-1 text-r">
          {{ myInfo.trueName }}
        </view>
      </view>
      <view class="form-list">
        <view class="mainText labelName">当前公司</view>
        <view class="text-32rpx font-w-500 flex-1 text-r p-t-10rpx">
          {{ myInfo.companyName }}
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">当前岗位</view>
        <view class="text-32rpx font-w-500 flex-1 text-r">
          {{ myInfo.hrPosition }}
        </view>
      </view>

      <view class="form-list">
        <view class="flex-between">
          <view class="mainText labelName">手机号</view>
          <view v-if="myInfo.wxCode" class="text-32rpx font-w-500 flex-1 text-r">
            {{ myInfo.phone }}
          </view>
          <view v-else class="text-32rpx font-w-500 flex-1 text-r activeColor">去完善</view>
        </view>
        <view class="flex-c p-t-10rpx">
          <wd-icon color="#999999" name="info-circle" size="14px"></wd-icon>
          <view class="subText">手机号只有在你与求职者交换时候，才会告知对方</view>
        </view>
      </view>

      <view class="form-list">
        <view class="flex-between">
          <view class="mainText labelName">微信号</view>
          <view
            v-if="myInfo.wxCode"
            class="text-32rpx font-w-500 flex-1 text-r"
            @click="gohrWxCode"
          >
            {{ myInfo.wxCode }}
          </view>
          <view v-else class="text-32rpx font-w-500 flex-1 text-r activeColor" @click="gohrWxCode">
            去完善
          </view>
        </view>
        <view class="flex-c p-t-10rpx">
          <wd-icon color="#999999" name="info-circle" size="14px"></wd-icon>
          <view class="subText">微信号只有在您与求职者交换时候，才会告知对方</view>
        </view>
      </view>

      <view class="form-list">
        <view class="flex-between">
          <view class="mainText labelName">在职证明</view>
          <view
            v-if="hrExamineStateObj.status === 1 && hrExamineStateObj.auditStatus === 1"
            class="text-32rpx font-w-500 flex-1 text-r"
            @click="gohrWxCode"
          >
            已通过
          </view>
          <view
            v-else-if="hrExamineStateObj.status === 0 && hrExamineStateObj.auditStatus === 2"
            class="text-32rpx font-w-500 flex-1 text-r activeColor"
            @click="goExamine"
          >
            去完善
          </view>
          <view v-else class="text-32rpx font-w-500 flex-1 text-r activeColor">审核中</view>
        </view>

        <view class="flex-c p-t-10rpx">
          <wd-icon color="#999999" name="info-circle" size="14px"></wd-icon>
          <view class="subText">未通过审核您将无法发布职位</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { userImageAudit } from '@/interPost/my'
import { hrUserInfo } from '@/service/hrUser'
import { quickHandlers } from '@/utils/compressImage'
const { hrExamineStateObj } = useUserInfo()

// 定义用户信息类型
interface UserInfo {
  gender?: number
  headImgStatus?: number
  phone?: string
  trueName?: string
  companyName?: string
  hrPosition?: string
  headImgUrl?: string
  wxCode?: string
}

const { getToken } = useUserInfo()
const fileList = ref([])
const myInfo = ref<UserInfo>({})
// 部门
const hrPosition = ref('')
// 图片地址/attachment/uploadImgThum
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// 微信号
const gohrWxCode = () => {
  uni.navigateTo({
    url: `/setting/wxUpdata/index?wxCode=${myInfo.value.wxCode}`,
  })
}
// 在职证明
const goExamine = () => {
  uni.navigateTo({
    url: '/loginSetting/companyJoin/jobCertificate?source=myInfo',
  })
}
// before-upload 处理函数 - 使用预设的头像压缩配置
const beforeUpload = quickHandlers.avatar()

// 图片上传成功
const successFun = ({ fileList }) => {
  console.log('上传成功，文件列表:', fileList)
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    getimgInfo(res.data[0].fileId)
  }
}

// 上传头像
const getimgInfo = async (fileId: any) => {
  const res: any = await userImageAudit({ fileId })
  if (res.code === 0) {
    myList()
  }
}

// hr基本信息列表
const myList = async () => {
  // 禁用缓存，确保获取最新数据
  const res: any = await hrUserInfo({
    custom: {
      cache: false,
      loading: false,
    },
  })
  if (res.code === 0) {
    if (res.data?.headImgUrl) {
      fileList.value = [
        {
          url: res.data.headImgUrl,
          name: 'tupian',
        },
      ]
    } else {
      fileList.value = [
        {
          url:
            res.data.gender === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png',
          name: 'tupian',
        },
      ]
    }
    myInfo.value = res.data
  }
}

// 部门
const gohrPosition = () => {
  const hrPosition = myInfo.value.hrPosition ? myInfo.value.hrPosition : ''
  uni.navigateTo({
    url: '/sub_business/pages/myInfo/model/hrPosition?hrPosition=' + hrPosition,
  })
}

onLoad(async () => {
  await uni.$onLaunched
  myList()
})

onShow(async () => {
  myList()
})
</script>

<style lang="scss" scoped>
::v-deep .wd-upload__picture {
  border-radius: 50%;
}

::v-deep .wd-upload {
  justify-content: right;
}

::v-deep .custom-class-img {
  width: 100rpx;
  height: 100rpx;
}

::v-deep .wd-upload__close {
  display: none;
}

::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  border-radius: 50% !important;
}

.activeColor {
  color: #4d8fff;
}

.labelName {
  width: 200rpx;
  text-align: left;
}

.pageContaner {
  padding: 0rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
