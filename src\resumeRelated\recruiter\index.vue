<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-backgroud">
    <CustomNavBar color="#000" bgColor="#F0F0F0"></CustomNavBar>

    <view class="recruiter-list">
      <view class="recruiter-company">
        <view class="jobDetail-conpany flex-between" @click="goCompany">
          <view class="flex-c">
            <!-- <image class="jobDetail-conpany-img" src="/static/img/1.jpg"></image> -->
            <image
              class="jobDetail-conpany-img"
              :src="listObj.companyLogoUrl ? listObj.companyLogoUrl : '/static/header/logo.png'"
              mode="aspectFill"
            ></image>
            <view class="jobDetail-conpany-text">
              <view class="jobDetail-conpany-title">{{ listObj.name }}</view>
              <view class="flex-c">
                <view class="jobDetail-conpany-subtitle p-r-40rpx">
                  {{ listObj.sizeName }}·{{ listObj.industryName }}
                </view>
              </view>
            </view>
          </view>
          <view class="jobDetail-card-icon">
            <wd-icon
              class="arrow-right-1"
              name="chevron-right"
              color="#333333"
              size="15px"
            ></wd-icon>
          </view>
        </view>
        <view class="p-b-20rpx p-t-40rpx">
          <view class="card-swiper">
            <wd-swiper
              :autoplay="false"
              v-model:current="current"
              custom-indicator-class="custom-indicator-class"
              custom-image-class="custom-image"
              custom-next-image-class="custom-image-prev"
              custom-prev-image-class="custom-image-prev"
              customItemClass="custom-image-b"
              value-key="attachIdUrl"
              :indicator="{ type: 'none' }"
              :list="listObj.companyStyleVOS"
              previousMargin="0px"
              nextMargin="50px"
              height="260rpx"
            ></wd-swiper>
          </view>
        </view>
      </view>
    </view>
    <view class="line-h"></view>
    <view
      class="position-card"
      v-for="(item, index) in listObj.positionInfoSingleVOS"
      :key="index"
      @click="goPosition(item.id)"
    >
      <view class="position-item">
        <view class="flex-between algin-c">
          <view class="position-item-name text-28rpx">
            {{ item.positionName }}
          </view>
          <view class="page_right salary">{{ item.workSalaryBegin }}-{{ item.workSalaryEnd }}</view>
        </view>

        <view class="position-item-subTag p-b-20rpx">
          <view
            class="subText position-item-subTag-item"
            v-for="(ele, i) in item.positionKey"
            :key="i"
          >
            {{ ele }}
          </view>
        </view>
        <!-- <view class="position-item-content">工作内容：{{ item.text }}</view> -->
      </view>
    </view>
    <view style="height: 40rpx"></view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryHrByCompanyId } from '@/interPost/home'
import { baseUrlPrever } from '@/interPost/img'
import { decryption, encryption } from '@/service/crypto'
import { numberTokw } from '@/utils/common'
// 公司id
const companyId = ref(null)
// hr
const hrPosition = ref(null)
const hrId = ref(null)
// hr名字
const hrPositionName = ref(null)
// 列表
const listObj = ref({})

const circular = ref(true)
const current = ref(1)
const plus = ref(2)
// 去岗位详情
const goPosition = (id: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/hrJobDetail?id=${id}&companyId=${companyId.value}`,
  })
}
// 公司详情
const goCompany = () => {
  uni.navigateTo({
    url: `/resumeRelated/company/index?companyId=${companyId.value}&id=`,
  })
}
// 获取列表
const getList = async () => {
  const res: any = await queryHrByCompanyId({
    id: companyId.value,
    hrId: hrId.value,
  })
  if (res.code === 0) {
    console.log(res.data.positionInfoSingleVOS, 'positionInfoSingleVOS')
    res.data.positionInfoSingleVOS.forEach((ele) => {
      ele.positionKey = ele.positionKey && ele.positionKey.split(',')
      ele.workSalaryBegin =
        ele.workSalaryBegin === 0 ? '面试' : numberTokw(ele.workSalaryBegin + '')
      ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '面试' : numberTokw(ele.workSalaryEnd + '')
      // console.log(item.positionKey.split(','), '===')
    })
    res.data.companyStyleVOS.forEach((item) => {
      // if (item.attachUrl) {
      //   item.attachUrl = `${item.attachUrl}?x-oss-process=image/resize,w_200,h_150,limit_0`
      // }
    })
    listObj.value = res.data
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  companyId.value = options.companyId
  hrId.value = options.hrId
  getList()
})
</script>

<style scoped lang="scss">
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  --wot-swiper-nav-dot-color: #e7e7e7;
  --wot-swiper-nav-dot-active-color: #4d80f0;
  // padding-bottom: 24rpx;
  ::v-deep .custom-image-b {
    border-radius: 20rpx !important;
  }
  :deep(.custom-indicator-class) {
    bottom: -16px;
  }
  :deep(.custom-image) {
    border-radius: 20rpx !important;
  }
}
::v-deep .swiper-item {
  margin-right: 30rpx !important;
  margin-left: 0rpx !important;
}
.salary {
  color: #ff8080 !important;
}
.algin-c {
  align-items: center;
}
.line-h {
  height: 10rpx;
  background-color: #ffffff;
}

.conpany-img {
  width: 100%;
  height: 100vh;
  background-image: url('@/resumeRelated/img/Mask_group(25).png');
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
}

.uScrollList-scroll-view {
  white-space: nowrap;
}

::v-deep .u-read-more__toggle {
  background-image: none !important;
}

.bg-backgroud {
  background-color: #f0f0f0;
}

.recruiter-list {
  padding: 0rpx 40rpx 0rpx;
}

.position-card {
  padding: 20rpx 40rpx 20rpx;
  margin: 40rpx 40rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 10rpx 20rpx 60rpx 0rpx rgba(0, 0, 0, 0.05);

  .position-item-name {
    color: #000000;
  }

  .position-item-subTag {
    display: flex;
    flex-wrap: wrap;
    align-items: center;

    .position-item-subTag-item {
      display: inline-block;
      padding: 0rpx 20rpx;
      margin-top: 30rpx;
      margin-right: 30rpx;
      background-color: #f3f3f3;
      border-radius: 6rpx;
    }
  }

  .position-item-content {
    font-size: 24rpx;
    color: #000000;
  }
}

.recruiter-company {
  padding: 40rpx;
  margin: 20rpx 0rpx 40rpx;
  background: #ffffff;
  border-radius: 40rpx;
  box-shadow: 0rpx 32rpx 60rpx 0rpx rgba(0, 0, 0, 0.03);
}

.jobDetail-conpany {
  .jobDetail-conpany-img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 34rpx;
  }

  .jobDetail-conpany-text {
    margin-left: 20rpx;

    .jobDetail-conpany-title {
      padding-bottom: 10rpx;
      font-size: 28rpx;
      font-weight: 600;
      color: #000000;
    }

    .jobDetail-conpany-subtitle {
      font-size: 24rpx;
      color: #888888;
    }
  }

  .jobDetail-card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;

    .arrow-right-1 {
      line-height: 60rpx;
    }
  }
}

.company-list {
  padding: 40rpx;

  .company-logos {
    width: 128rpx !important;
    height: 128rpx !important;
    border-radius: 18rpx;
  }

  .company-title {
    flex: 1;
    margin-left: 20rpx;
    color: #fff;

    .company-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #f4bb86;
    }

    .company-num {
      position: relative;
      padding-left: 10rpx;
    }

    .company-num::after {
      position: absolute;
      top: 10rpx;
      left: 0rpx;
      width: 1rpx;
      height: 20rpx;
      content: '';
      background-color: #fff;
    }
  }

  .company-js {
    padding: 80rpx 0rpx;
  }

  .company-adress {
    padding: 0rpx 0rpx 0rpx;

    .company-adress-img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      height: 60rpx;
      margin-left: 20rpx;
      line-height: 60rpx;
      border: 1rpx solid #f0bb80;
      border-radius: 20rpx;

      .company-adress-icon {
        width: 36rpx;
        height: 36rpx;
        line-height: 60rpx;
        background-image: url('@/resumeRelated/img/daohang_1.png');
        background-position: 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
}

.company-benefit {
  padding: 0 0 0 40rpx;

  .benefit-list {
    .benefit-list-item {
      display: inline-block;
      padding: 30rpx 40rpx;
      margin-right: 30rpx !important;
      background-color: #2b2b2b;
      border-radius: 20rpx;
    }

    .benefit-list-name {
      color: #888;
    }

    .benefit-list-img {
      width: 32rpx !important;
      height: 32rpx !important;
    }
  }
}
</style>
