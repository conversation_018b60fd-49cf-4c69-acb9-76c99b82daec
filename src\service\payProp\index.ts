import { POST, POSTPaging } from '../index'
import {
  payPropQueryUsingCountListInt,
  payPropQueryUsingCountListDataInt,
  payPropQueryUsingPositionListDataInt,
  payPropQueryUsingPositionListInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** 查询各道具使用中数量(含描述)接口 */
export const payPropQueryUsingCountList = (
  data: payPropQueryUsingCountListDataInt = {},
  config?: HttpRequestConfig,
) =>
  POST<payPropQueryUsingCountListInt>('/easyzhipin-api/payProp/queryUsingCountList', data, config)
/** 查询某道具使用中的岗位列表数量接口 */
export const payPropQueryUsingPositionList = (
  data: Api.Request.IResPagingDataParamsInt<payPropQueryUsingPositionListDataInt>,
  config?: HttpRequestConfig,
) =>
  POSTPaging<payPropQueryUsingPositionListInt>(
    '/easyzhipin-api/payProp/queryUsingPositionList',
    data,
    config,
  )
