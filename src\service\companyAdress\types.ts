export interface hrCompanyWorkAddresstype {
  // 详细地址
  address: string
  // 城市Code
  cityCode: string
  // 城市名称
  cityName: string
  // 区Code
  districtCode: string
  //   区名称
  districtName: string
  //   上传的图片id list
  imageIds: string[]
  //   纬度
  lat: number
  //   经度
  lon: number
  //   省份Code ,
  provideCode: string
  //   省份名称
  provideName: string
}

export interface queryListAdresstype {
  entity: object
  orderBy: object
  page: number
  size: number
}

export interface adressDeltype {
  id: number
}
