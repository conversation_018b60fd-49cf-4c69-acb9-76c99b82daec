<template>
  <view class="pt-54rpx px-28rpx">
    <NavBar @onLeftTap="onBack">
      <template #left>
        <!-- <view class="left-content">
          <Avatar
            class="nav-avatar"
            :size="32"
            :src="info.avatar"
            :placeholder="isSingleChat ? USER_AVATAR_URL : GROUP_AVATAR_URL"
            :withPresence="isSingleChat ? true : false"
            :presenceExt="info.presenceExt"
            :isOnline="info.isOnline"
          />
          <view class="name ellipsis">{{ info.name }}</view>
        </view> -->
      </template>
      <template #center>
        <view class="flex flex-col items-center gap-6rpx">
          <view>
            <text class="c-#000000 font-500 text-32rpx">{{ info.name }}</text>
            <text class="c-#888888 font-500 text-22rpx ml-6rpx">{{ info.ext.hrPosition }}</text>
          </view>
          <text class="c-#88888888 text-22rpx line-clamp-1">{{ info.ext.companyName }}</text>
        </view>
      </template>
      <template #right>
        <wd-icon name="ellipsis" size="22px" color="#000000" @click="handleConversationSetting" />
      </template>
    </NavBar>
    <InfoCard :ext="info.ext" />
  </view>
</template>

<script lang="ts" setup>
// import Avatar from "../../../../components/Avatar/index.vue";
import InfoCard from '@/ChatUIKit/modules/Chat/components/InfoCard/index.vue'
import NavBar from '../../../../components/NavBar/index.vue'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ChatUIKit } from '../../../../index'
import { Chat } from '../../../../types'
// import { USER_AVATAR_URL, GROUP_AVATAR_URL } from "../../../../const";
import { autorun } from 'mobx'
import { CommonUtil } from 'wot-design-uni'

type ChatNavInfo = {
  avatar: string
  name: string
  id: string
  conversationType?: Chat.ChatType
  presenceExt?: string
  ext?: Api.IM.UserBusinessExtInfo
  isOnline?: boolean
}

const info = ref<ChatNavInfo>({
  avatar: '',
  name: '',
  id: '',
  ext: {} as Api.IM.UserBusinessExtInfo,
})

const isSingleChat = computed(() => {
  return info.value.conversationType === 'singleChat'
})

const featureConfig = ChatUIKit.getFeatureConfig()

const unwatchUserInfo = autorun(() => {
  const conv = ChatUIKit.convStore.currConversation
  if (conv?.conversationType === 'singleChat') {
    const userinfo = ChatUIKit.appUserStore.getUserInfoFromStore(conv.conversationId)
    info.value = {
      name: userinfo?.name,
      id: conv.conversationId,
      avatar: userinfo?.avatar,
      conversationType: conv.conversationType,
      presenceExt: userinfo?.presenceExt,
      isOnline: userinfo?.isOnline,
      ext: JSON.parse(userinfo?.ext || '{}') as Api.IM.UserBusinessExtInfo,
    }
  } else if (conv?.conversationType === 'groupChat') {
    const groupInfo = ChatUIKit.groupStore.getGroupInfoFromStore(conv.conversationId)

    info.value = {
      name: groupInfo?.groupName || conv.conversationId,
      id: conv.conversationId,
      avatar: ChatUIKit.groupStore.getGroupAvatar(conv.conversationId),
      conversationType: conv.conversationType,
    }
  }
})

const onBack = () => {
  uni.navigateBack()
}
function handleConversationSetting() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_business/pages/conversation/setting', {}),
  })
}
onMounted(() => {
  if (featureConfig.usePresence && isSingleChat) {
    // 获取用户在线状态
    try {
      ChatUIKit.appUserStore.getUsersPresenceFromServer({
        userIdList: [info.value.id],
      })
    } catch (error) {
      console.log('获取在线状态失败', error)
    }

    // 订阅用户在线状态
    ChatUIKit.appUserStore.subscribePresence({
      userIdList: [info.value.id],
    })
  }
  console.log('featureConfig', info.value)
})

onUnmounted(() => {
  if (featureConfig.usePresence && isSingleChat) {
    // 取消订阅用户在线状态
    ChatUIKit.appUserStore.unsubscribePresence({
      userIdList: [info.value.id],
    })
  }
  unwatchUserInfo()
})
</script>
<style lang="scss" scoped>
@import url('../../../../styles/common.scss');

.name {
  max-width: 45vw;
  color: #171a1c;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  margin-left: 8px;
}

.left-content {
  display: flex;
  align-items: center;
}

.nav-avatar {
  height: 32px;
}
</style>
