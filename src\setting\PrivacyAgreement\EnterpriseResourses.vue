<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘增值服务协议</text>
        <text class="subtitle">版本：ver202505 生效日期：2025年05年24日</text>
      </view>

      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-text">{{ item }}</text>
        </view>
      </view>

      <view class="content">
        <view class="section" id="section-0">
          <text class="section-title">一、协议主体</text>
          <text class="paragraph">
            服务提供方：重庆中誉易职网络信息科技有限公司（以下简称“本公司”或“易直聘”）
          </text>
          <text class="paragraph">
            用户：指注册并使用易直聘APP及增值服务，符合真实身份认证及相关准入条件的自然人或依法设立的企业（以下简称“用户”）
          </text>
        </view>

        <view class="section" id="section-1">
          <text class="section-title">二、定义与范围</text>
          <text class="paragraph">
            1.增值服务：用户通过付费或参与活动获得的除基础招聘功能外的附加服务，包括但不限于：
          </text>
          <text class="paragraph">1.1极速置顶卡</text>
          <text class="paragraph">1.2曝光刷新卡</text>
          <text class="paragraph">1.3黑马炸弹卡</text>
          <text class="paragraph">1.4无限畅聊卡</text>
          <text class="paragraph">1.5简历下载次数扩容</text>
          <text class="paragraph">1.6其他定制化服务</text>
          <text class="paragraph">
            2、适用范围：本协议适用于用户在易直聘平台或相关平台购买及使用增值服务的全部行为。增值服务的使用权仅限于用户本人注册的账号。禁止用户通过API接口、爬虫或其他任何未经授权的技术手段使用本服务。用户不得通过第三方插件、非官方渠道或任何形式擅自接入、使用本服务。若有违反，本公司有权收取每日以合同金额1%计的违约金。
          </text>
        </view>

        <view class="section" id="section-2">
          <text class="section-title">三、服务内容与使用规则</text>
          <text class="paragraph">1.服务开通：</text>
          <text class="paragraph">
            1.1用户需完成实名认证（企业用户需提交营业执照）后方可购买增值服务。
          </text>
          <text class="paragraph">
            1.2部分服务可能需单独签订补充协议（如大型企业定制服务），且该补充协议与本协议冲突的，以补充协议为准。
          </text>
          <text class="paragraph">2.服务期限：</text>
          <text class="paragraph">
            2.1单次购买服务有效期以订单页面标注为准，逾期自动失效。有效期标注方式包括但不限于倒计时提示、服务状态栏标识、订单详情页展示等。
          </text>
          <text class="paragraph">
            2.2自动续费服务需经用户主动授权，用户可随时关闭自动续费功能。关闭操作自下一计费周期起生效。因用户取消授权、拒付、支付账户异常或其他非本公司原因导致自动续费失败的，本公司有权暂停或终止相关服务，且因此产生的后果由用户自行承担，本公司不承担任何责任。
          </text>
          <text class="paragraph">3.使用限制：</text>
          <text class="paragraph">
            用户不得利用增值服务发布虚假职位、歧视性信息或违反法律法规的内容。若用户违反本条规定，首次违规本公司有权予以警告，第二次违规可暂停相关服务，第三次违规本公司有权终止账号并保留追偿损失的权利。经本公司发现或收到相关举报后，有权暂停、终止服务，视情节追究用户法律责任，并有权不予退还服务费用。用户不得私自将账号、服务权限共享、转让、出租、出借、出售、分包于他人，否则本公司有权在不另行通知的情况下暂停或终止全部服务，造成损失的，用户应承担赔偿责任。用户还应遵守平台公示的各项管理规则，不得从事任何损害平台、其他用户或第三方合法权益的行为。对于用户存在上述或其他不当行为的，本公司有权单方认定、暂停、下架、冻结、终止相关服务，且无需提前通知或承担任何责任。
          </text>
          <text class="paragraph">
            4.本公司有权对用户使用增值服务的过程进行实时监测，发现异常流量（包括但不限于非正常时段集中使用、机械性操作特征等）时，可单方暂停服务并启动人工核查程序。
          </text>
          <text class="paragraph">
            5.本公司保留对增值服务效果进行算法优化的权利，用户理解并同意不同时期购买的同类型服务可能因算法迭代产生效果差异。
          </text>
        </view>

        <view class="section" id="section-3">
          <text class="section-title">四、费用与支付</text>
          <text class="paragraph">1.收费标准：</text>
          <text class="paragraph">
            1.1费用以平台内公示价格为准，本公司保留调整价格的权利（调整前7日公示）。
          </text>
          <text class="paragraph">
            1.2套餐类服务不可部分退款。部分退款将导致服务逻辑混乱并产生操作风险，平台有权对此类请求予以拒绝。
          </text>
          <text class="paragraph">2.支付方式：</text>
          <text class="paragraph">2.1支持微信支付、支付宝、企业银行转账。</text>
          <text class="paragraph">
            2.2企业用户可申请开具增值税发票（需提供开票信息），且应在服务开通后20个工作日内提交。
          </text>
          <text class="paragraph">3.争议处理：</text>
          <text class="paragraph">
            扣费异常需在扣费后3个工作日内以书面形式提交凭证申诉，逾期视为无异议。
          </text>
        </view>

        <view class="section" id="section-4">
          <text class="section-title">五、数据与隐私保护</text>
          <text class="paragraph">1.数据使用：</text>
          <text class="paragraph">
            1.1用户通过增值服务获取的候选人简历仅限招聘用途，禁止转售、共享或用于商业爬虫。用户违反本款约定应立即停止服务且已支付费用不予退还。
          </text>
          <text class="paragraph">
            1.2企业用户行为数据（包括但不限于搜索关键词、点击率等），经去标识化处理后，本公司有权用于优化平台算法、产品研发、服务改进及其他合法用途。
          </text>
          <text class="paragraph">2.安全措施：</text>
          <text class="paragraph">2.1采用SSL加密传输、定期安全审计。</text>
          <text class="paragraph">
            2.2用户需自行保管账号密码，因用户故意或重大过失导致账号信息泄漏造成的损失，由用户承担相应责任。
          </text>
        </view>

        <view class="section" id="section-5">
          <text class="section-title">六、免责声明</text>
          <text class="paragraph">1.服务可用性：</text>
          <text class="paragraph">
            因系统维护、网络故障等非本公司重大过失或故意行为导致服务中断的，本公司不承担赔偿责任，但将优先恢复服务。因用户自身设备配置、网络环境不符合平台最低技术要求或因用户网络、终端、自身操作等非平台原因导致的服务障碍或异常，本公司不承担任何责任。优先恢复服务不包含补偿性措施，且累计中断时间不超过72小时的，不构成根本违约。
          </text>
          <text class="paragraph">2.第三方责任：</text>
          <text class="paragraph">
            用户与候选人之间因职位信息、沟通内容、招聘流程或合同履行等产生的任何争议或纠纷，均由用户与候选人自行协商解决，本公司仅作为信息展示平台，不参与其中。除非法律法规另有明确，本公司对用户与候选人之间的争议、沟通结果及合同履行不承担任何直接或间接法法律责任。
          </text>
          <text class="paragraph">2.不可抗力：</text>
          <text class="paragraph">包括但不限于自然灾害、政策变更、黑客攻击等导致的服务异常。</text>
        </view>

        <view class="section" id="section-6">
          <text class="section-title">七、协议变更与终止</text>
          <text class="paragraph">1.变更通知：</text>
          <text class="paragraph">
            协议修改后，公司将通过APP弹窗、站内信、邮件或公司认可的其他有效方式通知用户。用户在变更通知发出后继续使用且未以书面形式提出异议的，视为已接受修改后的协议。
          </text>
          <text class="paragraph">2.终止情形：</text>
          <text class="paragraph">2.1如用户账号被封禁，未使用的增值服务费用不予退还。</text>
          <text class="paragraph">
            2.2如因不可抗力导致本公司停止运营或服务无法履行的，本公司将提前30日通过公告等方式通知用户，并安排剩余服务价值的折现。剩余服务价值的折现标准及具体执行细则由本公司根据实际情况合理确定，用户对此无异议。
          </text>
          <text class="paragraph">3.争议解决</text>
          <text class="paragraph">
            因本协议产生的争议，双方应协商解决；协商不成，提交本公司所在地有管辖权的人民法院诉讼解决。
          </text>
        </view>

        <view class="section" id="section-7">
          <text class="section-title">八、确认条款</text>
          <text class="paragraph">
            用户点击“立即购买”或实际使用增值服务，即视为已阅读并同意本协议全部内容。
          </text>
        </view>

        <view class="section" id="section-8">
          <text class="section-title">九、保密条款</text>
          <text class="paragraph">
            用户承诺对使用增值服务过程中知悉的本公司商业秘密（包括但不限于算法逻辑、经营数据）承担保密义务，未经书面许可不得向第三方披露。
          </text>
        </view>

        <view class="section" id="section-9">
          <text class="section-title">十、通知与送达</text>
          <text class="paragraph">
            双方确认以用户注册时提供的联系方式及APP站内信系统作为有效送达方式。通过上述方式发送的通知，发出即视为送达。
          </text>
          <text class="paragraph">
            注：本协议的解释应当遵循诚实信用原则及行业惯例进行。如协议条款部分无效，不影响其他条款的效力。
          </text>
        </view>

        <view class="footer">
          <text class="footer-text">生效日期：2025年05年24日</text>
          <text class="footer-text">版本：ver202505</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 目录列表
const contentList = ref([
  '一、协议主体',
  '二、定义与范围',
  '三、服务内容与使用规则',
  '四、费用与支付',
  '五、数据与隐私保护',
  '六、免责声明',
  '七、协议变更与终止',
  '八、确认条款',
  '九、保密条款',
  '十、通知与送达',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量
            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height // 导航栏高度 + 额外间距
            }
            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop
            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.content-list {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
}

.list-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.list-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;

  .list-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
</style>
