<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="上传附件" class="base_header"></CustomNavBar>
    </template>
    <web-view :src="webViewUrl" @message="handleWebviewMessage"></web-view>
  </z-paging>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { addFileResume } from '@/interPost/resume'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { useResumeStore } from '@/store'
const resumeStore = useResumeStore()
const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const webViewUrl = ref('')
// 判断是哪个页面
const type = ref('')
onLoad((options) => {
  // 使用本地HTML文件路径
  const token = getToken()
  type.value = options.type
  webViewUrl.value = `/static/resumeRelated/pdf-upload.html?token=${token}`
})

// 处理WebView发送的消息
const handleWebviewMessage = (event: any) => {
  console.log(event, 'event')
  const message = event.detail.data[0].fileId[0]
  console.log(message.fileId, 'message.fileId')
  if (message.fileId) {
    console.log(message, 'message')
    if (type.value === 'onlineResume') {
      // 上传作品集成功，刷新列表
      resumeStore.setPortfolio(message.fileId)
      resumeStore.setPortfolioUrl(message.url + '.pdf')
      uni.navigateBack()
    } else if (type.value === 'portfolio') {
      // 上传简历成功，刷新列表
      addFilePDF(message.fileId)
    }
  }

  if (message.action === 'uploadError') {
    uni.showToast({
      title: message.msg || '上传失败',
      icon: 'none',
    })
  }

  if (message.action === 'close') {
    uni.navigateBack()
  }
}
// 上传附件简历
const addFilePDF = async (fileId) => {
  const res: any = await addFileResume({ fileId })
  if (res.code === 0) {
    uni.$emit('pdfUploadSuccess')
    uni.navigateBack()
    uni.showToast({ title: '上传成功', icon: 'none' })
  } else {
    uni.showToast({ title: '上传失败', icon: 'none' })
  }
}

onMounted(() => {
  const instance = getCurrentInstance()
  const query = uni.createSelectorQuery().in(instance)
  const { windowHeight } = uni.getSystemInfoSync() // 屏幕高度（单位：px）
  console.log('屏幕高度:', windowHeight)
  if (instance && instance.proxy) {
    const currentWebview = instance.proxy.$scope?.$getAppWebview()
    if (currentWebview) {
      nextTick(() => {
        setTimeout(() => {
          const closeHeight = 0
          let baseHeaderHeight = 0

          query
            .select('.base_header')
            .boundingClientRect((res) => {
              const rect = Array.isArray(res) ? res[0] : res
              if (rect && rect.height) {
                baseHeaderHeight = rect.height
              } else {
                baseHeaderHeight = 100 // 默认高度
              }
            })
            .exec(() => {
              const totalTop = closeHeight + baseHeaderHeight
              console.log('Calculated totalTop:', totalTop)

              const wv = currentWebview.children()?.[0]
              if (wv) {
                wv.setStyle({
                  top: `${totalTop}px`,
                  height: `${windowHeight - totalTop + 30}px`,
                  zIndex: -1,
                })
              }
            })
        }, 300)
      })
    }
  }
})
</script>

<style scoped></style>
