<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="accountLogin bg-img">
    <CustomNavBar title="增值电信许可证-易直聘"></CustomNavBar>
    <view class="humanResources">
      <image class="rlzy" :src="rlzy"></image>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import rlzy from '@/setting/img/tel.png'
</script>

<style lang="scss" scoped>
.humanResources {
  padding: 80rpx 40rpx;
  text-align: center;
}
.rlzy {
  width: calc(100% - 80rpx);
  height: 800rpx;
  margin: auto;
}
</style>
