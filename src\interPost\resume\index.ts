import { POST } from '@/service'
// 在线简历
export const queryFullData = () => {
  return POST('/easyzhipin-api/resume/queryFullData')
}
// 求职状态
export const updateStatus = (data) => {
  return POST('/easyzhipin-api/resume/updateStatus', data)
}
// 工作经历新增
export const resumeWorkExperiencesAdd = (data) => {
  return POST('/easyzhipin-api/resumeWorkExperiences/add', data)
}
// 工作经历编辑
export const resumeWorkExperiencesUpdate = (data) => {
  return POST('/easyzhipin-api/resumeWorkExperiences/update', data)
}
// 工作经历删除
export const resumeWorkExperiencesDel = (data) => {
  return POST('/easyzhipin-api/resumeWorkExperiences/deleteById', data)
}
// 首页距离
export const cuserInfo = (data) => {
  return POST('/easyzhipin-api/my/cuserInfo', data)
}
// 个人优势新增
export const updateMyLights = (data) => {
  return POST('/easyzhipin-api/resumeBaseInfo/updateMyLights', data)
}
// 添加项目经历
export const resumeProjectAdd = (data) => {
  return POST('/easyzhipin-api/resumeProject/add', data)
}
// 删除工作经历
export const resumeProjectDel = (data) => {
  return POST('/easyzhipin-api/resumeProject/deleteById', data)
}
// 编辑工作经历
export const resumeProjectUpdate = (data) => {
  return POST('/easyzhipin-api/resumeProject/update', data)
}
// 工作技能更新
export const resumeSkillCertificateUpdate = (data) => {
  return POST('/easyzhipin-api/resumeBaseInfo/updateSkills', data)
}

// 技能证书新增
export const resumeCertificateAdd = (data) => {
  return POST('/easyzhipin-api/resumeCertificate/add', data)
}
// 技能证书编辑
export const resumeCertificateUpdata = (data) => {
  return POST('/easyzhipin-api/resumeCertificate/update', data)
}
// 技能证书删除
export const resumeCertificateDel = (data) => {
  return POST('/easyzhipin-api/resumeCertificate/deleteById', data)
}
// 技能证书所有
export const resumeCertificateQuery = (data) => {
  return POST('/easyzhipin-api/resumeCertificate/queryBaseInfoId', data)
}
// 技能证书单个
export const resumeCertificateQueryById = (data) => {
  return POST('/easyzhipin-api/resumeCertificate/queryById', data)
}
// 求职期望新增
export const userJobIntentionAdd = (data) => {
  return POST('/easyzhipin-api/userJobIntention/add', data)
}
// 求职期望编辑
export const userJobIntentionUpdate = (data) => {
  return POST('/easyzhipin-api/userJobIntention/update', data)
}
// 删除
export const userJobIntentionDel = (data) => {
  return POST('/easyzhipin-api/userJobIntention/deleteById', data)
}
// 教育经历-新增
export const resumeEducationalAdd = (data) => {
  return POST('/easyzhipin-api/resumeEducational/add', data)
}
// 教育经历-编辑
export const resumeEducationalUpdate = (data) => {
  return POST('/easyzhipin-api/resumeEducational/update', data)
}
// 教育删除
export const resumeEducationalDel = (data) => {
  return POST('/easyzhipin-api/resumeEducational/deleteById', data)
}
// 作品集-新增
export const resumeFileAdd = (data) => {
  return POST('/easyzhipin-api/resumeFile/add', data)
}
// 作品集-编辑
export const resumeFileUpdate = (data) => {
  return POST('/easyzhipin-api/resumeFile/update', data)
}
// 高薪就业
export const highPositions = (data) => {
  return POST('/easyzhipin-api/positionInfo/highPositions', data)
}
// 查看简历列表
export const queryMyFileResumeList = () => {
  return POST('/easyzhipin-api/resume/queryMyFileResumeList')
}
// 删除简历
export const deleteFileResume = (data) => {
  return POST('/easyzhipin-api/resume/deleteFileResume', data)
}
// 新增简历接口
export const addFileResume = (data) => {
  return POST('/easyzhipin-api/resume/addFileResume', data)
}
// 根据简历模板渲染我的HTML简历接口
export const generatorHtml = (data) => {
  return POST('/easyzhipin-api/resumeTemplate/generatorHtml', data)
}
// 生成附件简历PDF并保存到附件简历接口
export const generatorFileResumeAndSave = (data) => {
  return POST('/easyzhipin-api/resumeTemplate/generatorFileResumeAndSave', data)
}

// 沟通过
export const ditchThroughList = (data) => {
  return POST('/easyzhipin-api/myDetails/ditchThroughList', data)
}
// (投递过)通过用户id查看已经发送的岗位接口
export const sendResumeList = (data) => {
  return POST('/easyzhipin-api/myDetails/sendResumeList', data)
}
// 待面试
export const queryWaitMeetingList = () => {
  return POST('/easyzhipin-api/myDetails/queryWaitList')
}
// 历史面试
export const queryHistoryList = () => {
  return POST('/easyzhipin-api/myDetails/queryHistoryList')
}
// 预览卡片
export const myCard = () => {
  return POST('/easyzhipin-api/my/myCard')
}

// 附加简历
export const resumeFileUrl = () => {
  return POST('/easyzhipin-api/resumeFile/resumeFileUrl')
}

// 发起面试
export const interviewAdd = (data) => {
  return POST<number>('/easyzhipin-api/hrInterviewRecord/add', data)
}
// 根据id查询面试记录
export const queryInterviewRecordById = (data) => {
  return POST('/easyzhipin-api/hrInterviewRecord/queryById', data)
}
// 根据取消面试记录
export const cancelById = (data) => {
  return POST('/easyzhipin-api/hrInterviewRecord/cancelById', data)
}
// 修改面试记录
export const hrInterviewUpdate = (data) => {
  return POST('/easyzhipin-api/hrInterviewRecord/update', data)
}
// 拒绝面试
export const userInterviewRecord = (data) => {
  return POST('/easyzhipin-api/userInterviewRecord/deal', data)
}
