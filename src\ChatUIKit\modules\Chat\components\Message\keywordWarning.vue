<template>
  <view class="text-center mb-20rpx flex items-start justify-center gap-12rpx">
    <wd-img :src="categoryIcon" width="26rpx" height="30rpx" />
    <text class="c-#888888 text-22rpx max-w-460rpx text-center">
      {{ keywordData[userIntel.type] }}
    </text>
  </view>
</template>

<script lang="ts" setup>
import errorImg from '@/ChatUIKit/static/message-custom/error.png'
import promptImg from '@/ChatUIKit/static/message-custom/prompt.png'

interface propsInt {
  keywordData: Record<number, string>
  category: string
}

const props = defineProps<propsInt>()

const { userIntel } = useUserInfo()

const ICON_TYPE_CONFIG = {
  ERROR_TYPES: ['ADULT', 'ILLEGAL', 'POLITICAL', 'ILLEGAL_WORK'],
  WARNING_TYPES: ['PAYMENT', 'DISCRIMINATION', 'FALSE_INFO', 'OVERSEAS'],
}

const categoryIcon = computed(() => {
  if (ICON_TYPE_CONFIG.ERROR_TYPES.includes(props.category)) {
    return errorImg
  }
  if (ICON_TYPE_CONFIG.WARNING_TYPES.includes(props.category)) {
    return promptImg
  }
  return promptImg
})
</script>

<style lang="scss" scoped></style>
