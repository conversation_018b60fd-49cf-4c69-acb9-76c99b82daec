import { POST } from '../index'
import { payDealDetailInt, payDealListInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 订单信息 */
export const payDealList = (data: payDealListInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/queryAllList', data, config)
// 订单详情
export const payDealDetail = (data: payDealDetailInt, config?: HttpRequestConfig) =>
  POST<any>('/easyzhipin-api/payDeal/detail', data, config)
