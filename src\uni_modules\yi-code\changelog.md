## 1.0.8（2024-10-05）

修复vue2输入内容获取为空的BUG

## 1.0.7（2024-09-02）

修复部分情况下undefined BUG

## 1.0.6（2024-08-01）

增加ref方法 clear 清除输入内容

## 1.0.5（2024-06-26）

修复因text-align导致的input显示问题

## 1.0.4（2024-06-26）

更新隐藏input长度过短显示数字BUG

## 1.0.3（2024-06-19）

修复插件取消聚焦时的动画隐藏问题

## 1.0.2（2024-05-15）

支持自定义隐藏显示时的字符， 新增hide属性String，传入参数为隐藏显示的字符（一般使用 \* 号）

## 1.0.1（2024-03-23）

修复在某些设备显示输入框数字的bug

## 1.0.0（2024-03-23）

首次更新三种样式验证框效果
