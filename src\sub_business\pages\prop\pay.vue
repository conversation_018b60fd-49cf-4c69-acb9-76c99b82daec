<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '道具',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #right>
            <text class="c-white text-28rpx" @click="handleGoCdk">兑换</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import { payPropQueryUsingCountList } from '@/service/payProp'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import propList from '@/sub_business/components/propList.vue'

const { pageStyle } = usePaging({
  style: {
    background: '#383838',
  },
})
const { payPropInfo, payPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#ffffff',
}

function handleClickLeft() {
  uni.navigateBack()
}
function handleGoCdk() {
  if (!payPropActive.value) {
    return
  }
  uni.navigateTo({
    url: `/sub_business/pages/prop/cdk`,
  })
}
</script>

<style lang="scss" scoped></style>
