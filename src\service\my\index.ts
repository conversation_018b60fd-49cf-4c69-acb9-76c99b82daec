import { POST } from '../index'
import { queryMyInfoInt, myQueryMyWxCodeInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 获取C端用户信息 */
export const myQueryMyInfo = (config?: HttpRequestConfig) =>
  POST<queryMyInfoInt>('/easyzhipin-api/my/queryMyInfo', {}, config)

// 更新微信号
export const updateHrWxCode = (data: myQueryMyWxCodeInt, config?: HttpRequestConfig) =>
  POST<string>('/easyzhipin-api/hrUser/updateHrWxCode', data, config)
// 获取B端用户微信号
export const queryHrWxCode = (config?: HttpRequestConfig) =>
  POST<string>('/easyzhipin-api/hrUser/queryHrWxCode', {}, config)
