import { debounce } from 'lodash'

/**
 * 全局防抖函数
 * @param {Function} func - 需要防抖的函数
 * @param {number} [wait=500] - 防抖延迟时间(ms)
 * @param {Object} [options={}] - 配置选项
 * @returns {Function} - 包装后的防抖函数
 */
const globalDebounce = (func, wait = 500, options = {}) => {
  return debounce(func, wait, {
    leading: true, // 禁用首次立即执行
    trailing: false, // 启用延迟结束后执行
    ...options, // 允许自定义覆盖
  })
}

export default globalDebounce
