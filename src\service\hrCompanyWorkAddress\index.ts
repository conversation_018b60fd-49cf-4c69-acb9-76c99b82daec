import { POSTPaging } from '../index'
import { hrCompanyWorkAddressQueryPassListDataInt } from './types'
import {} from '@/service/types'
import { HttpRequestConfig } from 'luch-request'

/** 查看已审核通过的所有位置列表数据接口 */
export const hrCompanyWorkAddressQueryPassList = (
  data: Api.Request.IResPagingDataParamsInt<hrCompanyWorkAddressQueryPassListDataInt>,
  config?: HttpRequestConfig,
) => POSTPaging('/easyzhipin-api/hrCompanyWorkAddress/queryPassList', data, config)
