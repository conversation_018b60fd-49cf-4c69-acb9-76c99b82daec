import { POST } from '../index'
import { hrExchangeResumeRecordRequestResumeDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** B端用户索要简历(只记录不发IM)接口 */
export const hrExchangeResumeRecordRequestResume = (
  data: hrExchangeResumeRecordRequestResumeDataInt,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/hrExchangeResumeRecord/requestResume', data, config)

/** B端用户同意或拒绝接收简历(C端发送后B端同意或拒绝)不发送IM消息接口 */
export const hrExchangeResumeRecordDoResume = (
  data: Api.IM.CustomMessage.ModifyCustomMessage,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/hrExchangeResumeRecord/doResume', data, config)
