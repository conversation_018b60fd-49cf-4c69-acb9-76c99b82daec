import { POST } from '../index'
import {
  imDomainInfoInt,
  imCreateAccountInt,
  imCreateAccountDataInt,
  imModifyCustomMessageDataInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** 环信服务域名信息接口 */
export const imDomainInfo = (config?: HttpRequestConfig) =>
  POST<imDomainInfoInt>('/easyzhipin-api/im/imDomainInfo', {}, config)

/** 创建环信账号 */
export const imCreateAccount = (data: imCreateAccountDataInt, config?: HttpRequestConfig) =>
  POST<imCreateAccountInt>('/easyzhipin-api/im/createAccount', data, config)

/** 修改环信自定义消息 */
export const imModifyCustomMessage = (
  data: imModifyCustomMessageDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/im/modifyCustomMessage', data, config)

/** 获取环信用户不感兴趣的用户名单 */
export const imUnInterestUserNameList = (config?: HttpRequestConfig) =>
  POST<string[]>('/easyzhipin-api/im/unInterestUserNameList', {}, config)
