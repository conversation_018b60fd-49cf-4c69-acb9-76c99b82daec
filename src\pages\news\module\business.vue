<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          safe-area-inset-top
          placeholder
          custom-class="!bg-transparent px-30rpx"
        >
          <template #left>
            <wd-icon :name="thunderPromptImg" size="54rpx" @click="handleThunderPrompt" />
          </template>
          <template #right>
            <view class="flex items-center" @click="goInfo">
              <wd-icon :name="setupImg" size="54rpx" />
            </view>
          </template>
        </wd-navbar>
        <view class="px-40rpx mt-28rpx">
          <wd-search
            v-model="searchConversation"
            placeholder-left
            hide-cancel
            custom-class="!p-0 !bg-transparent"
            placeholder="搜索联系人"
          />
          <view class="border-t-1px border-t-solid border-t-[#DEDDDD] mx--40rpx px-40rpx my-34rpx">
            <view class="w-580rpx">
              <wd-tabs v-model="newsTabsStatus" line-width="80rpx" line-height="10rpx">
                <wd-tab
                  v-for="(item, index) in newsTabsList"
                  :key="item.name"
                  :title="`${item.label}`"
                  :name="index"
                />
              </wd-tabs>
            </view>
          </view>
        </view>
      </wd-config-provider>
    </template>
    <view class="px-40rpx">
      <newsBusinessList
        v-model:search="searchConversation"
        @chat="handleChat"
        :type="currentMarkType"
      />
    </view>
    <template #bottom>
      <customTabbar name="news" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { CONVERSATION_MARKS } from '@/enum'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import customTabbar from '@/components/common/custom-tabbar.vue'
import newsBusinessList from '@/components/news/news-business-list.vue'
import setupImg from '@/static/common/setup.png'
import thunderPromptImg from '@/static/common/thunder-prompt.png'

defineOptions({
  name: 'NewsBusiness',
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const searchConversation = ref('')
const themeVars: ConfigProviderThemeVars = {
  searchInputBg: '#FFFFFF',
  searchInputHeight: '82rpx',
  searchInputRadius: '80rpx',
  searchPlaceholderColor: '#333333',
  searchIconSize: '36rpx',
  searchIconColor: '#333333',
  searchInputFs: '24rpx',
  searchInputColor: ' #333333',
  searchCancelColor: '#333333',
  tabsNavLineBgColor: '#FFA7A7',
  tabsNavFs: '26rpx',
}
const newsTabsStatus = ref(0)
const newsTabsList = [
  {
    label: '全部',
    name: CONVERSATION_MARKS.ALL,
    index: 0,
  },
  {
    label: '新招呼',
    name: CONVERSATION_MARKS.NEW_GREETING,
    index: 1,
  },
  {
    label: '仅沟通',
    name: CONVERSATION_MARKS.ONLY_CHAT,
    index: 2,
  },
  {
    label: '已交换',
    name: CONVERSATION_MARKS.EXCHANGED,
    index: 3,
  },
  {
    label: '已约面',
    name: CONVERSATION_MARKS.INTERVIEWED,
    index: 4,
  },
]

// 当前选中的标记类型
const currentMarkType = computed(() => {
  return newsTabsList[newsTabsStatus.value]?.name ?? CONVERSATION_MARKS.ALL
})
const goInfo = () => {
  uni.navigateTo({
    url: '/chartPage/message/index',
  })
}
function handleThunderPrompt() {
  uni.navigateTo({
    url: '/sub_business/pages/rapid-processing/index',
  })
}
const handleChat = async (id: string) => {
  try {
    uni.navigateTo({
      url: CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
        type: 'singleChat',
        id,
      }),
    })
  } catch (error) {}
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__line {
    bottom: 6px;
  }
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    &.is-active {
      font-size: 30rpx;
    }
  }
}
</style>
