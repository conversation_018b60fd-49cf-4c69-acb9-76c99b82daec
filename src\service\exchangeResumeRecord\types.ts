export interface sendResumeDataInt {
  /** 附件id */
  attachmentId: number
  /** 企业hr的用户id */
  hrId: number
  /** 岗位id */
  positionId: number
}

export interface BatchSendListInt {
  hrUserId: number
  positionId: number
}
export interface BatchSendMessageListInt extends BatchSendListInt {
  to: string
}
export interface batchSendResumeDataInt {
  attachmentId: number
  greeting: string
  sendList: BatchSendListInt[]
}

export interface batchSendResumeInt {
  batchNo: string
  data: {
    exchangeResumeRecordId: number
    hrUserId: number
    imSessionId: number
  }[]
}

export interface batchSendResumeCallbackDataInt extends Pick<batchSendResumeInt, 'batchNo'> {}
