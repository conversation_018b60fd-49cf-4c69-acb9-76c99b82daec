import { POST } from '../index'
import { hrExchangeCodeExchangeDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** B端HR用户发起交换微信号的请求接口 */
export const hrExchangeCodeExchange = (
  data: hrExchangeCodeExchangeDataInt,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/hrExchangeCode/exchange', data, config)

/** B端HR用户同意或拒绝应聘者发送的交换微信请求处理接口 */
export const hrExchangeCodeDoExchange = (
  data: Api.IM.CustomMessage.ModifyCustomMessage,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/hrExchangeCode/doExchange', data, config)
