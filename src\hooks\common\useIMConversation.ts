import { CommonUtil } from 'wot-design-uni'
import pLimit from 'p-limit'
import { chat<PERSON><PERSON> } from '@/ChatUIKit/sdk'
import { logger } from '@/ChatUIKit/log'
import { EasemobChat } from 'easemob-websdk'
import { currRoute } from '@/utils'
import { IM_LISTENER_TYPE, USER_TYPE, PUSH_NOTIFICATION_TYPE, CONVERSATION_MARKS } from '@/enum'
import { imSessionRecordSaveSession } from '@/service/imSessionRecord'
import { resumeQueryMyFileResumeList } from '@/service/resume'
import {
  exchangeResumeRecordSendResume,
  exchangeResumeRecordBatchSendResume,
  exchangeResumeRecordBatchSendResumeCallback,
} from '@/service/exchangeResumeRecord'
import { exchangeNumberExchange } from '@/service/exchangeNumber'
import { exchangeCodeExchange } from '@/service/exchangeCode'
import { sysUserCallQueryOne } from '@/service/sysUserCall'
import { hrExchangeResumeRecordRequestResume } from '@/service/hrExchangeResumeRecord'
import { hrExchangeNumberExchange } from '@/service/hrExchangeNumber'
import { hrExchangeCodeExchange } from '@/service/hrExchangeCode'
import { imUnInterestUserNameList } from '@/service/im'
import type { UserInfoWithPresence, UIKITConversationItem } from '@/ChatUIKit/types'
import type { positionInfoQueryIMCardInfoByIdInt } from '@/service/positionInfo/types'
import type { exchangeCodeExchangeDataInt } from '@/service/exchangeCode/types'
import type {
  batchSendResumeDataInt,
  BatchSendMessageListInt,
} from '@/service/exchangeResumeRecord/types'

interface PayloadMsg {
  type: PUSH_NOTIFICATION_TYPE
  from: string
  chatType: string
}

interface SendResult {
  recipient: string
  status: 'fulfilled' | 'rejected'
  reason?: any
}

type ConversationMarkType = (typeof CONVERSATION_MARKS)[keyof typeof CONVERSATION_MARKS]
export type ConversationWithUserInfo = UIKITConversationItem &
  UserInfoWithPresence &
  EasemobChat.ConversationItem

interface CustomMessage
  extends Omit<EasemobChat.CreateCustomMsgParameters, 'type' | 'chatType' | 'customExts'> {
  customExts: Api.IM.CustomMessage.ExtInfo
}

interface SendMassResumeMessageParams extends Pick<batchSendResumeDataInt, 'greeting'> {
  sendList: BatchSendMessageListInt[]
}
const MESSAGE_STATUS = {
  DELIVERED: '送达',
  READ: '已读',
  FAILED: '失败',
} as const
const chatType: EasemobChat.ChatType = 'singleChat'
const customCardInfo = ref<positionInfoQueryIMCardInfoByIdInt>({})
const conversationList = ref<ConversationWithUserInfo[]>([])
const unInterestBlackUserNameList = ref<string[]>([])
const totalUnreadCount = ref(0)
export const useIMConversation = () => {
  const { sm4Encrypt } = useSmCrypto({
    type: 'password',
  })
  const { userIntel, userRoleIsBusiness } = useUserInfo()
  const lastUpdatedTime = ref(0)
  /**
   * 加载会话列表
   * @param forceRefresh 是否强制刷新
   */
  const loadConversations = CommonUtil.debounce(async (forceRefresh: boolean = false) => {
    if (!uni.$UIKit) return
    const now = Date.now()
    if (!forceRefresh && now - lastUpdatedTime.value < 5000) return
    try {
      await uni.$UIKit.convStore.getConversationList()
      const { data: unInterestUserNameList } = await imUnInterestUserNameList()
      unInterestBlackUserNameList.value = unInterestUserNameList
      const conversationStoreList = uni.$UIKit.convStore.conversationList
      const userIdList = conversationStoreList.map((item) => item.conversationId)
      await uni.$UIKit.appUserStore.getUsersInfoFromServer({
        userIdList,
      })
      try {
        await uni.$UIKit.appUserStore.getUsersPresenceFromServer({
          userIdList,
        })
      } catch (error) {
        console.log('获取在线状态失败', error)
      }
      const filteredConversationList = conversationStoreList.filter(
        (item) => !unInterestBlackUserNameList.value.includes(item.conversationId),
      )
      const currentUserId = getIMLoginId.value
      for (const conversation of filteredConversationList) {
        const { conversationId, lastMessage, marks = [] } = conversation
        const hasNewGreetingMark = marks.includes(
          CONVERSATION_MARKS.NEW_GREETING as ConversationMarkType,
        )
        const hasHigherPriorityMark = marks.some((mark) =>
          [
            CONVERSATION_MARKS.EXCHANGED as ConversationMarkType,
            CONVERSATION_MARKS.INTERVIEWED as ConversationMarkType,
          ].includes(mark as ConversationMarkType),
        )
        if (hasNewGreetingMark || hasHigherPriorityMark) continue
        if (lastMessage && lastMessage.from && lastMessage.from !== currentUserId) {
          try {
            await setConversationMark(conversationId, 'singleChat', CONVERSATION_MARKS.NEW_GREETING)
            console.log(`为会话 ${conversationId} 设置 NEW_GREETING 标记（离线消息）`)
          } catch (error) {
            console.error(`设置离线消息标记失败 ${conversationId}:`, error)
          }
        }
      }
      conversationList.value = filteredConversationList.map((item) => {
        const { conversationId } = item
        const user = uni.$UIKit.appUserStore.getUserInfoFromStore(conversationId)
        return {
          ...item,
          ...user,
        }
      })
      console.log('conversationList', conversationList.value)
      totalUnreadCount.value = conversationList.value.reduce((total, conversation) => {
        return total + (conversation.unReadCount || 0)
      }, 0)
      // #ifdef APP-PLUS
      plus.runtime.setBadgeNumber(totalUnreadCount.value)
      // #endif
      lastUpdatedTime.value = now
    } catch (error) {
      console.error('加载会话列表失败', error)
    }
  }, 300)
  const handleConversationMarkForMessage = async (from: string) => {
    const conversation = uni.$UIKit.convStore.getConversationById(from)
    const marks = conversation?.marks || []
    const hasNewGreetingMark = marks.includes(
      CONVERSATION_MARKS.NEW_GREETING as ConversationMarkType,
    )
    const hasHigherPriorityMark = marks.some((mark) =>
      [
        CONVERSATION_MARKS.EXCHANGED as ConversationMarkType,
        CONVERSATION_MARKS.INTERVIEWED as ConversationMarkType,
      ].includes(mark as ConversationMarkType),
    )
    // 如果没有NEW_GREETING标记且没有更高优先级标记，才设置NEW_GREETING
    if (!hasNewGreetingMark && !hasHigherPriorityMark) {
      await setConversationMark(from, 'singleChat', CONVERSATION_MARKS.NEW_GREETING)
    }
    loadConversations(true)
  }
  /** 监听新消息以更新未读计数 */
  const listenForNewMessages = () => {
    if (!uni.$UIKit) return
    uni.$UIKit.getChatConn().addEventHandler(IM_LISTENER_TYPE.RECEIVE_MESSAGE, {
      onTextMessage: async (msg) => {
        const {
          from,
          chatType,
          ext: { ease_chat_uikit_user_info: easeChatUikitUserInfo },
        } = msg
        const { nickname } = easeChatUikitUserInfo ?? {}
        createLocalNotification(
          {
            title: nickname ?? '',
            content: getLastTypeMessage(msg),
            payload: { type: PUSH_NOTIFICATION_TYPE.IM_MESSAGE, from, chatType } as PayloadMsg,
          },
          msg.from,
        )
        handleConversationMarkForMessage(msg.from)
      },
      onImageMessage: async (msg) => {
        handleConversationMarkForMessage(msg.from)
      },
      onCustomMessage: async (msg) => {
        const { customExts } = msg
        if (customExts?.type === 'conversation_mark_sync') {
          const { mark } = customExts as Api.IM.CustomMessage.ExtInfo
          await setConversationMark(msg.from, 'singleChat', mark, false)
          console.log(`收到对方标记同步，会话 ${msg.from} 标记更新为: ${mark}`)
          loadConversations(true)
          return
        }
        handleConversationMarkForMessage(msg.from)
      },
      onReceivedMessage: () => {
        loadConversations(true)
      },
      onReadMessage: async (msg) => {
        console.log('收到已读回执', msg)
      },
      onDeliveredMessage: async (msg) => {
        console.log('收到已送达回执', msg)
      },
    })
  }
  /** 创建本地通知栏消息 */
  const createLocalNotification = (
    options: UniNamespace.CreatePushMessageOptions,
    form: string,
  ) => {
    if (!unInterestBlackUserNameList.value.includes(form)) {
      // #ifdef APP-PLUS
      uni.createPushMessage({
        ...options,
      })
      // #endif
    }
  }
  /** 监听推送事件 */
  const listenForPushNotifications = () => {
    const pushMsgCallback = (message: UniNamespace.OnPushMessageCallbackOptions) => {
      const { path } = currRoute()
      const { type, data } = message
      const { payload } = data
      if (type === 'click') {
        const chatUrl = '/ChatUIKit/modules/Chat/index'
        const isChat = path === chatUrl
        const { from, chatType } = payload as PayloadMsg
        if (from && chatType) {
          uni[isChat ? 'redirectTo' : 'navigateTo']({
            url: CommonUtil.buildUrlWithParams(chatUrl, {
              type: chatType,
              id: from,
            }),
          })
        }
      }
    }
    // #ifdef APP-PLUS
    uni.onPushMessage(pushMsgCallback)
    // #endif
  }
  /** 关闭推送事件监听 */
  const closePushNotifications = () => {
    // #ifdef APP-PLUS
    uni.offPushMessage()
    // #endif
  }
  /** 获取im用户配置信息 */
  const getIMUserInfo = (userId: string) => {
    return uni.$UIKit.appUserStore.getUserInfoFromStore(userId)
  }
  /** 获取自己的im信息 */
  const getIMUserInfoSelf = computed(() => uni.$UIKit.appUserStore.getSelfUserInfo())
  /** 获取当前会话信息 */
  const getCoversationInfo = computed(() => uni.$UIKit.convStore.currConversation)
  /** 获取当前im登录实例 */
  const getIMLoginId = computed<string>(() => uni.$UIKit.getChatConn().user ?? '')
  const getMessageStatusPrefix = (message: EasemobChat.MessageBody) => {
    if (message.from !== getIMLoginId.value) return ''
    return MESSAGE_STATUS.DELIVERED
  }
  /** 根据类型获取最后一条消息 */
  const getLastTypeMessage = (last: EasemobChat.MessageBody) => {
    const messageHandlers = {
      txt: (msg: EasemobChat.TextMsgBody) => msg.msg || '',
      img: () => '[图片]',
      custom: (msg: EasemobChat.CustomMsgBody) => {
        const customExts = msg.customExts || {}
        console.log('customExts', customExts, last, getIMLoginId.value)
        // 处理标记同步消息
        if (customExts.type === 'conversation_mark_sync') {
          const { sourceMessageType } = customExts as Api.IM.CustomMessage.ExtInfo
          if (sourceMessageType) {
            const isMyOperation = last.from === getIMLoginId.value
            const getSourceMessageText = (messageType: Api.IM.CustomMessage.ExtType) => {
              const messageTypeHandlers = {
                resume: () => {
                  if (isMyOperation) {
                    return userRoleIsBusiness.value ? '您已同意对方的简历请求' : '您已发送简历'
                  } else {
                    return userRoleIsBusiness.value ? '对方同意发送简历' : '对方同意了您的简历请求'
                  }
                },
                exchange_phone: () => {
                  return isMyOperation ? '您同意了交换电话' : '对方同意了交换电话'
                },
                exchange_wechat: () => {
                  return isMyOperation ? '您同意了交换微信' : '对方同意了交换微信'
                },
                interview_appointment: () => {
                  return isMyOperation ? '您同意了面试邀约' : '对方同意了面试邀约'
                },
              }
              const handler = messageTypeHandlers[messageType]
              return handler ? handler() : isMyOperation ? '您已操作' : '对方已操作'
            }
            return getSourceMessageText(sourceMessageType)
          }
        }
        const customTypeHandlers: Record<
          Exclude<Api.IM.CustomMessage.ExtType, 'uninterested' | 'conversation_mark_sync'>,
          string
        > = {
          resume: '[简历]',
          exchange_phone: '[手机号]',
          exchange_wechat: '[微信号]',
          interview_appointment: '[面试邀约]',
        }
        return customTypeHandlers[customExts.type] || '自定义消息'
      },
      _default: (msg: any) => `[${msg.type || '未知'}消息]`,
    }
    const handler = messageHandlers[last.type] || messageHandlers._default
    const messageContent = handler(last)
    const statusPrefix = getMessageStatusPrefix(last)
    return statusPrefix ? `「${statusPrefix}」${messageContent}` : messageContent
  }
  const hasConversation = (conversationId: string) => {
    return !!uni.$UIKit.convStore.getConversationById(conversationId)
  }
  /** 设置会话标记 */
  const setConversationMark = async (
    conversationId: string,
    conversationType: 'singleChat' | 'groupChat',
    mark: ConversationMarkType | number,
    syncToRemote: boolean = false,
    sourceMessageType?: Api.IM.CustomMessage.ExtType,
  ) => {
    try {
      const conversation = uni.$UIKit.convStore.getConversationById(conversationId)
      const existingMarks = conversation?.marks || []

      const removePromises = existingMarks
        .filter((existingMark) => existingMark !== mark)
        .map((existingMark) =>
          removeConversationMark(
            conversationId,
            conversationType,
            existingMark as ConversationMarkType,
          ),
        )

      if (removePromises.length > 0) {
        await Promise.allSettled(removePromises)
      }

      if (!existingMarks.includes(mark)) {
        await uni.$UIKit.getChatConn().addConversationMark({
          conversations: [
            {
              conversationId,
              conversationType,
            },
          ],
          mark,
        })
      }
      // 如果需要同步到对方，发送标记同步消息
      if (syncToRemote) {
        await sendConversationMarkSyncMessage(conversationId, mark, sourceMessageType)
      }
    } catch (error) {
      console.error('设置会话标记失败', error)
    }
  }
  /** 发送会话标记同步消息 */
  const sendConversationMarkSyncMessage = async (
    to: string,
    mark: ConversationMarkType | number,
    sourceMessageType?: Api.IM.CustomMessage.ExtType,
  ) => {
    try {
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'conversation_mark_sync',
          status: 1,
          mark,
          syncTime: Date.now(),
          sourceMessageType,
        },
      })
      console.log(`已发送标记同步消息到 ${to}，标记: ${mark}`)
    } catch (error) {
      console.error('发送标记同步消息失败', error)
    }
  }
  /** 处理历史消息中的标记同步 */
  const processHistoryMarkSyncMessages = async () => {
    try {
      const conversations = conversationList.value
      const currentUserId = getIMLoginId.value
      for (const conversation of conversations) {
        const { conversationId } = conversation
        // 获取最近的消息历史（包括离线消息）
        const messages = await uni.$UIKit.getChatConn().getHistoryMessages({
          targetId: conversationId,
          chatType: 'singleChat',
          pageSize: 50, // 检查最近50条消息
        })
        if (messages?.messages?.length) {
          // 找出所有标记同步消息（排除自己发送的）
          const markSyncMessages = messages.messages.filter(
            (msg) =>
              msg.type === 'custom' &&
              msg.customExts?.type === 'conversation_mark_sync' &&
              msg.from !== currentUserId,
          )
          // 处理最新的标记同步消息
          if (markSyncMessages.length > 0) {
            const latestSyncMessage = markSyncMessages[
              markSyncMessages.length - 1
            ] as EasemobChat.CustomMsgBody
            const { mark } = latestSyncMessage.customExts as Api.IM.CustomMessage.ExtInfo & {
              mark: ConversationMarkType
            }
            const currentMarks = conversation.marks || []
            if (!currentMarks.includes(mark)) {
              await setConversationMark(conversationId, 'singleChat', mark, false)
              console.log(`处理历史标记同步消息，会话 ${conversationId} 标记更新为: ${mark}`)
            }
          }
        }
      }
    } catch (error) {
      console.error('处理历史标记同步消息失败:', error)
    }
  }
  /** 移除会话标记 */
  const removeConversationMark = async (
    conversationId: string,
    conversationType: 'singleChat' | 'groupChat',
    mark: ConversationMarkType,
  ) => {
    try {
      await uni.$UIKit.getChatConn().removeConversationMark({
        conversations: [
          {
            conversationId,
            conversationType,
          },
        ],
        mark,
      })
    } catch (error) {
      console.error('移除会话标记失败', error)
    }
  }
  /** 获取指定标记的会话列表 */
  const getConversationsByMark = async (mark: ConversationMarkType, pageSize = 50) => {
    const allConversations: ConversationWithUserInfo[] = []
    const seenIds = new Set<string>() // 1. 优化：提前声明去重Set，避免重复创建
    let cursor = ''

    try {
      while (true) {
        const result = await uni.$UIKit.getChatConn().getServerConversationsByFilter({
          pageSize,
          cursor,
          filter: { mark },
        })
        const { conversations = [], cursor: nextCursor = '' } = result.data || {}
        if (unInterestBlackUserNameList.value.length === 0) {
          const { data: unInterestUserNameList } = await imUnInterestUserNameList()
          unInterestBlackUserNameList.value = unInterestUserNameList
        }
        const validConversations = conversations
          .filter((item) => {
            if (unInterestBlackUserNameList.value.includes(item.conversationId)) {
              return false
            }
            if (seenIds.has(item.conversationId)) {
              return false
            }
            seenIds.add(item.conversationId)
            return true
          })
          .map((item) => {
            const { conversationId } = item
            const user = uni.$UIKit.appUserStore.getUserInfoFromStore(conversationId)
            return {
              ...item,
              ...user,
            } as ConversationWithUserInfo
          })
        allConversations.push(...validConversations)
        if (!nextCursor || validConversations.length === 0) {
          console.log(`获取标记 ${mark} 的会话完成，共 ${allConversations.length} 条`)
          break
        }
        if (allConversations.length >= 1000) {
          console.warn(`会话数量达到上限 1000，停止获取`)
          break
        }
        cursor = nextCursor
      }
      return {
        data: {
          conversations: allConversations,
          cursor: '',
        },
      }
    } catch (error) {
      console.error('获取标记会话失败', error)
      return {
        data: {
          conversations: allConversations,
          cursor: cursor || '',
        },
      }
    }
  }
  /** 根据消息类型自动更新会话标记 */
  const autoUpdateConversationMark = async (
    conversationId: string,
    messageType?: Api.IM.CustomMessage.ExtType,
    status?: Api.IM.CustomMessage.StatusType,
    syncToRemote: boolean = true,
  ) => {
    if (!messageType || status !== 1) return
    try {
      const currentConversation = conversationList.value.find(
        (item) => item.conversationId === conversationId,
      )
      const MARK_PRIORITY = {
        [CONVERSATION_MARKS.NEW_GREETING]: 1,
        [CONVERSATION_MARKS.ONLY_CHAT]: 2,
        [CONVERSATION_MARKS.EXCHANGED]: 3,
        [CONVERSATION_MARKS.INTERVIEWED]: 4,
      }
      const messageToMarkMap: Record<Api.IM.CustomMessage.ExtType, ConversationMarkType | null> = {
        resume: CONVERSATION_MARKS.EXCHANGED,
        exchange_phone: CONVERSATION_MARKS.EXCHANGED,
        exchange_wechat: CONVERSATION_MARKS.EXCHANGED,
        interview_appointment: CONVERSATION_MARKS.INTERVIEWED,
        uninterested: null, // 不感兴趣不设置标记，因为会话会被删除
        conversation_mark_sync: null, // 同步消息不设置标记
      }
      const targetMark = messageToMarkMap[messageType]
      if (!targetMark) return
      const currentMarks = currentConversation?.marks || []
      const targetPriority = MARK_PRIORITY[targetMark]
      const hasHigherPriorityMark = currentMarks.some(
        (mark) => MARK_PRIORITY[mark as ConversationMarkType] > targetPriority,
      )
      if (hasHigherPriorityMark) {
        console.log(`会话 ${conversationId} 已有更高优先级标记，跳过更新`)
        return
      }
      await setConversationMark(conversationId, 'singleChat', targetMark, syncToRemote, messageType)
      console.log(`会话 ${conversationId} 标记已更新为: ${targetMark}`)
    } catch (error) {
      console.error('更新会话标记失败:', error)
    }
  }
  /** 发送即时文本消息 */
  const sendIMTextMessage = async (to: string, msg: string, force: boolean = false) => {
    if (!hasConversation(to) || force) {
      const createMsg = chatSDK.message.create({
        type: 'txt',
        to,
        chatType,
        msg,
      })
      uni.$UIKit.messageStore.sendMessage(createMsg)
      await CommonUtil.pause(500)
      await setConversationMark(to, 'singleChat', CONVERSATION_MARKS.ONLY_CHAT)
    }
  }
  /** 发送自定义消息 */
  const sendCustomMessage = async (message: CustomMessage) => {
    const { to, ...params } = message
    const createMsg = chatSDK.message.create({
      type: 'custom',
      to,
      chatType,
      ...params,
    })
    uni.$UIKit.messageStore.sendMessage(createMsg)
    if (!hasConversation(to)) {
      await CommonUtil.pause(500)
      await setConversationMark(to, 'singleChat', CONVERSATION_MARKS.ONLY_CHAT)
    }
  }
  /** 发送邀约面试 */
  const sendInvitationForInterview = async (to: string, interviewInfo: AnyObject) => {
    const { id = 0, agreeTime: interviewTime = '' } = interviewInfo
    if (!id || !interviewTime) {
      return Promise.reject(new Error('缺少面试信息'))
    }
    try {
      const customExts: Api.IM.CustomMessage.ExtInfo = {
        type: 'interview_appointment',
        status: 0,
        interview_appointment_record_id: id,
        interviewTime,
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts,
      })
      return Promise.resolve(customExts)
    } catch (error) {
      return Promise.reject(new Error('发送邀约面试失败'))
    }
  }
  /** 发送微信号 */
  const sendCustomWeChatCodeMessage = async (to: string, exchange: exchangeCodeExchangeDataInt) => {
    const { wechatCode } = exchange
    const sm4WeChatCode = sm4Encrypt(wechatCode)
    try {
      let exchangeResumeRecordId: number | null = null
      let cUserWechat: string | null = null
      let bUserWechat: string | null = null
      if (!userRoleIsBusiness.value) {
        const { data } = await exchangeCodeExchange(exchange)
        exchangeResumeRecordId = data
        cUserWechat = sm4WeChatCode
      } else {
        const { data } = await hrExchangeCodeExchange(exchange)
        exchangeResumeRecordId = data
        bUserWechat = sm4WeChatCode
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'exchange_wechat',
          status: 0,
          exchange_wechat_record_id: exchangeResumeRecordId,
          cUserWechat,
          bUserWechat,
        },
      })
    } catch (error) {
      return Promise.reject(new Error('发送微信号失败'))
    }
  }
  /** 发送手机号 */
  const sendCustomPhoneMessage = async (to: string, userId: number) => {
    const sm4PhoneCode = sm4Encrypt(userIntel.value.phone)
    try {
      let exchangeResumeRecordId: number | null = null
      let cUserPhone: string | null = null
      let bUserPhone: string | null = null
      if (!userRoleIsBusiness.value) {
        const { data } = await exchangeNumberExchange({
          phone: userIntel.value.phone,
          userId,
        })
        exchangeResumeRecordId = data
        cUserPhone = sm4PhoneCode
      } else {
        const { data } = await hrExchangeNumberExchange({
          phone: userIntel.value.phone,
          userId,
        })
        exchangeResumeRecordId = data
        bUserPhone = sm4PhoneCode
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'exchange_phone',
          status: 0,
          exchange_phone_record_id: exchangeResumeRecordId,
          cUserPhone,
          bUserPhone,
        },
      })
    } catch (error) {
      return Promise.reject(new Error('发送手机号失败'))
    }
  }
  /** 发送/索要简历 */
  const sendCustomResumeMessage = async (to: string, post: AnyObject) => {
    try {
      const {
        id: positionId,
        hxUserInfoVO: { userId: hrId },
      } = post
      let exchangeResumeRecordId: number | null = null
      let cUserResumeLink: string | null = null
      if (!userRoleIsBusiness.value) {
        const { data } = await resumeQueryMyFileResumeList()
        if (!data || !data.length) {
          return Promise.reject(new Error('请先完善简历信息'))
        }
        const [{ fileId: attachmentId, fileUrl: attachmentUrl }] = data
        const { data: resumeId } = await exchangeResumeRecordSendResume({
          attachmentId,
          hrId,
          positionId,
        })
        exchangeResumeRecordId = resumeId
        cUserResumeLink = sm4Encrypt(attachmentUrl)
      } else {
        const { data } = await hrExchangeResumeRecordRequestResume({
          positionId,
          userId: hrId,
        })
        exchangeResumeRecordId = data
      }
      await sendCustomMessage({
        to,
        customEvent: '',
        customExts: {
          type: 'resume',
          status: 0,
          exchange_resume_record_id: exchangeResumeRecordId,
          cUserResumeLink,
        },
      })
    } catch {
      return Promise.reject(new Error('投递失败'))
    }
  }
  /** 打招呼 */
  const sendGreetingMessage = async (
    to: string,
    post: AnyObject = {},
    chat = true,
    force = false,
  ) => {
    try {
      const {
        id: positionId,
        positionName,
        companyId,
        hxUserInfoVO: { userId },
      } = post
      let greetingMsg: string = null
      // 消息模板和会话参数配置
      const roleConfig = {
        [USER_TYPE.HR]: {
          getMessage: (name?: string) =>
            name
              ? `您好，我们正在诚聘${name}，有兴趣聊聊吗？`
              : 'Hi，您好，我们正在诚聘，期待您的加入！',
          getSessionParams: {
            companyId: userIntel.value.companyId,
            hrUserId: userIntel.value.userId,
            positionId,
            userId,
          },
        },
        [USER_TYPE.APPLICANT]: {
          getMessage: (name?: string) =>
            name
              ? `您好，希望和您沟通下${name}`
              : '您好，我对您发布的岗位很感兴趣，希望能进一步了解！',
          getSessionParams: {
            companyId,
            hrUserId: userId,
            positionId,
            userId: userIntel.value.userId,
          },
        },
      }
      const userType = userIntel.value.type
      const { getMessage, getSessionParams } = roleConfig[userType]
      if (getSessionParams.positionId && getSessionParams.companyId) {
        await imSessionRecordSaveSession(getSessionParams, {
          custom: { catch: true },
        })
        const { data } = await sysUserCallQueryOne()
        greetingMsg = data
      }
      await sendIMTextMessage(to, greetingMsg || getMessage(positionName), force)
      if (chat) {
        const url = CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
          type: 'singleChat',
          id: to,
        })
        uni.navigateTo({ url })
      }
    } catch (error) {
      console.error('发送打招呼消息失败', error)
      throw error
    }
  }
  /** 发送简历 */
  const sendResumeMessage = async (to: string, post: AnyObject = {}) => {
    const TOAST_DURATION = 2000
    try {
      await sendCustomResumeMessage(to, post)
      await sendGreetingMessage(to, post, false)
      uni.showToast({ title: '投递成功', icon: 'none', duration: TOAST_DURATION })
      return 1
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '投递失败'
      // uni.showToast({ title: errorMessage, icon: 'none', duration: TOAST_DURATION })
      return 0
    }
  }
  /** 群发简历 */
  const sendMassResumeMessage = async (params: SendMassResumeMessageParams) => {
    let cUserResumeLink: string | null = null
    const { greeting, sendList } = params
    const { data: fileResume } = await resumeQueryMyFileResumeList()
    if (!fileResume || !fileResume.length) {
      return Promise.reject(new Error('请先完善简历信息'))
    }
    const [{ fileId: attachmentId, fileUrl: attachmentUrl }] = fileResume
    cUserResumeLink = sm4Encrypt(attachmentUrl)
    const {
      data: { batchNo, data: sendResumeList },
    } = await exchangeResumeRecordBatchSendResume({
      attachmentId,
      greeting,
      sendList: sendList.map(({ to, ...params }) => params),
    })
    if (!Array.isArray(sendResumeList) || !sendResumeList.length) return 0
    const MAX_CONCURRENT = 3
    const TOAST_DURATION = 2000
    const total = sendResumeList.length
    const isMultiple = total > 1
    const failed: string[] = []
    let timer: ReturnType<typeof setInterval> | null = null
    try {
      uni.showLoading({ title: '投递中...', mask: true })
      if (isMultiple) {
        timer = setInterval(() => {
          uni.showLoading({ title: `投递中 ${total - failed.length}/${total}`, mask: true })
        }, 3000)
      }
      const limit = pLimit(MAX_CONCURRENT)
      const tasks = sendResumeList.map(({ exchangeResumeRecordId, hrUserId }) =>
        limit(async () => {
          const { to } = sendList.find((index) => index.hrUserId === hrUserId)
          try {
            await sendCustomMessage({
              to,
              customEvent: '',
              customExts: {
                type: 'resume',
                status: 0,
                exchange_resume_record_id: exchangeResumeRecordId,
                cUserResumeLink,
              },
            })
            await sendIMTextMessage(to, greeting)
            return { recipient: to, status: 'fulfilled' } as SendResult
          } catch (err) {
            failed.push(to)
            return { recipient: to, status: 'rejected', reason: err } as SendResult
          }
        }),
      )
      const results = await Promise.all(tasks)
      const sent = results.filter((r) => r.status === 'fulfilled').length
      if (timer) clearInterval(timer)
      uni.hideLoading()
      if (sent > 0) {
        await exchangeResumeRecordBatchSendResumeCallback({
          batchNo,
        })
        uni.showToast({ title: '投递成功', icon: 'none', duration: TOAST_DURATION })
      } else {
        uni.showToast({ title: '投递失败', icon: 'none', duration: TOAST_DURATION })
      }
      return sent
    } catch (e) {
      if (timer) clearInterval(timer)
      uni.hideLoading()
      uni.showToast({ title: '投递失败', icon: 'none', duration: TOAST_DURATION })
      return 0
    } finally {
      if (timer) clearInterval(timer)
    }
  }
  /** 删除会话并设置免打扰 */
  const deleteConversation = async (conversationId: string) => {
    const currentConversation = conversationList.value.find(
      (cvs) => cvs.conversationType === 'singleChat' && cvs.conversationId === conversationId,
    )
    if (currentConversation) {
      uni.$UIKit.convStore.setSilentModeForConversation(currentConversation)
    }
    await uni.$UIKit.getChatConn().deleteConversation({
      channel: conversationId,
      chatType: 'singleChat',
      deleteRoam: false,
    })
    const idx = uni.$UIKit.convStore.conversationList.findIndex(
      (cvs) => cvs.conversationType === 'singleChat' && cvs.conversationId === conversationId,
    )
    if (idx > -1) {
      uni.$UIKit.convStore.conversationList.splice(idx, 1)
    }
    loadConversations(true)
  }
  return {
    conversationList,
    totalUnreadCount,
    customCardInfo,
    loadConversations,
    listenForNewMessages,
    listenForPushNotifications,
    closePushNotifications,
    deleteConversation,
    getLastTypeMessage,
    getIMUserInfo,
    getIMUserInfoSelf,
    getCoversationInfo,
    getIMLoginId,
    sendIMTextMessage,
    sendGreetingMessage,
    sendResumeMessage,
    sendMassResumeMessage,
    sendCustomResumeMessage,
    sendCustomPhoneMessage,
    sendCustomWeChatCodeMessage,
    sendInvitationForInterview,
    setConversationMark,
    removeConversationMark,
    getConversationsByMark,
    autoUpdateConversationMark,
    processHistoryMarkSyncMessages,
  }
}
