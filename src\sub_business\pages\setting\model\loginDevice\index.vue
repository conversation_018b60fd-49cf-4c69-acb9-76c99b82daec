<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="登陆账号管理"></CustomNavBar>
    <view class="setting">
      <view class="setting-text m-b-40rpxrpx">
        已下是最近登陆过您账号的设备情况，若发现非本人操作，请及时删除，以保障您的账号安全
      </view>

      <view class="setting-list border-b">
        <view class="flex-between">
          <view class="text-32rpx">ipone 19plus</view>
          <view class="color-8">当前设备</view>
        </view>
        <view class="color-8">2025-05-12 14:31:34</view>
      </view>
      <view class="setting-list border-b">
        <view class="flex-between">
          <view class="text-32rpx">ipone 19plus</view>
        </view>
        <view class="color-8">2025-05-12 14:31:34</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 40rpx 40rpx;
  .setting-list {
    padding: 30rpx 0rpx;
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx 40rpx;
}

::v-deep .u-cell__value {
  font-size: 28rpx;
  color: #888888;
}

::v-deep .u-cell__label--large {
  font-size: 28rpx;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}

.setting-text {
  font-size: 24rpx;
  color: #888888;
}
</style>
