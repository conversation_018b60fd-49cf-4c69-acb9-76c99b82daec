export interface payPropQueryUsingCountListInt {
  [key: string]: string | number
}
export interface payPropQueryUsingCountListDataInt {}

export interface payPropQueryUsingPositionListDataInt {
  /** 道具ID */
  propId: number
}
export interface payPropQueryUsingPositionListInt {
  /** 到期时间 */
  endTime?: string
  /** 岗位主键id */
  id?: number
  /** 岗位名称 */
  positionName?: string
  /** 例如12薪资 */
  salaryMonths?: number
  /** 薪资范围-最低0为不限 */
  workSalaryBegin?: number
  /** 薪资范围-最高0为不限 */
  workSalaryEnd?: number
}
