.message-input-wrap {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.send-input {
  font-size: 16px;
  width: 100%;
  box-sizing: unset;
}

.send-input input {
  height: 100rpx;
}

.icon-wrap {
  width: 25px;
  height: 25px;
  flex-shrink: 0;
  margin: 0 4px;
  user-select: none;
}

.icon {
  width: 25px;
  height: 25px;
}

.audio-icon {
  background: url('../../../../assets/icon/audioButton.png') no-repeat center;
  background-size: 100% 100%;
}

.emoji-icon {
  background: url('../../../../assets/icon/emoji.png') no-repeat center;
  background-size: 100% 100%;
}

.plus-icon {
  background: url('../../../../assets/icon/plus.png') no-repeat center;
  background-size: 100% 100%;
}

.send-icon {
  background: url('../../../../assets/icon/send.png') no-repeat center;
  background-size: 100% 100%;
}

.prevent-event {
  pointer-events: none;
}
