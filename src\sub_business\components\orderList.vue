<template>
  <view
    @click="handleClick"
    class="shadow-[0px_4px_13.5px_0px_rgba(0,0,0,0.15)] px-40rpx py-30rpx bg-white rounded-20rpx relative m-b-20rpx"
  >
    <view class="flex items-baseline">
      <view class="c-#000 text-32rpx font500 p-b-15rpx">
        {{ item.goodsDesc }}
      </view>
      <view class="c-#666 text-28rpx font500 m-l-10rpx">￥{{ item.actualAmount / 1000 }}</view>
    </view>

    <view class="flex items-center p-b-10rpx">
      <view class="c-#333 text-24rpx">岗位：</view>
      <view class="c-#333 text-24rpx">{{ item.positionName }}</view>
    </view>
    <view class="flex items-center p-b-10rpx">
      <view class="c-#333 text-24rpx">订单号：</view>
      <view class="c-#333 text-24rpx">{{ item.outTradeNo }}</view>
    </view>
    <view class="flex items-center p-b-10rpx">
      <view class="c-#333 text-24rpx">下单时间：</view>
      <view class="c-#333 text-24rpx">{{ item.dealCreateTime }}</view>
    </view>
    <view class="flex items-center">
      <view class="c-#333 text-24rpx">到期时间：</view>
      <view class="c-#333 text-24rpx">{{ item.finishTime }}</view>
    </view>
    <view class="flex items-center">
      <view class="absolute top-30rpx right-40rpx c-#555 text-32rpx">已完成</view>
    </view>
    <!-- <view
        v-else-if="item.dealState === 0"
        class="absolute top-30rpx right-40rpx flex items-baseline"
      >
        <wd-count-down :time="time" />
        <view class="m-l-10rpx c-#555 text-32rpx c-#ff4545">待付款</view>
      </view>
    </view>
    <view
      v-if="item.dealState === 0"
      class="bg-#5882FF rounded-10rpx h-70rpx c-#fff w-180rpx text-26rpx text-center line-height-70rpx absolute bottom-30rpx right-40rpx"
    >
      立即支付
    </view> -->
  </view>
</template>

<script setup lang="ts">
// const time = ref(60 * 60 * 24 * 30)
const props = defineProps({
  item: {
    type: Object,
    default: () => {},
  },
})
const handleClick = () => {
  uni.navigateTo({
    url: `/sub_business/pages/order/detail?dealId=${props.item.dealId}`,
  })
}
</script>

<style lang="scss" scoped>
:deep(.wd-count-down) {
  font-size: 24rpx !important;
  color: #ff4545 !important;
}
</style>
