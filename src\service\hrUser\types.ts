import { Gender } from '@/enum'

export interface hrUserInfoInt {
  /** 公司id */
  companyId?: number
  /** 公司name */
  companyName?: string
  /** 公司简称 */
  companyShortName?: string
  /** 用户性别。1：男；2：女；（默认）0：未知；设置为其他值无效。 */
  gender?: Gender
  /** 头像审批状态 0 是待审核 1 是审批通过 */
  headImgStatus?: Api.Common.EnableStatus
  /** 头像URL */
  headImgUrl?: string
  /** HR岗位 */
  hrPosition?: string
  /** 手机号(用于交换) */
  phone?: string
  /** 真实姓名 */
  trueName?: string
  /** 用户id */
  userId?: number
  /** 微信号(用于交换) */
  wxCode?: string
}
