package com.yourcompany.pdfviewer;

import android.content.Context;
import android.widget.FrameLayout;
import com.github.barteksc.pdfviewer.PDFView;

public class PdfViewer extends FrameLayout {

    PDFView pdfView;

    public PdfViewer(Context context) {
        super(context);
        pdfView = new PDFView(context, null);
        this.addView(pdfView, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
    }

    public void setSrc(String path) {
        if (path == null || path.isEmpty()) return;
        java.io.File file = new java.io.File(path);
        if (file.exists()) {
            pdfView.fromFile(file).enableSwipe(true).swipeHorizontal(false).load();
        }
    }
}