/**
 * 地理位置坐标系转换工具
 *
 * WGS84: GPS全球定位系统使用的标准坐标系
 * GCJ02: 中国国测局制定的坐标系统，俗称"火星坐标系"
 * BD09: 百度地图使用的坐标系统
 */
interface Coordinate {
  latitude: number
  longitude: number
}

/** 创建坐标对象并保留5位小数 */
function createCoord(lat: number, lng: number): Coordinate {
  return {
    latitude: Number(lat.toFixed(5)),
    longitude: Number(lng.toFixed(5)),
  }
}

/** 是否在中国 */
export function isInChina(lat: number, lng: number): boolean {
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271
}

/** WGS84 -> GCJ02 */
export function wgs84ToGcj02(latitude: number, longitude: number): Coordinate {
  if (!isInChina(latitude, longitude)) {
    return { latitude, longitude }
  }

  // 转换参数
  const a = 6378245.0 // 长半轴
  const ee = 0.00669342162296594 // 偏心率平方

  let dLat = transformLat(longitude - 105.0, latitude - 35.0)
  let dLng = transformLng(longitude - 105.0, latitude - 35.0)

  const radLat = (latitude / 180.0) * Math.PI
  let magic = Math.sin(radLat)
  magic = 1 - ee * magic * magic

  const sqrtMagic = Math.sqrt(magic)
  dLat = (dLat * 180.0) / (((a * (1 - ee)) / (magic * sqrtMagic)) * Math.PI)
  dLng = (dLng * 180.0) / ((a / sqrtMagic) * Math.cos(radLat) * Math.PI)

  return createCoord(latitude + dLat, longitude + dLng)
}

/** GCJ02 -> WGS84 */
export function gcj02ToWgs84(latitude: number, longitude: number): Coordinate {
  if (!isInChina(latitude, longitude)) {
    return { latitude, longitude }
  }
  let wgsLat = latitude
  let wgsLng = longitude
  for (let i = 0; i < 2; i++) {
    const gcjCoord = wgs84ToGcj02(wgsLat, wgsLng)
    const dLat = gcjCoord.latitude - latitude
    const dLng = gcjCoord.longitude - longitude
    wgsLat = latitude - dLat
    wgsLng = longitude - dLng
  }
  return createCoord(wgsLat, wgsLng)
}
function transformLat(x: number, y: number): number {
  let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x))
  ret += ((20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0) / 3.0
  ret += ((20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin((y / 3.0) * Math.PI)) * 2.0) / 3.0
  ret +=
    ((160.0 * Math.sin((y / 12.0) * Math.PI) + 320 * Math.sin((y * Math.PI) / 30.0)) * 2.0) / 3.0
  return ret
}
function transformLng(x: number, y: number): number {
  let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x))
  ret += ((20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0) / 3.0
  ret += ((20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin((x / 3.0) * Math.PI)) * 2.0) / 3.0
  ret +=
    ((150.0 * Math.sin((x / 12.0) * Math.PI) + 300.0 * Math.sin((x / 30.0) * Math.PI)) * 2.0) / 3.0
  return ret
}

/** BD09 -> GCJ02 */
export function bd09ToGcj02(latitude: number, longitude: number): Coordinate {
  if (!isInChina(latitude, longitude)) {
    return { latitude, longitude }
  }
  const xPi = (Math.PI * 3000.0) / 180.0
  const x = longitude - 0.0065
  const y = latitude - 0.006
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * xPi)
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * xPi)
  const gcjLng = z * Math.cos(theta)
  const gcjLat = z * Math.sin(theta)

  return createCoord(gcjLat, gcjLng)
}

/** GCJ02 -> BD09 */
export function gcj02ToBd09(latitude: number, longitude: number): Coordinate {
  if (!isInChina(latitude, longitude)) {
    return { latitude, longitude }
  }
  const xPi = (Math.PI * 3000.0) / 180.0
  const z =
    Math.sqrt(longitude * longitude + latitude * latitude) + 0.00002 * Math.sin(latitude * xPi)
  const theta = Math.atan2(latitude, longitude) + 0.000003 * Math.cos(longitude * xPi)
  const bdLng = z * Math.cos(theta) + 0.0065
  const bdLat = z * Math.sin(theta) + 0.006

  return createCoord(bdLat, bdLng)
}
/** WGS84 -> BD09 */
export function wgs84ToBd09(latitude: number, longitude: number): Coordinate {
  if (!isInChina(latitude, longitude)) {
    return { latitude, longitude }
  }
  const gcj02Coord = wgs84ToGcj02(latitude, longitude)
  return gcj02ToBd09(gcj02Coord.latitude, gcj02Coord.longitude)
}
