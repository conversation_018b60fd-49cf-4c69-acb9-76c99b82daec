<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="掌握技能">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageList">
      <view class="flex-c">
        <view class="pageList-input">
          <wd-input
            no-border
            type="text"
            focus
            v-model="valueTag"
            placeholder="请输入技能名称"
            class="flex-1 input-w"
            :maxlength="20"
            show-word-limit
          />
        </view>
        <view class="submitClass" @click="submitTag">
          <wd-icon name="add" size="22px" color="#fff"></wd-icon>
        </view>
      </view>
      <view class="btnList text-24rpx" v-if="listArry.length > 0">
        <view class="btnList-btn" v-for="(item, index) in listArry" :key="index">
          <view class="flex items-center justify-center">
            <view class="text-24rpx">{{ item }}</view>
            <wd-icon name="close-normal" size="16px" @click="del(index)"></wd-icon>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn_fixed" @click="submit">
        <view class="btn_box">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import isEqual from 'lodash/isEqual'
import { resumeSkillCertificateUpdate } from '@/interPost/resume'
import { useMessage } from 'wot-design-uni'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// value
const valueTag = ref('')
// 列表
const listArry = ref([])
const fromDataInit = ref([])
const message = useMessage()
// 表单初列表

// id
const id = ref(null)
// 新增、编辑表示
const isAdd = ref(null)
// 技能编辑
const objItem = ref()
// 添加tag
const submitTag = () => {
  if (valueTag.value.trim()) {
    listArry.value.push(valueTag.value)
    valueTag.value = ''
  }
}
onLoad((options) => {
  id.value = options.id
  isAdd.value = options.isAdd
  const ele = JSON.parse(decodeURIComponent(options.item))
  fromDataInit.value = ele
  listArry.value = JSON.parse(JSON.stringify(ele))
})
// 删除
const del = (index: number) => {
  console.log(index, 'index===')
  listArry.value.splice(index, 1)
}
// 返回
const back = () => {
  console.log(listArry.value)
  console.log(fromDataInit.value)
  if (isEqual(listArry.value, fromDataInit.value)) {
    uni.navigateBack()
  } else {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  }
}
// 提交
const submit = async () => {
  console.log(listArry.value, 'listArry.value')
  if (listArry.value.length < 0) {
    uni.showToast({
      title: '请输入技能名称',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await resumeSkillCertificateUpdate({
    id: id.value,
    skills: listArry.value,
  })
  if (res.code === 0) {
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }

  console.log(res, 'res====')
}
</script>

<style lang="scss" scoped>
.pageList-input {
  width: calc(100% - 100rpx);
  padding: 20rpx 20rpx;
  background: #fff;
  border-radius: 20rpx !important;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
.btnList {
  margin: 60rpx 0rpx 40rpx;
  .btnList-btn {
    display: inline-block;
    padding: 8rpx 50rpx;
    margin-right: 20rpx;
    margin-bottom: 30rpx;
    color: #5378ff;
    border: 2rpx dashed #5378ff;
    border-radius: 10rpx;
  }
}
.pageList {
  padding: 40rpx 60rpx;
}
.submitClass {
  width: 90rpx;
  height: 90rpx;
  padding: 0rpx 20rpx;
  margin: auto;
  margin-left: 20rpx;
  line-height: 90rpx;
  text-align: center;
  background: rgba(83, 120, 255, 1);
  border-radius: 20rpx !important;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 40rpx 60rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 30rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
