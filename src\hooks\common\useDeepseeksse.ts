// 本地连接
import { ref } from 'vue'
import { useUserInfo } from '@/hooks/common/useUserInfo'
const baseUrl = import.meta.env.VITE_SERVER_BASEURL
const { sysAppPlatform } = useSystemInfo()

export const useDeepseeksse = () => {
  const { getToken } = useUserInfo()

  // sse实例
  const chatSSEClientRef = ref(null)
  const loading = ref(false)
  const openLoading = ref(false)

  const openCore = (response: any) => {
    openLoading.value = false
    console.log('open sse：', response)
  }
  const errorCore = (err: any) => {
    console.log('error sse：', err)
  }

  const finishCore = () => {
    console.log('finish sse')
    loading.value = false
  }

  const messageCore = (msg: any) => {
    console.log('message sse：', msg)
    // 这里可以自定义处理逻辑，比如 dataList.value[0].content += `${msg.data}`
  }

  const start = (url: string, message: string, position: string, number: number) => {
    const appPlatform = sysAppPlatform.value
    if (appPlatform === 'ios') {
      url = url + '?token=' + getToken()
    }
    console.log(url, 'url========')
    if (chatSSEClientRef.value && chatSSEClientRef.value.startChat) {
      chatSSEClientRef.value.startChat({
        url: baseUrl + url,
        // 请求头
        headers: {
          token: getToken(),
        },
        // 默认为 post
        method: 'post',
        body: {
          stream: true,
          model: 'deepseek-chat',
          message,
          position,
          number,
        },
      })
    }
  }

  const stop = () => {
    loading.value = false
    if (chatSSEClientRef.value && chatSSEClientRef.value.stop) {
      chatSSEClientRef.value.stop()
    }
  }

  return {
    chatSSEClientRef,
    start,
    stop,
    openCore,
    errorCore,
    finishCore,
    messageCore,
  }
}
