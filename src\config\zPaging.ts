/** z-padding 组件的初始化全局配置 */
export default {
  install() {
    if (!uni || typeof uni !== 'object') return
    uni.$zp = {
      config: {
        'default-page-size': 15,
        'loading-more-no-more-text': '没有更多数据啦~',
        'show-loading-more-no-more-line': false,
        'empty-view-text': '暂无内容',
        'loading-more-title-custom-style': {
          color: '#808080',
          fontSize: '26rpx',
        },
        auto: false,
        'auto-scroll-to-top-when-reload': true,
        'auto-clean-list-when-reload': false,
        'show-scrollbar': false,
        'refresher-end-bounce-enabled': false,
        'show-refresher-update-time': true,
      },
    }
  },
}
