<template>
  <view class="box_width" :style="fixed ? 'position: fixed;bottom: 52rpx' : ''">
    <view class="box_padding-1">
      <view
        v-for="(item, index) in dataListTop"
        :key="index"
        @click="handerOPen(index)"
        class="box_font-1 text-24rpx inline-item"
      >
        {{ item.label }}
      </view>
    </view>
    <view class="box_padding">
      <view class="box_flex" v-for="(item, index) in dataList" :key="index" @click="hander(index)">
        <view class="box_font">
          {{ item.label }}
        </view>
      </view>
    </view>
    <wd-popup
      v-model="show"
      position="bottom"
      closable
      custom-style="border-radius:32rpx 32rpx 0rpx 0rpx;padding:60rpx 40rpx 160rpx;"
    >
      <view class="">
        <view class="subText p-b-20rpx">举报电话</view>
        <view class="mainText font-w-500">400-965-9675</view>
      </view>
    </wd-popup>
    <wd-popup
      v-model="show1"
      position="bottom"
      closable
      custom-style="border-radius:32rpx 32rpx 0rpx 0rpx;padding:60rpx 40rpx 160rpx;"
    >
      <view class="">
        <view class="subText p-b-20rpx">法律顾问</view>
        <view class="mainText font-w-500">重庆立标律师事务所</view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
defineProps({
  fixed: {
    type: Boolean,
    default: true,
  },
})
const dataList = ref([
  {
    value: 1,
    label: '举报电话',
  },
  {
    value: 2,
    label: '人力资源服务许可证',
  },
  {
    value: 1,
    label: '营业执照',
  },
])
const dataListTop = ref([
  {
    value: 0,
    label: '法律顾问',
  },
  {
    value: 1,
    label: '渝ICP备**********号-2A',
  },
  {
    value: 2,
    label: '渝B2-20250575',
  },
])
const show = ref(false)
const show1 = ref(false)
const hander = (index: number) => {
  if (index === 0) {
    show.value = true
  }
  if (index === 1) {
    uni.navigateTo({
      url: '/setting/certificateImage/humanResources',
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/setting/certificateImage/businesslicense',
    })
  }
}
const handerOPen = (index: number) => {
  if (index === 0) {
    show1.value = true
  }
  if (index === 1) {
    uni.navigateTo({
      url:
        '/loginSetting/Externalfiling/index?url=' +
        encodeURIComponent('https://beian.miit.gov.cn/'),
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/setting/certificateImage/TelServices',
    })
  }
}
</script>

<style scoped>
.box_flex-1 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.custom-txt {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 400rpx;
  height: 400rpx;
  font-size: 40rpx;
  color: black;
  border-radius: 32rpx;
}
.box_width {
  width: 100%;
}

.box_padding {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 10rpx 80rpx;
  padding-bottom: 0;
}
.box_padding-1 {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10rpx 20rpx;
  padding-bottom: 0;
  overflow: hidden;
}

.box_flex {
  /* width: 100%; */
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.box_font {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 44rpx;
  color: #666;
}
.box_font-1 {
  font-size: 22rpx;
  font-weight: 400;
  line-height: 44rpx;
  color: #666;
}

.inline-item {
  flex-shrink: 0;
  min-width: 0;
  margin-right: 26rpx;
  white-space: nowrap;
}

.inline-item:last-child {
  margin-right: 0;
}
</style>
