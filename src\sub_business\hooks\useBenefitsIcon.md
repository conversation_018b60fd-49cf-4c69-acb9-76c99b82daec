# useBenefitsIcon Hook 使用说明

## 概述

`useBenefitsIcon` 是一个用于根据福利待遇名称动态匹配对应图标的 Vue 3 Composition API Hook。它提供了一套完整的福利待遇图标映射系统，支持20种常见的福利待遇类型。

## 功能特性

- 🎯 **智能匹配**: 根据福利待遇名称自动匹配对应图标
- 🔄 **动态映射**: 支持动态添加和更新图标映射关系
- 📦 **批量处理**: 支持批量获取福利待遇图标列表
- 🛡️ **类型安全**: 完整的 TypeScript 类型支持
- 🎨 **默认图标**: 未匹配的福利待遇使用默认图标

## 支持的福利待遇类型

| 福利待遇 | 对应图标 |
|---------|---------|
| 年终奖 | benefits_1.png |
| 定期团建 | benefits_2.png |
| 零食下午茶 | benefits_3.png |
| 节日福利 | benefits_4.png |
| 生日福利 | benefits_5.png |
| 五险一金 | benefits_6.png |
| 带薪年假 | benefits_7.png |
| 员工食堂 | benefits_8.png |
| 定期体检 | benefits_9.png |
| 交通补贴 | benefits_10.png |
| 通讯补贴 | benefits_11.png |
| 餐饮补贴 | benefits_12.png |
| 住房补贴 | benefits_13.png |
| 免费工作餐 | benefits_14.png |
| 提供住宿 | benefits_15.png |
| 免费班车 | benefits_16.png |
| 岗位晋升 | benefits_17.png |
| 年度调薪 | benefits_18.png |
| 高温补贴 | benefits_20.png |
| 弹性工作 | benefits_29.png |

## 基本使用

### 1. 导入 Hook

```typescript
import { useBenefitsIcon } from '@/sub_business/hooks/useBenefitsIcon'
```

### 2. 在组件中使用

```vue
<template>
  <view class="benefits-list">
    <view 
      v-for="(item, index) in benefitsList" 
      :key="index"
      class="benefit-item"
    >
      <image 
        :src="getBenefitsIcon(item.welfareTreatment)" 
        class="benefit-icon"
      />
      <text>{{ item.welfareTreatment }}</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useBenefitsIcon } from '@/sub_business/hooks/useBenefitsIcon'

// 使用 Hook
const { getBenefitsIcon } = useBenefitsIcon()

// 示例数据
const benefitsList = ref([
  { welfareTreatment: '年终奖' },
  { welfareTreatment: '五险一金' },
  { welfareTreatment: '带薪年假' },
  { welfareTreatment: '员工食堂' }
])
</script>
```

## API 参考

### getBenefitsIcon(welfareTreatment: string): string

根据福利待遇名称获取对应图标路径。

**参数:**
- `welfareTreatment`: 福利待遇名称

**返回值:**
- 对应的图标路径，如果没有匹配则返回默认图标

**示例:**
```typescript
const iconPath = getBenefitsIcon('年终奖') // 返回 benefits_1.png 的路径
const defaultIcon = getBenefitsIcon('未知福利') // 返回 benefits_custom.png 的路径
```

### getBenefitsIconList(benefitsList: Array<{ welfareTreatment: string }>): string[]

批量获取福利待遇图标列表。

**参数:**
- `benefitsList`: 福利待遇对象数组

**返回值:**
- 对应的图标路径数组

**示例:**
```typescript
const benefits = [
  { welfareTreatment: '年终奖' },
  { welfareTreatment: '五险一金' }
]
const iconPaths = getBenefitsIconList(benefits)
// 返回: [benefits_1.png的路径, benefits_6.png的路径]
```

### hasBenefitsIcon(welfareTreatment: string): boolean

检查是否有对应的图标。

**参数:**
- `welfareTreatment`: 福利待遇名称

**返回值:**
- 是否有对应的图标

**示例:**
```typescript
const hasIcon = hasBenefitsIcon('年终奖') // true
const hasCustomIcon = hasBenefitsIcon('未知福利') // false
```

### setBenefitsIcon(welfareTreatment: string, iconPath: string): void

添加或更新福利待遇图标映射。

**参数:**
- `welfareTreatment`: 福利待遇名称
- `iconPath`: 图标路径

**示例:**
```typescript
setBenefitsIcon('新福利', '/path/to/new-icon.png')
```

### getAllMappedBenefits: ComputedRef<string[]>

获取所有已映射的福利待遇名称。

**返回值:**
- 福利待遇名称数组的计算属性

**示例:**
```typescript
const mappedBenefits = getAllMappedBenefits.value
// 返回: ['年终奖', '定期团建', '零食下午茶', ...]
```

## 实际应用场景

### 1. 公司详情页面福利展示

已在 `src/resumeRelated/company/index.vue` 中实现，用于显示公司福利待遇列表。

### 2. 职位详情页面福利展示

可以在职位详情页面的福利待遇部分使用：

```vue
<view class="benefit-item" v-for="benefit in positionBenefits" :key="benefit">
  <image :src="getBenefitsIcon(benefit)" class="benefit-icon" />
  <text>{{ benefit }}</text>
</view>
```

### 3. 福利选择器组件

在福利待遇选择组件中显示图标：

```vue
<view class="benefit-selector">
  <view 
    v-for="benefit in availableBenefits" 
    :key="benefit.welfareTreatment"
    class="benefit-option"
    @click="selectBenefit(benefit)"
  >
    <image :src="getBenefitsIcon(benefit.welfareTreatment)" />
    <text>{{ benefit.welfareTreatment }}</text>
  </view>
</view>
```

## 注意事项

1. **图标资源**: 确保所有图标文件都存在于 `@/resumeRelated/img/benefits/` 目录下
2. **默认图标**: 未匹配的福利待遇会使用 `benefits_custom.png` 作为默认图标
3. **性能优化**: Hook 使用 `ref` 和 `computed` 进行响应式管理，确保良好的性能表现
4. **扩展性**: 可以通过 `setBenefitsIcon` 方法动态添加新的福利待遇图标映射

## 更新日志

- **v1.0.0**: 初始版本，支持20种常见福利待遇图标映射
