import { CommonUtil } from 'wot-design-uni'
import { currRoute } from '@/utils'
interface shareParamsInt extends Omit<Page.CustomShareContent, 'url'> {
  url?: string
  query?: AnyObject
}
interface shareInt extends shareParamsInt {
  hideShareApp?: boolean
}

/** 分享hooks */
export const useShare = (
  option: shareInt = {
    hideShareApp: false,
  },
) => {
  const { hideShareApp, ...optionParams } = option
  const shareParams = ref<Page.CustomShareContent>(optionParams)
  if (hideShareApp) {
    uni.hideShareMenu({
      hideShareItems: ['shareAppMessage'],
    })
  }
  const shareSetParams = async (
    params: shareParamsInt = {
      query: {},
    },
  ) => {
    const { query, url, ...share } = {
      ...shareParams.value,
      ...params,
    }
    const { path } = currRoute()
    shareParams.value = {
      ...share,
      path: CommonUtil.buildUrlWithParams(url || path, query),
    }
  }
  /** 分享参数 */
  const shareApp = () => {
    onShareAppMessage(() => {
      return shareParams.value
    })
  }
  return {
    onShareAppMessage: shareApp,
    shareSetParams,
  }
}
