import { POSTPaging, POST } from '../index'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import {
  hrResumeResumeUserSeniorListDataInt,
  hrResumeQueryIMCardInfoByIdDataInt,
  hrResumeQueryIMCardInfoByIdInt,
  hrResumeQuickCardInfoDataInt,
  hrResumeQuickCardInfoInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** 岗位下拉选数据查询接口 */
export const hrResumeResumeUserSeniorList = (
  data: Api.Request.IResPagingDataParamsInt<hrResumeResumeUserSeniorListDataInt>,
  config?: HttpRequestConfig,
) =>
  POSTPaging<hrIndexResumeUserListInt>(
    '/easyzhipin-api/hrResume/resumeUserSeniorList',
    data,
    config,
  )
/** 岗位下拉选数据查询接口 */
export const hrResumeQueryIMCardInfoById = (
  data: hrResumeQueryIMCardInfoByIdDataInt,
  config?: HttpRequestConfig,
) =>
  POST<hrResumeQueryIMCardInfoByIdInt>('/easyzhipin-api/hrResume/queryIMCardInfoById', data, config)

/** 获取IM卡片信息 */
export const hrResumeQuickCardInfo = (
  data: hrResumeQuickCardInfoDataInt,
  config?: HttpRequestConfig,
) => POST<hrResumeQuickCardInfoInt>('/easyzhipin-api/hrResume/quickCardInfo', data, config)
