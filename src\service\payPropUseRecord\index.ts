import { POSTPaging, POST } from '../index'
import { HttpRequestConfig } from 'luch-request'
import { pagingDataInt, detailId } from './types'

/** 道具列表 */
export const payPropUseRecord = (data: pagingDataInt, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/payPropUseRecord/queryList', data, config)

// 道具详情
export const payPropUseDetail = (data: detailId, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/payPropUseRecord/detail', data, config)

/** 可视化招聘数据 */
export const payPropUseRecordView = (data: any, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrResume/searchView', data, config)

/** 表格招聘数据 */
export const payPropUseRecordTable = (data?: any, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrResume/searchViewTotal', data, config)
