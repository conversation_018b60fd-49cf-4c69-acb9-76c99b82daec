<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    @query="queryList"
    :paging-style="pageStyle"
    safe-area-inset-bottom
    empty-view-text="未添加黑名单？"
    :empty-view-img="security"
  >
    <template #top>
      <CustomNavBar title="黑名单"></CustomNavBar>
    </template>
    <view class="flex flex-col">
      <mylist
        v-for="(item, index) in pageData"
        :key="index"
        :list="item"
        class="border-b border-#E0E0E0 border-b-solid"
      />
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import mylist from '@/sub_business/components/mylist.vue'
import { imBlackList } from '@/service/imBlackList'
import security from '@/sub_business/static/setting/security_shield.png'
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const list = ref({})
const params = reactive({
  entity: {},
  orderBy: {},
})

const queryList = async (page: number, size: number) => {
  pageSetInfo(page, size)
  const res: any = await imBlackList({
    ...params,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    pagingRef.value.complete(res.data?.list)
  }
}

onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.empty-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 240rpx;

  .empty-img {
    width: 220rpx;
    height: 220rpx;
  }
  .empty-text {
    margin-top: 30rpx;
    font-size: 32rpx;
    color: #333333;
  }
}
</style>
