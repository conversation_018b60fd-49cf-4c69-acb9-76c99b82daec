<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col bg-img">
    <CustomNavBar title="行业"></CustomNavBar>

    <!-- 搜索区域 -->
    <view class="flex-shrink-0 border-b border-gray-200">
      <view class="content_flex">
        <view class="content_search">
          <view class="content_search_bg">
            <view class="content_search_left">
              <image src="/static/img/<EMAIL>" mode="aspectFill"></image>
            </view>
            <view class="content_search_right">
              <wd-input
                no-border
                placeholder="搜索您想要的内容"
                v-model="keyword"
                confirm-type="search"
                @confirm="confirmSearch"
              ></wd-input>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex flex-1 min-h-0">
      <!-- 左侧父级分类 -->
      <scroll-view scroll-y class="w-230rpx flex-shrink-0">
        <view v-for="(parent, pIndex) in industryList" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parent.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子级内容 -->
      <scroll-view scroll-y class="flex-1 min-w-0">
        <view>
          <view class="page-list-right-p">
            <view class="page-tag-list">
              <view
                v-for="(industry, cIndex) in currentChildren"
                :key="cIndex"
                class="tag-select-r"
                :class="industry.active ? 'myStyle-box' : ''"
                @click="selectIndustry(cIndex)"
              >
                {{ industry.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import industryDataList from '@/utils/json/industry.json'
import { useLoginStore } from '@/store'

const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const industryList = ref([])
const originalIndustryList = ref([])
const isSearching = ref(false)

const currentChildren = computed(() => {
  return industryList.value[activeFIndex.value]?.childerIndustryData || []
})

const industrySearch = (searchKeyword: string) => {
  if (!searchKeyword.trim()) return originalIndustryList.value

  const keyword = searchKeyword.toLowerCase().trim()
  const results = []

  originalIndustryList.value.forEach((category) => {
    const categoryMatches = category.name.toLowerCase().includes(keyword)
    const matchedIndustries =
      category.childerIndustryData?.filter((industry) =>
        industry.name.toLowerCase().includes(keyword),
      ) || []

    if (categoryMatches || matchedIndustries.length > 0) {
      results.push({
        ...category,
        childerIndustryData: categoryMatches ? category.childerIndustryData : matchedIndustries,
        _matchType: categoryMatches ? 'category' : 'industry',
      })
    }
  })

  return results.sort((a, b) => {
    if (a._matchType === 'category' && b._matchType !== 'category') return -1
    if (b._matchType === 'category' && a._matchType !== 'category') return 1
    return 0
  })
}

const confirmSearch = () => {
  const searchKeyword = keyword.value.trim()
  if (searchKeyword) {
    isSearching.value = true
    const searchResults = industrySearch(searchKeyword)
    industryList.value = searchResults
    if (searchResults.length > 0) {
      activeFIndex.value = 0
    }
  } else {
    clearSearch()
  }
}

const clearSearch = () => {
  isSearching.value = false
  industryList.value = originalIndustryList.value
  keyword.value = ''
  initSelectedData()
}

// 监听搜索关键词变化
watch(keyword, (newKeyword) => {
  if (!newKeyword.trim() && isSearching.value) {
    clearSearch()
  }
})

const activeF = (index) => {
  activeFIndex.value = index
}

const resetAllActive = () => {
  for (const parent of industryList.value) {
    for (const child of parent.childerIndustryData || []) {
      child.active = false
    }
  }
}

// 构建行业选择对象
const buildIndustrySelection = (categoryIndex, industryIndex) => {
  const category = industryList.value[categoryIndex]
  const industry = category.childerIndustryData[industryIndex]
  const result = reactive({
    industryName: industry.name,
    industryCode: industry.code,
  })
  return result
}

// 完成行业选择
const completeIndustrySelection = (industryObj) => {
  console.log(industryObj, 'industryObj=====')
  loginStore.setindustryObj(industryObj)
  uni.navigateBack()
}

// 选择行业
const selectIndustry = (industryIndex) => {
  resetAllActive()

  const targetIndustry = industryList.value[activeFIndex.value].childerIndustryData[industryIndex]

  targetIndustry.active = true

  const industrySelection = buildIndustrySelection(activeFIndex.value, industryIndex)
  completeIndustrySelection(industrySelection)
}

const initializeIndustryData = () => {
  return CommonUtil.deepClone(industryDataList.industryData)
}

const initSelectedData = () => {
  console.log(loginStore.industryObj, 'loginStore.industryObj=====里面的')
  const selectedCode = loginStore.industryObj.industryCode
  console.log(selectedCode, 'selectedCode=====')
  // if (!selectedCode) return
  console.log(industryList.value, 'industryList.value=====')
  for (let pIndex = 0; pIndex < industryList.value.length; pIndex++) {
    const category = industryList.value[pIndex]
    const industry = category.childerIndustryData?.find((i) => i.code === selectedCode)
    if (industry) {
      console.log(industry, 'industry=====')
      industry.active = true
      activeFIndex.value = pIndex
      return
    }
  }
}

onLoad(() => {
  const initializedData = initializeIndustryData()
  originalIndustryList.value = initializedData
  industryList.value = initializedData
  initSelectedData()
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  width: 100%;
  background-color: transparent !important;
  .wd-input__placeholder {
    font-size: 28rpx !important;
    color: #333333 !important;
  }
  .wd-input__inner {
    font-size: 28rpx !important;
    font-weight: 500;
    color: #333333 !important;
  }
}
.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 25rpx 40rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #333333;
      background: #fff;
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('../img/Mask_group(2).png');
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
}

.activeBg {
  font-weight: 500;
  color: #1160ff;
  background: transparent;
}

.normalBg {
  background: #f5f4f4;
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 230rpx;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      // 相邻元素的圆角
      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 26rpx;
    color: #333333;
    text-align: center;
    background: #ffffff;
    border-radius: 10rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
