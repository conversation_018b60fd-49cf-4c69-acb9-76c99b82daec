;(function (g, fn) {
  var version = '1.3.18',
    pdfjsVersion = '2.3.200'
  console.log('pdfh5.js v' + version + '  https://www.gjtool.cn')
  if (typeof require !== 'undefined') {
    if (g.$ === undefined) {
      g.$ = require('./jquery-1.11.3.min.js')
    }
    g.pdfjsWorker = require('./pdf.worker.js')
    g.pdfjsLib = require('./pdf.js')
  }
  var pdfjsLib = g.pdfjsLib,
    $ = g.$,
    pdfjsWorker = g.pdfjsWorker
  if (typeof define === 'function' && define.amd) {
    define(function () {
      return fn(g, pdfjsWorker, pdfjsLib, $, version)
    })
  } else if (typeof module !== 'undefined' && module.exports) {
    module.exports = fn(g, pdfjsWorker, pdfjsLib, $, version)
  } else {
    g.Pdfh5 = fn(g, pdfjs<PERSON>orker, pdfjsLib, $, version)
  }
})(typeof window !== 'undefined' ? window : this, function (g, pdfjsWorker, pdfjsLib, $, version) {
  'use strict'

  var _createClass = (function () {
    function defineProperties(target, props) {
      for (var i = 0; i < props.length; i++) {
        var descriptor = props[i]
        descriptor.enumerable = descriptor.enumerable || false
        descriptor.configurable = true
        if ('value' in descriptor) descriptor.writable = true
        Object.defineProperty(target, descriptor.key, descriptor)
      }
    }
    return function (Constructor, protoProps, staticProps) {
      if (protoProps) defineProperties(Constructor.prototype, protoProps)
      if (staticProps) defineProperties(Constructor, staticProps)
      return Constructor
    }
  })()

  function _classCallCheck(instance, Constructor) {
    if (!(instance instanceof Constructor)) {
      throw new TypeError('Cannot call a class as a function')
    }
  }

  var renderTextLayer = pdfjsLib.renderTextLayer
  var EXPAND_DIVS_TIMEOUT = 300 // ms

  var TextLayerBuilder = (function () {
    function TextLayerBuilder(_ref) {
      var textLayerDiv = _ref.textLayerDiv
      var eventBus = _ref.eventBus
      var pageIndex = _ref.pageIndex
      var viewport = _ref.viewport
      var _ref$findController = _ref.findController
      var findController = _ref$findController === undefined ? null : _ref$findController
      var _ref$enhanceTextSelec = _ref.enhanceTextSelection
      var enhanceTextSelection = _ref$enhanceTextSelec === undefined ? false : _ref$enhanceTextSelec

      _classCallCheck(this, TextLayerBuilder)

      this.textLayerDiv = textLayerDiv
      this.eventBus = eventBus
      this.textContent = null
      this.textContentItemsStr = []
      this.textContentStream = null
      this.renderingDone = false
      this.pageIdx = pageIndex
      this.pageNumber = this.pageIdx + 1
      this.matches = []
      this.viewport = viewport
      this.textDivs = []
      this.findController = findController
      this.textLayerRenderTask = null
      this.enhanceTextSelection = enhanceTextSelection

      this._onUpdateTextLayerMatches = null
      this._bindMouse()
    }

    /**
     * @private
     */

    _createClass(TextLayerBuilder, [
      {
        key: '_finishRendering',
        value: function _finishRendering() {
          this.renderingDone = true

          if (!this.enhanceTextSelection) {
            var endOfContent = document.createElement('div')
            endOfContent.className = 'endOfContent'
            this.textLayerDiv.appendChild(endOfContent)
          }
          if (this.eventBus) {
            this.eventBus.dispatch('textlayerrendered', {
              source: this,
              pageNumber: this.pageNumber,
              numTextDivs: this.textDivs.length,
            })
          }
        },

        /**
         * Renders the text layer.
         *
         * @param {number} [timeout] - Wait for a specified amount of milliseconds
         *                             before rendering.
         */
      },
      {
        key: 'render',
        value: function render() {
          var _this = this

          var timeout = arguments.length <= 0 || arguments[0] === undefined ? 0 : arguments[0]

          if (!(this.textContent || this.textContentStream) || this.renderingDone) {
            return
          }
          this.cancel()

          this.textDivs = []
          var textLayerFrag = document.createDocumentFragment()
          this.textLayerRenderTask = renderTextLayer({
            textContent: this.textContent,
            textContentStream: this.textContentStream,
            container: textLayerFrag,
            viewport: this.viewport,
            textDivs: this.textDivs,
            textContentItemsStr: this.textContentItemsStr,
            timeout: timeout,
            enhanceTextSelection: this.enhanceTextSelection,
          })
          this.textLayerRenderTask.promise.then(
            function () {
              _this.textLayerDiv.appendChild(textLayerFrag)
              _this._finishRendering()
              _this._updateMatches()
            },
            function (reason) {
              // Cancelled or failed to render text layer; skipping errors.
            },
          )

          if (!this._onUpdateTextLayerMatches) {
            this._onUpdateTextLayerMatches = function (evt) {
              if (evt.pageIndex === _this.pageIdx || evt.pageIndex === -1) {
                _this._updateMatches()
              }
            }
            if (this.eventBus) {
              this.eventBus._on('updatetextlayermatches', this._onUpdateTextLayerMatches)
            }
          }
        },

        /**
         * Cancel rendering of the text layer.
         */
      },
      {
        key: 'cancel',
        value: function cancel() {
          if (this.textLayerRenderTask) {
            this.textLayerRenderTask.cancel()
            this.textLayerRenderTask = null
          }
          if (this._onUpdateTextLayerMatches) {
            this.eventBus._off('updatetextlayermatches', this._onUpdateTextLayerMatches)
            this._onUpdateTextLayerMatches = null
          }
        },
      },
      {
        key: 'setTextContentStream',
        value: function setTextContentStream(readableStream) {
          this.cancel()
          this.textContentStream = readableStream
        },
      },
      {
        key: 'setTextContent',
        value: function setTextContent(textContent) {
          this.cancel()
          this.textContent = textContent
        },
      },
      {
        key: '_convertMatches',
        value: function _convertMatches(matches, matchesLength) {
          // Early exit if there is nothing to convert.
          if (!matches) {
            return []
          }
          var findController = this.findController
          var textContentItemsStr = this.textContentItemsStr

          var i = 0,
            iIndex = 0
          var end = textContentItemsStr.length - 1
          var queryLen = findController.state.query.length
          var result = []

          for (var m = 0, mm = matches.length; m < mm; m++) {
            // Calculate the start position.
            var matchIdx = matches[m]

            // Loop over the divIdxs.
            while (i !== end && matchIdx >= iIndex + textContentItemsStr[i].length) {
              iIndex += textContentItemsStr[i].length
              i++
            }

            if (i === textContentItemsStr.length) {
              console.error('Could not find a matching mapping')
            }

            var match = {
              begin: {
                divIdx: i,
                offset: matchIdx - iIndex,
              },
            }

            // Calculate the end position.
            if (matchesLength) {
              // Multiterm search.
              matchIdx += matchesLength[m]
            } else {
              // Phrase search.
              matchIdx += queryLen
            }

            // Somewhat the same array as above, but use > instead of >= to get
            // the end position right.
            while (i !== end && matchIdx > iIndex + textContentItemsStr[i].length) {
              iIndex += textContentItemsStr[i].length
              i++
            }

            match.end = {
              divIdx: i,
              offset: matchIdx - iIndex,
            }
            result.push(match)
          }
          return result
        },
      },
      {
        key: '_renderMatches',
        value: function _renderMatches(matches) {
          // Early exit if there is nothing to render.
          if (matches.length === 0) {
            return
          }
          var findController = this.findController
          var pageIdx = this.pageIdx
          var textContentItemsStr = this.textContentItemsStr
          var textDivs = this.textDivs

          var isSelectedPage = pageIdx === findController.selected.pageIdx
          var selectedMatchIdx = findController.selected.matchIdx
          var highlightAll = findController.state.highlightAll
          var prevEnd = null
          var infinity = {
            divIdx: -1,
            offset: undefined,
          }

          function beginText(begin, className) {
            var divIdx = begin.divIdx
            textDivs[divIdx].textContent = ''
            appendTextToDiv(divIdx, 0, begin.offset, className)
          }

          function appendTextToDiv(divIdx, fromOffset, toOffset, className) {
            var div = textDivs[divIdx]
            var content = textContentItemsStr[divIdx].substring(fromOffset, toOffset)
            var node = document.createTextNode(content)
            if (className) {
              var span = document.createElement('span')
              span.className = className
              span.appendChild(node)
              div.appendChild(span)
              return
            }
            div.appendChild(node)
          }

          var i0 = selectedMatchIdx,
            i1 = i0 + 1
          if (highlightAll) {
            i0 = 0
            i1 = matches.length
          } else if (!isSelectedPage) {
            // Not highlighting all and this isn't the selected page, so do nothing.
            return
          }

          for (var i = i0; i < i1; i++) {
            var match = matches[i]
            var begin = match.begin
            var end = match.end
            var isSelected = isSelectedPage && i === selectedMatchIdx
            var highlightSuffix = isSelected ? ' selected' : ''

            if (isSelected) {
              // Attempt to scroll the selected match into view.
              findController.scrollMatchIntoView({
                element: textDivs[begin.divIdx],
                pageIndex: pageIdx,
                matchIndex: selectedMatchIdx,
              })
            }

            // Match inside new div.
            if (!prevEnd || begin.divIdx !== prevEnd.divIdx) {
              // If there was a previous div, then add the text at the end.
              if (prevEnd !== null) {
                appendTextToDiv(prevEnd.divIdx, prevEnd.offset, infinity.offset)
              }
              // Clear the divs and set the content until the starting point.
              beginText(begin)
            } else {
              appendTextToDiv(prevEnd.divIdx, prevEnd.offset, begin.offset)
            }

            if (begin.divIdx === end.divIdx) {
              appendTextToDiv(begin.divIdx, begin.offset, end.offset, 'highlight' + highlightSuffix)
            } else {
              appendTextToDiv(
                begin.divIdx,
                begin.offset,
                infinity.offset,
                'highlight begin' + highlightSuffix,
              )
              for (var n0 = begin.divIdx + 1, n1 = end.divIdx; n0 < n1; n0++) {
                textDivs[n0].className = 'highlight middle' + highlightSuffix
              }
              beginText(end, 'highlight end' + highlightSuffix)
            }
            prevEnd = end
          }

          if (prevEnd) {
            appendTextToDiv(prevEnd.divIdx, prevEnd.offset, infinity.offset)
          }
        },
      },
      {
        key: '_updateMatches',
        value: function _updateMatches() {
          // Only show matches when all rendering is done.
          if (!this.renderingDone) {
            return
          }
          var findController = this.findController
          var matches = this.matches
          var pageIdx = this.pageIdx
          var textContentItemsStr = this.textContentItemsStr
          var textDivs = this.textDivs

          var clearedUntilDivIdx = -1

          // Clear all current matches.
          for (var i = 0, ii = matches.length; i < ii; i++) {
            var match = matches[i]
            var begin = Math.max(clearedUntilDivIdx, match.begin.divIdx)
            for (var n = begin, end = match.end.divIdx; n <= end; n++) {
              var div = textDivs[n]
              div.textContent = textContentItemsStr[n]
              div.className = ''
            }
            clearedUntilDivIdx = match.end.divIdx + 1
          }

          if (!findController || !findController.highlightMatches) {
            return
          }
          // Convert the matches on the `findController` into the match format
          // used for the textLayer.
          var pageMatches = findController.pageMatches[pageIdx] || null
          var pageMatchesLength = findController.pageMatchesLength[pageIdx] || null

          this.matches = this._convertMatches(pageMatches, pageMatchesLength)
          this._renderMatches(this.matches)
        },

        /**
         * Improves text selection by adding an additional div where the mouse was
         * clicked. This reduces flickering of the content if the mouse is slowly
         * dragged up or down.
         *
         * @private
         */
      },
      {
        key: '_bindMouse',
        value: function _bindMouse() {
          var _this2 = this

          var div = this.textLayerDiv
          var expandDivsTimer = null

          div.addEventListener('mousedown', function (evt) {
            if (_this2.enhanceTextSelection && _this2.textLayerRenderTask) {
              _this2.textLayerRenderTask.expandTextDivs(true)
              if (
                (typeof PDFJSDev === 'undefined' || !PDFJSDev.test('MOZCENTRAL')) &&
                expandDivsTimer
              ) {
                clearTimeout(expandDivsTimer)
                expandDivsTimer = null
              }
              return
            }

            var end = div.querySelector('.endOfContent')
            if (!end) {
              return
            }
            if (typeof PDFJSDev === 'undefined' || !PDFJSDev.test('MOZCENTRAL')) {
              // On non-Firefox browsers, the selection will feel better if the height
              // of the `endOfContent` div is adjusted to start at mouse click
              // location. This avoids flickering when the selection moves up.
              // However it does not work when selection is started on empty space.
              var adjustTop = evt.target !== div
              if (typeof PDFJSDev === 'undefined' || PDFJSDev.test('GENERIC')) {
                adjustTop =
                  adjustTop &&
                  window.getComputedStyle(end).getPropertyValue('-moz-user-select') !== 'none'
              }
              if (adjustTop) {
                var divBounds = div.getBoundingClientRect()
                var r = Math.max(0, (evt.pageY - divBounds.top) / divBounds.height)
                end.style.top = (r * 100).toFixed(2) + '%'
              }
            }
            end.classList.add('active')
          })

          div.addEventListener('mouseup', function () {
            if (_this2.enhanceTextSelection && _this2.textLayerRenderTask) {
              if (typeof PDFJSDev === 'undefined' || !PDFJSDev.test('MOZCENTRAL')) {
                expandDivsTimer = setTimeout(function () {
                  if (_this2.textLayerRenderTask) {
                    _this2.textLayerRenderTask.expandTextDivs(false)
                  }
                  expandDivsTimer = null
                }, EXPAND_DIVS_TIMEOUT)
              } else {
                _this2.textLayerRenderTask.expandTextDivs(false)
              }
              return
            }

            var end = div.querySelector('.endOfContent')
            if (!end) {
              return
            }
            if (typeof PDFJSDev === 'undefined' || !PDFJSDev.test('MOZCENTRAL')) {
              end.style.top = ''
            }
            end.classList.remove('active')
          })
        },
      },
    ])

    return TextLayerBuilder
  })()

  /**
   * @implements IPDFTextLayerFactory
   */

  var DefaultTextLayerFactory = (function () {
    function DefaultTextLayerFactory() {
      _classCallCheck(this, DefaultTextLayerFactory)
    }

    _createClass(DefaultTextLayerFactory, [
      {
        key: 'createTextLayerBuilder',

        /**
         * @param {HTMLDivElement} textLayerDiv
         * @param {number} pageIndex
         * @param {PageViewport} viewport
         * @param {boolean} enhanceTextSelection
         * @param {EventBus} eventBus
         * @returns {TextLayerBuilder}
         */
        value: function createTextLayerBuilder(textLayerDiv, pageIndex, viewport) {
          var enhanceTextSelection =
            arguments.length <= 3 || arguments[3] === undefined ? false : arguments[3]
          var eventBus = arguments[4]

          return new TextLayerBuilder({
            textLayerDiv: textLayerDiv,
            pageIndex: pageIndex,
            viewport: viewport,
            enhanceTextSelection: enhanceTextSelection,
            eventBus: eventBus,
          })
        },
      },
    ])

    return DefaultTextLayerFactory
  })()

  g.TextLayerBuilder = TextLayerBuilder
  g.DefaultTextLayerFactory = DefaultTextLayerFactory

  var definePinchZoom = function () {
    var PinchZoom = function (el, options, viewerContainer) {
        this.el = typeof el === 'string' ? document.querySelector(el) : el
        this.viewerContainer = viewerContainer
        this.zoomFactor = 1
        this.lastScale = 1
        this.offset = {
          x: 0,
          y: 0,
        }
        this.options = Object.assign({}, this.defaults, options)
        this.options.zoomOutFactor = isNaN(options.zoomOutFactor) ? 1.2 : options.zoomOutFactor
        this.options.animationDuration = isNaN(options.animationDuration)
          ? 300
          : options.animationDuration
        this.options.maxZoom = isNaN(options.maxZoom) ? 3 : options.maxZoom
        this.options.minZoom = isNaN(options.minZoom) ? 0.8 : options.minZoom
        this.setupMarkup()
        this.bindEvents()
        this.update()
        this.enable()
        this.height = 0
        this.load = false
        this.direction = null
        this.clientY = null
        this.lastclientY = null
      },
      sum = function (a, b) {
        return a + b
      },
      isCloseTo = function (value, expected) {
        return value > expected - 0.01 && value < expected + 0.01
      }

    PinchZoom.prototype = {
      defaults: {
        tapZoomFactor: 3,
        zoomOutFactor: 1.2,
        animationDuration: 300,
        maxZoom: 5,
        minZoom: 0.8,
        lockDragAxis: false,
        use2d: true,
        zoomStartEventName: 'pz_zoomstart',
        zoomEndEventName: 'pz_zoomend',
        dragStartEventName: 'pz_dragstart',
        dragEndEventName: 'pz_dragend',
        doubleTapEventName: 'pz_doubletap',
      },
      handleDragStart: function (event) {
        this.el.dispatchEvent(new CustomEvent(this.options.dragStartEventName))
        this.stopAnimation()
        this.lastDragPosition = false
        this.hasInteraction = true
        this.handleDrag(event)
      },
      handleDrag: function (event) {
        if (this.zoomFactor > 1.0) {
          var touch = this.getTouches(event)[0]
          this.drag(touch, this.lastDragPosition, event)
          this.offset = this.sanitizeOffset(this.offset)
          this.lastDragPosition = touch
        }
      },

      handleDragEnd: function () {
        this.el.dispatchEvent(new CustomEvent(this.options.dragEndEventName))
        this.end()
      },
      handleZoomStart: function (event) {
        this.el.dispatchEvent(new CustomEvent(this.options.zoomStartEventName))
        this.stopAnimation()
        this.lastScale = 1
        this.nthZoom = 0
        this.lastZoomCenter = false
        this.hasInteraction = true
      },
      handleZoom: function (event, newScale) {
        var touchCenter = this.getTouchCenter(this.getTouches(event)),
          scale = newScale / this.lastScale
        this.lastScale = newScale
        this.nthZoom += 1
        if (this.nthZoom > 3) {
          this.scale(scale, touchCenter)
          this.drag(touchCenter, this.lastZoomCenter)
        }
        this.lastZoomCenter = touchCenter
      },

      handleZoomEnd: function () {
        this.el.dispatchEvent(new CustomEvent(this.options.zoomEndEventName))
        this.end()
      },
      handleDoubleTap: function (event) {
        var center = this.getTouches(event)[0],
          zoomFactor = this.zoomFactor > 1 ? 1 : this.options.tapZoomFactor,
          startZoomFactor = this.zoomFactor,
          updateProgress = function (progress) {
            this.scaleTo(startZoomFactor + progress * (zoomFactor - startZoomFactor), center)
          }.bind(this)

        if (this.hasInteraction) {
          return
        }
        if (startZoomFactor > zoomFactor) {
          center = this.getCurrentZoomCenter()
        }

        this.animate(this.options.animationDuration, updateProgress, this.swing)
        this.el.dispatchEvent(new CustomEvent(this.options.doubleTapEventName))
      },
      sanitizeOffset: function (offset) {
        var maxX = (this.zoomFactor - 1) * this.getContainerX(),
          maxY = (this.zoomFactor - 1) * this.getContainerY(),
          maxOffsetX = Math.max(maxX, 0),
          maxOffsetY = Math.max(maxY, 0),
          minOffsetX = Math.min(maxX, 0),
          minOffsetY = Math.min(maxY, 0)

        var x = Math.min(Math.max(offset.x, minOffsetX), maxOffsetX),
          y = Math.min(Math.max(offset.y, minOffsetY), maxOffsetY)

        return {
          x: x,
          y: y,
        }
      },
      scaleTo: function (zoomFactor, center) {
        this.scale(zoomFactor / this.zoomFactor, center)
      },
      scale: function (scale, center) {
        scale = this.scaleZoomFactor(scale)
        this.addOffset({
          x: (scale - 1) * (center.x + this.offset.x),
          y: (scale - 1) * (center.y + this.offset.y),
        })
        this.done && this.done.call(this, this.getInitialZoomFactor() * this.zoomFactor)
      },
      scaleZoomFactor: function (scale) {
        var originalZoomFactor = this.zoomFactor
        this.zoomFactor *= scale
        this.zoomFactor = Math.min(
          this.options.maxZoom,
          Math.max(this.zoomFactor, this.options.minZoom),
        )
        return this.zoomFactor / originalZoomFactor
      },
      drag: function (center, lastCenter, event) {
        if (lastCenter) {
          if (this.options.lockDragAxis) {
            if (Math.abs(center.x - lastCenter.x) > Math.abs(center.y - lastCenter.y)) {
              this.addOffset({
                x: -(center.x - lastCenter.x),
                y: 0,
              })
            } else {
              this.addOffset({
                y: -(center.y - lastCenter.y),
                x: 0,
              })
            }
          } else {
            if (center.y - lastCenter.y < 0) {
              this.direction = 'down'
            } else if (center.y - lastCenter.y > 10) {
              this.direction = 'up'
            }
            this.addOffset({
              y: -(center.y - lastCenter.y),
              x: -(center.x - lastCenter.x),
            })
          }
        }
      },
      getTouchCenter: function (touches) {
        return this.getVectorAvg(touches)
      },
      getVectorAvg: function (vectors) {
        return {
          x:
            vectors
              .map(function (v) {
                return v.x
              })
              .reduce(sum) / vectors.length,
          y:
            vectors
              .map(function (v) {
                return v.y
              })
              .reduce(sum) / vectors.length,
        }
      },
      addOffset: function (offset) {
        this.offset = {
          x: this.offset.x + offset.x,
          y: this.offset.y + offset.y,
        }
      },

      sanitize: function () {
        if (this.zoomFactor < this.options.zoomOutFactor) {
          this.zoomOutAnimation()
        } else if (this.isInsaneOffset(this.offset)) {
          this.sanitizeOffsetAnimation()
        }
      },
      isInsaneOffset: function (offset) {
        var sanitizedOffset = this.sanitizeOffset(offset)
        return sanitizedOffset.x !== offset.x || sanitizedOffset.y !== offset.y
      },
      sanitizeOffsetAnimation: function () {
        var targetOffset = this.sanitizeOffset(this.offset),
          startOffset = {
            x: this.offset.x,
            y: this.offset.y,
          },
          updateProgress = function (progress) {
            this.offset.x = startOffset.x + progress * (targetOffset.x - startOffset.x)
            this.offset.y = startOffset.y + progress * (targetOffset.y - startOffset.y)
            this.update()
          }.bind(this)

        this.animate(this.options.animationDuration, updateProgress, this.swing)
      },
      zoomOutAnimation: function () {
        var startZoomFactor = this.zoomFactor,
          zoomFactor = 1,
          center = this.getCurrentZoomCenter(),
          updateProgress = function (progress) {
            this.scaleTo(startZoomFactor + progress * (zoomFactor - startZoomFactor), center)
          }.bind(this)

        this.animate(this.options.animationDuration, updateProgress, this.swing)
      },
      updateAspectRatio: function () {
        this.setContainerY(this.getContainerX() / this.getAspectRatio())
      },
      getInitialZoomFactor: function () {
        if (this.container && this.el) {
          return this.container.offsetWidth / this.el.offsetWidth
        } else {
          return 0
        }
      },
      getAspectRatio: function () {
        if (this.el) {
          var offsetHeight = this.el.offsetHeight
          return this.container.offsetWidth / offsetHeight
        } else {
          return 0
        }
      },
      getCurrentZoomCenter: function () {
        var length = this.container.offsetWidth * this.zoomFactor,
          offsetLeft = this.offset.x,
          offsetRight = length - offsetLeft - this.container.offsetWidth,
          widthOffsetRatio = offsetLeft / offsetRight,
          centerX = (widthOffsetRatio * this.container.offsetWidth) / (widthOffsetRatio + 1),
          height = this.container.offsetHeight * this.zoomFactor,
          offsetTop = this.offset.y,
          offsetBottom = height - offsetTop - this.container.offsetHeight,
          heightOffsetRatio = offsetTop / offsetBottom,
          centerY = (heightOffsetRatio * this.container.offsetHeight) / (heightOffsetRatio + 1)

        if (offsetRight === 0) {
          centerX = this.container.offsetWidth
        }
        if (offsetBottom === 0) {
          centerY = this.container.offsetHeight
        }

        return {
          x: centerX,
          y: centerY,
        }
      },

      canDrag: function () {
        return !isCloseTo(this.zoomFactor, 1)
      },

      getTouches: function (event) {
        var position = this.container.getBoundingClientRect()
        return Array.prototype.slice.call(event.touches).map(function (touch) {
          return {
            x: touch.pageX - position.left,
            y: touch.pageY - position.top,
          }
        })
      },
      animate: function (duration, framefn, timefn, callback) {
        var startTime = new Date().getTime(),
          renderFrame = function () {
            if (!this.inAnimation) {
              return
            }
            var frameTime = new Date().getTime() - startTime,
              progress = frameTime / duration
            if (frameTime >= duration) {
              framefn(1)
              if (callback) {
                callback()
              }
              this.update()
              this.stopAnimation()
            } else {
              if (timefn) {
                progress = timefn(progress)
              }
              framefn(progress)
              this.update()
              requestAnimationFrame(renderFrame)
            }
          }.bind(this)
        this.inAnimation = true
        requestAnimationFrame(renderFrame)
      },
      stopAnimation: function () {
        this.inAnimation = false
      },
      swing: function (p) {
        return -Math.cos(p * Math.PI) / 2 + 0.5
      },

      getContainerX: function () {
        if (this.el) {
          return this.el.offsetWidth
        } else {
          return 0
        }
      },

      getContainerY: function () {
        return this.el.offsetHeight
      },
      setContainerY: function (y) {
        y = y.toFixed(2)
        return (this.container.style.height = y)
      },
      setupMarkup: function () {
        // 创建容器
        this.container = document.createElement('div')
        this.container.className = 'pinch-zoom-container'
        this.el.parentNode.insertBefore(this.container, this.el)
        this.container.appendChild(this.el)
        this.container.style.position = 'relative'
        this.el.style.transformOrigin = '0% 0%'
        this.el.style.position = 'relative'
      },

      end: function () {
        this.hasInteraction = false
        this.sanitize()
        this.update()
      },
      bindEvents: function () {
        // 绑定自定义手势
        detectGestures(this.container, this, this.viewerContainer)
        window.addEventListener('resize', this.update.bind(this))
        var imgs = this.el.querySelectorAll('img')
        imgs.forEach(function (img) {
          img.addEventListener('load', this.update.bind(this))
        }, this)
      },
      update: function () {
        if (this.updatePlaned) {
          return
        }
        this.updatePlaned = true
        setTimeout(
          function () {
            this.updatePlaned = false
            this.updateAspectRatio()
            var zoomFactor = this.getInitialZoomFactor() * this.zoomFactor,
              offsetX = (-this.offset.x / zoomFactor).toFixed(3),
              offsetY = (-this.offset.y / zoomFactor).toFixed(3)
            this.lastclientY = offsetY

            var transform3d =
                'scale3d(' +
                zoomFactor +
                ', ' +
                zoomFactor +
                ',1) ' +
                'translate3d(' +
                offsetX +
                'px,' +
                offsetY +
                'px,0px)',
              transform2d =
                'scale(' +
                zoomFactor +
                ', ' +
                zoomFactor +
                ') ' +
                'translate(' +
                offsetX +
                'px,' +
                offsetY +
                'px)',
              removeClone = function () {
                if (this.clone) {
                  this.clone.remove()
                  delete this.clone
                }
              }.bind(this)
            if (!this.options.use2d || this.hasInteraction || this.inAnimation) {
              this.is3d = true
              this.el.style.transform = transform3d
              this.el.style.webkitTransform = transform3d
              this.el.style.MozTransform = transform2d
              this.el.style.msTransform = transform2d
              this.el.style.OTransform = transform2d
            } else {
              this.el.style.transform = transform2d
              this.el.style.webkitTransform = transform2d
              this.el.style.MozTransform = transform2d
              this.el.style.msTransform = transform2d
              this.el.style.OTransform = transform2d
              this.is3d = false
            }
          }.bind(this),
          0,
        )
      },
      enable: function () {
        this.enabled = true
      },
      disable: function () {
        this.enabled = false
      },
      destroy: function () {
        var dom = this.el.cloneNode(true)
        var p = this.container.parentNode
        this.container.remove()
        dom.removeAttribute('style')
        p.appendChild(dom)
      },
    }

    var detectGestures = function (el, target, viewerContainer) {
      var interaction = null,
        fingers = 0,
        lastTouchStart = null,
        startTouches = null,
        lastTouchY = null,
        clientY = null,
        lastclientY = 0,
        lastTop = 0,
        setInteraction = function (newInteraction, event) {
          if (interaction !== newInteraction) {
            if (interaction && !newInteraction) {
              switch (interaction) {
                case 'zoom':
                  target.handleZoomEnd(event)
                  break
                case 'drag':
                  target.handleDragEnd(event)
                  break
              }
            }

            switch (newInteraction) {
              case 'zoom':
                target.handleZoomStart(event)
                break
              case 'drag':
                target.handleDragStart(event)
                break
            }
          }
          interaction = newInteraction
        },
        updateInteraction = function (event) {
          if (fingers === 2) {
            setInteraction('zoom')
          } else if (fingers === 1 && target.canDrag()) {
            setInteraction('drag', event)
          } else {
            setInteraction(null, event)
          }
        },
        targetTouches = function (touches) {
          return Array.prototype.slice.call(touches).map(function (touch) {
            return {
              x: touch.pageX,
              y: touch.pageY,
            }
          })
        },
        getDistance = function (a, b) {
          var x, y
          x = a.x - b.x
          y = a.y - b.y
          return Math.sqrt(x * x + y * y)
        },
        calculateScale = function (startTouches, endTouches) {
          var startDistance = getDistance(startTouches[0], startTouches[1]),
            endDistance = getDistance(endTouches[0], endTouches[1])
          return endDistance / startDistance
        },
        cancelEvent = function (event) {
          event.stopPropagation()
          event.preventDefault()
        },
        detectDoubleTap = function (event) {
          var time = new Date().getTime()
          var pageY = event.changedTouches[0].pageY
          var top = viewerContainer ? viewerContainer.scrollTop || 0 : 0
          if (fingers > 1) {
            lastTouchStart = null
            lastTouchY = null
            cancelEvent(event)
          }

          if (
            time - lastTouchStart < 300 &&
            Math.abs(pageY - lastTouchY) < 10 &&
            Math.abs(lastTop - top) < 10
          ) {
            cancelEvent(event)
            target.handleDoubleTap(event)
            switch (interaction) {
              case 'zoom':
                target.handleZoomEnd(event)
                break
              case 'drag':
                target.handleDragEnd(event)
                break
            }
          }

          if (fingers === 1) {
            lastTouchStart = time
            lastTouchY = pageY
            lastTop = top
          }
        },
        firstMove = true
      if (viewerContainer) {
        var parentNode = viewerContainer
      }
      if (parentNode) {
        parentNode.addEventListener('touchstart', function (event) {
          if (target.enabled) {
            firstMove = true
            fingers = event.touches.length
            detectDoubleTap(event)
            clientY = event.changedTouches[0].clientY
            if (fingers > 1) {
              cancelEvent(event)
            }
          }
        })

        parentNode.addEventListener('touchmove', function (event) {
          if (target.enabled) {
            lastclientY = event.changedTouches[0].clientY
            if (firstMove) {
              updateInteraction(event)
              startTouches = targetTouches(event.touches)
            } else {
              switch (interaction) {
                case 'zoom':
                  target.handleZoom(
                    event,
                    calculateScale(startTouches, targetTouches(event.touches)),
                  )
                  break
                case 'drag':
                  target.handleDrag(event)
                  break
              }
              if (interaction) {
                target.update(lastclientY)
              }
            }
            if (fingers > 1) {
              cancelEvent(event)
            }
            firstMove = false
          }
        })

        parentNode.addEventListener('touchend', function (event) {
          if (target.enabled) {
            fingers = event.touches.length
            if (fingers > 1) {
              cancelEvent(event)
            }
            updateInteraction(event)
          }
        })
      }
    }
    return PinchZoom
  }
  var PinchZoom = definePinchZoom()
  var Pdfh5 = function (dom, options) {
    this.version = version
    this.container = typeof dom === 'string' ? document.querySelector(dom) : dom
    this.options = options
    this.thePDF = null
    this.totalNum = null
    this.pages = null
    this.initTime = 0
    this.scale = 1.5
    this.currentNum = 1
    this.loadedCount = 0
    this.endTime = 0
    this.pinchZoom = null
    this.timer = null
    this.docWidth = document.documentElement.clientWidth
    this.winWidth = window.innerWidth
    this.cache = {}
    this.eventType = {}
    this.cacheNum = 1
    this.resizeEvent = false
    this.cacheData = null
    this.pdfjsLibPromise = null
    this.init(options)
  }
  Pdfh5.prototype = {
    init: function (options) {
      var self = this
      if (this.container.pdfLoaded) {
        this.destroy()
      }
      if (options.cMapUrl) {
        pdfjsLib.cMapUrl = options.cMapUrl
      } else {
        pdfjsLib.cMapUrl = 'https://unpkg.com/pdfjs-dist@2.0.943/cmaps/'
      }
      pdfjsLib.cMapPacked = true
      pdfjsLib.rangeChunkSize = 65536
      this.container.pdfLoaded = false
      this.container.classList.add('pdfjs')
      this.initTime = new Date().getTime()
      setTimeout(function () {
        var arr1 = self.eventType['scroll']
        if (arr1 && Array.isArray(arr1)) {
          for (var i = 0; i < arr1.length; i++) {
            arr1[i] && arr1[i].call(self, self.initTime)
          }
        }
      }, 0)
      this.options = this.options ? this.options : {}
      this.options.pdfurl = this.options.pdfurl ? this.options.pdfurl : null
      this.options.data = this.options.data ? this.options.data : null
      this.options.scale = this.options.scale ? this.options.scale : this.scale
      this.options.zoomEnable = this.options.zoomEnable === false ? false : true
      this.options.scrollEnable = this.options.scrollEnable === false ? false : true
      this.options.loadingBar = this.options.loadingBar === false ? false : true
      this.options.pageNum = this.options.pageNum === false ? false : true
      this.options.backTop = this.options.backTop === false ? false : true
      this.options.URIenable = this.options.URIenable === true ? true : false
      this.options.fullscreen = this.options.fullscreen === false ? false : true
      this.options.lazy = this.options.lazy === true ? true : false
      this.options.renderType = this.options.renderType === 'svg' ? 'svg' : 'canvas'
      this.options.resize = this.options.resize === false ? false : true
      this.options.textLayer = this.options.textLayer === true ? true : false
      this.options.goto = isNaN(this.options.goto) ? 0 : this.options.goto
      if (
        this.options.logo &&
        Object.prototype.toString.call(this.options.logo) === '[object Object]' &&
        this.options.logo.src
      ) {
        this.options.logo.img = new Image()
        this.options.logo.img.src = this.options.logo.src
        this.options.logo.img.style.display = 'none'
        document.body.appendChild(this.options.logo.img)
      } else {
        this.options.logo = false
      }
      if (
        !(
          this.options.background &&
          (this.options.background.color || this.options.background.image)
        )
      ) {
        this.options.background = false
      }
      if (this.options.limit) {
        var n = parseFloat(this.options.limit)
        this.options.limit = isNaN(n) ? 0 : n < 0 ? 0 : n
      } else {
        this.options.limit = 0
      }
      this.options.type = this.options.type === 'fetch' ? 'fetch' : 'ajax'
      var html =
        '<div class="loadingBar">' +
        '<div class="progress">' +
        ' <div class="glimmer">' +
        '</div>' +
        ' </div>' +
        '</div>' +
        '<div class="pageNum">' +
        '<div class="pageNum-bg"></div>' +
        ' <div class="pageNum-num">' +
        ' <span class="pageNow">1</span>/' +
        '<span class="pageTotal">1</span>' +
        '</div>' +
        ' </div>' +
        '<div class="backTop">' +
        '</div>' +
        '<div class="loadEffect loading"></div>'
      if (!this.container.querySelector('.pageNum')) {
        this.container.insertAdjacentHTML('beforeend', html)
      }
      var viewer = document.createElement('div')
      viewer.className = 'pdfViewer'
      var viewerContainer = document.createElement('div')
      viewerContainer.className = 'viewerContainer'
      viewerContainer.appendChild(viewer)
      this.container.appendChild(viewerContainer)
      this.viewer = viewer
      this.viewerContainer = viewerContainer
      this.pageNum = this.container.querySelector('.pageNum')
      this.pageNow = this.pageNum.querySelector('.pageNow')
      this.pageTotal = this.pageNum.querySelector('.pageTotal')
      this.loadingBar = this.container.querySelector('.loadingBar')
      this.progress = this.loadingBar.querySelector('.progress')
      this.progressDom = this.progress
      this.backTop = this.container.querySelector('.backTop')
      this.loading = this.container.querySelector('.loading')
      if (!this.options.loadingBar) {
        this.loadingBar.style.display = 'none'
      }
      var containerH = this.container.offsetHeight,
        height = containerH * (1 / 3)
      if (!this.options.scrollEnable) {
        this.viewerContainer.style.overflow = 'hidden'
      } else {
        this.viewerContainer.style.overflow = 'auto'
      }
      viewerContainer.addEventListener('scroll', function () {
        var scrollTop = viewerContainer.scrollTop
        if (scrollTop >= 150) {
          if (self.options.backTop) {
            self.backTop.style.display = 'block'
          }
        } else {
          if (self.options.backTop) {
            self.backTop.style.display = 'none'
          }
        }
        if (self.viewerContainer) {
          self.pages = self.viewerContainer.querySelectorAll('.pageContainer')
        }
        clearTimeout(self.timer)
        if (self.options.pageNum && self.pageNum) {
          self.pageNum.style.display = 'block'
        }
        var h = containerH
        if (self.pages) {
          self.pages.forEach(function (item, index) {
            var top = item.getBoundingClientRect().top
            var bottom = item.getBoundingClientRect().bottom
            if (top <= height && bottom > height) {
              if (self.options.pageNum) {
                self.pageNow.textContent = index + 1
              }
              self.currentNum = index + 1
            }
            if (top <= h && bottom > h) {
              self.cacheNum = index + 1
            }
          })
        }
        if (scrollTop + self.container.offsetHeight >= self.viewer.offsetHeight) {
          self.pageNow.textContent = self.totalNum
        }
        if (scrollTop === 0) {
          self.pageNow.textContent = 1
        }
        self.timer = setTimeout(function () {
          if (self.options.pageNum && self.pageNum) {
            self.pageNum.style.display = 'none'
          }
        }, 1500)
        if (self.options.lazy) {
          var num = Math.floor(100 / self.totalNum).toFixed(2)
          if (self.cache[self.cacheNum + ''] && !self.cache[self.cacheNum + ''].loaded) {
            var page = self.cache[self.cacheNum + ''].page
            var container = self.cache[self.cacheNum + ''].container
            var pageNum = self.cacheNum
            self.cache[pageNum + ''].loaded = true
            var scaledViewport = self.cache[pageNum + ''].scaledViewport
            if (self.options.renderType === 'svg') {
              self.renderSvg(page, scaledViewport, pageNum, num, container, self.options)
            } else {
              self.renderCanvas(page, scaledViewport, pageNum, num, container, self.options)
            }
          }
          if (
            self.cache[self.totalNum - 1 + ''] &&
            self.cache[self.totalNum - 1 + ''].loaded &&
            !self.cache[self.totalNum + ''].loaded
          ) {
            var page = self.cache[self.totalNum + ''].page
            var container = self.cache[self.totalNum + ''].container
            var pageNum = self.totalNum
            self.cache[pageNum + ''].loaded = true
            var scaledViewport = self.cache[pageNum + ''].scaledViewport
            if (self.options.renderType === 'svg') {
              self.renderSvg(page, scaledViewport, pageNum, num, container, self.options)
            } else {
              self.renderCanvas(page, scaledViewport, pageNum, num, container, self.options)
            }
          }
        }
        var arr1 = self.eventType['scroll']
        if (arr1 && Array.isArray(arr1)) {
          for (var i = 0; i < arr1.length; i++) {
            arr1[i] && arr1[i].call(self, viewerContainer.scrollTop)
          }
        }
      })
      this.backTop.addEventListener('click', function () {
        var mart = self.viewer.style.transform
        var arr = mart.replace(/[a-z\(\)\s]/g, '').split(',')
        var s1 = arr[0]
        var s2 = arr[3]
        var x = arr[4] / 2
        var left = self.viewer.getBoundingClientRect().left
        if (left <= -self.docWidth * 2) {
          x = -self.docWidth / 2
        }
        self.viewer.style.transform = 'scale(' + s1 + ', ' + s2 + ') translate(' + x + 'px, 0px)'
        self.viewer.style.webkitTransform =
          'scale(' + s1 + ', ' + s2 + ') translate(' + x + 'px, 0px)'
        self.viewer.style.MozTransform = 'scale(' + s1 + ', ' + s2 + ') translate(' + x + 'px, 0px)'
        self.viewer.style.msTransform = 'scale(' + s1 + ', ' + s2 + ') translate(' + x + 'px, 0px)'
        self.viewer.style.OTransform = 'scale(' + s1 + ', ' + s2 + ') translate(' + x + 'px, 0px)'
        if (self.pinchZoom) {
          self.pinchZoom.offset.y = 0
          self.pinchZoom.lastclientY = 0
        }
        self.viewerContainer.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
        var arr1 = self.eventType['backTop']
        if (arr1 && Array.isArray(arr1)) {
          for (var i = 0; i < arr1.length; i++) {
            arr1[i] && arr1[i].call(self)
          }
        }
      })

      function GetQueryString(name) {
        var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
        var r = g.location.search.substr(1).match(reg)
        if (r != null) return decodeURIComponent(r[2])
        return ''
      }
      var pdfurl = GetQueryString('file'),
        url = ''
      if (pdfurl && self.options.URIenable) {
        url = pdfurl
      } else if (self.options.pdfurl) {
        url = self.options.pdfurl
      }
      if (self.options.loadingBar) {
        self.loadingBar.style.display = 'block'
        self.progress.style.width = '3%'
      }

      if (url) {
        if (self.options.type === 'ajax') {
          var xhr = new XMLHttpRequest()
          xhr.open('GET', url, true)
          // 不设置 responseType，使用字符串模式
          xhr.overrideMimeType('text/plain; charset=x-user-defined')
          xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
              var data = xhr.responseText
              var rawLength = data.length
              var array = new Uint8Array(rawLength)
              for (var i = 0; i < rawLength; i++) {
                array[i] = data.charCodeAt(i) & 0xff
              }
              self.cacheData = array
              self.renderPdf(self.options, { data: array })
            }
          }
          xhr.onerror = function () {
            self.loading.style.display = 'none'
            var time = new Date().getTime()
            self.endTime = time - self.initTime
            var arr1 = self.eventType['complete']
            if (arr1 && Array.isArray(arr1)) {
              for (var i = 0; i < arr1.length; i++) {
                arr1[i] &&
                  arr1[i].call(self, 'error', this.status + ': ' + this.statusText, self.endTime)
              }
            }
            var arr2 = self.eventType['error']
            if (arr2 && Array.isArray(arr2)) {
              for (var i = 0; i < arr2.length; i++) {
                arr2[i] && arr2[i].call(self, this.status + ': ' + this.statusText, self.endTime)
              }
            }
            throw Error('PDF加载失败: ' + this.status + ' ' + this.statusText)
          }
          xhr.send()
        } else {
          self.renderPdf(self.options, {
            url: url,
          })
        }
      } else if (self.options.data) {
        var data = self.options.data
        var rawLength = data.length
        if (typeof data === 'string' && data !== '') {
          var array = new Uint8Array(rawLength)
          for (var i = 0; i < rawLength; i++) {
            array[i] = data.charCodeAt(i) & 0xff
          }
          self.cacheData = array
          self.renderPdf(self.options, {
            data: array,
          })
        } else if (typeof data === 'object') {
          if (data.length === 0) {
            var time = new Date().getTime()
            self.endTime = time - self.initTime
            var arr1 = self.eventType['complete']
            if (arr1 && Array.isArray(arr1)) {
              for (var i = 0; i < arr1.length; i++) {
                arr1[i] && arr1[i].call(self, 'error', 'options.data is empty Array', self.endTime)
              }
            }
            var arr2 = self.eventType['error']
            if (arr2 && Array.isArray(arr2)) {
              for (var i = 0; i < arr2.length; i++) {
                arr2[i] && arr2[i].call(self, 'options.data is empty Array', self.endTime)
              }
            }
            throw Error('options.data is empty Array')
          } else {
            self.cacheData = data
            self.renderPdf(self.options, {
              data: data,
            })
          }
        }
      } else {
        var time = new Date().getTime()
        self.endTime = time - self.initTime
        var arr1 = self.eventType['complete']
        if (arr1 && Array.isArray(arr1)) {
          for (var i = 0; i < arr1.length; i++) {
            arr1[i] &&
              arr1[i].call(self, 'error', 'Expect options.pdfurl or options.data!', self.endTime)
          }
        }
        var arr2 = self.eventType['error']
        if (arr2 && Array.isArray(arr2)) {
          for (var i = 0; i < arr2.length; i++) {
            arr2[i] && arr2[i].call(self, 'Expect options.pdfurl or options.data!', self.endTime)
          }
        }
        throw Error('Expect options.pdfurl or options.data!')
      }
    },
    renderPdf: function (options, obj) {
      this.container.pdfLoaded = true
      var self = this
      if (options.cMapUrl) {
        obj.cMapUrl = options.cMapUrl
      } else {
        obj.cMapUrl = 'https://unpkg.com/pdfjs-dist@2.0.943/cmaps/'
      }
      if (options.httpHeaders) {
        obj.httpHeaders = options.httpHeaders
      }
      if (options.withCredentials) {
        obj.withCredentials = true
      }
      if (options.password) {
        obj.password = options.password
        console.log(obj.password)
      }
      if (options.stopAtErrors) {
        obj.stopAtErrors = true
      }
      if (options.disableFontFace) {
        obj.disableFontFace = true
      }
      if (options.disableRange) {
        obj.disableRange = true
      }
      if (options.disableStream) {
        obj.disableStream = true
      }
      if (options.disableAutoFetch) {
        obj.disableAutoFetch = true
      }
      obj.cMapPacked = true
      obj.rangeChunkSize = 65536
      this.pdfjsLibPromise = pdfjsLib
        .getDocument(obj)
        .then(function (pdf) {
          self.loading.style.display = 'none'
          self.thePDF = pdf
          self.totalNum = pdf.numPages
          if (options.limit > 0) {
            self.totalNum = options.limit
          }
          self.pageTotal.textContent = self.totalNum
          if (!self.pinchZoom) {
            var arr1 = self.eventType['ready']
            if (arr1 && Array.isArray(arr1)) {
              for (var i = 0; i < arr1.length; i++) {
                arr1[i] && arr1[i].call(self)
              }
            }
            self.pinchZoom = new PinchZoom(
              self.viewer,
              {
                tapZoomFactor: options.tapZoomFactor,
                zoomOutFactor: options.zoomOutFactor,
                animationDuration: options.animationDuration,
                maxZoom: options.maxZoom,
                minZoom: options.minZoom,
              },
              self.viewerContainer,
            )
            var timeout,
              firstZoom = true
            self.pinchZoom.done = function (scale) {
              clearTimeout(timeout)
              timeout = setTimeout(function () {
                if (self.options.renderType === 'svg') {
                  return
                }
                if (scale <= 1 || self.options.scale == 5) {
                  return
                }
                if (self.thePDF) {
                  self.thePDF.destroy()
                  self.thePDF = null
                }
                self.options.scale = scale
                self.renderPdf(self.options, {
                  data: self.cacheData,
                })
              }, 310)
              if (scale == 1) {
                if (self.viewerContainer) {
                  self.viewerContainer.style.overflow = 'auto'
                }
              } else {
                if (self.viewerContainer) {
                  self.viewerContainer.style.overflow = 'auto'
                }
              }
              var arr1 = self.eventType['zoom']
              if (arr1 && Array.isArray(arr1)) {
                for (var i = 0; i < arr1.length; i++) {
                  arr1[i] && arr1[i].call(self, scale)
                }
              }
            }
            if (options.zoomEnable) {
              self.pinchZoom.enable()
            } else {
              self.pinchZoom.disable()
            }
          }

          var promise = Promise.resolve()
          var num = Math.floor(100 / self.totalNum).toFixed(2)
          var i = 1
          for (i = 1; i <= self.totalNum; i++) {
            self.cache[i + ''] = {
              page: null,
              loaded: false,
              container: null,
              scaledViewport: null,
            }
            promise = promise.then(
              function (pageNum) {
                return self.thePDF.getPage(pageNum).then(function (page) {
                  setTimeout(function () {
                    if (self.options.goto) {
                      if (pageNum == self.options.goto) {
                        self.goto(pageNum)
                      }
                    }
                  }, 0)

                  self.cache[pageNum + ''].page = page
                  var viewport = page.getViewport(options.scale)
                  var scale = (self.docWidth / viewport.width).toFixed(2)
                  var scaledViewport = page.getViewport(parseFloat(scale))
                  var div = self.container.querySelector('.pageContainer' + pageNum)
                  var container
                  if (!div) {
                    container = document.createElement('div')
                    container.className = 'pageContainer pageContainer' + pageNum
                    container.setAttribute('name', 'page=' + pageNum)
                    container.setAttribute('title', 'Page ' + pageNum)
                    var loadEffect = document.createElement('div')
                    loadEffect.className = 'loadEffect'
                    container.appendChild(loadEffect)
                    self.viewer.appendChild(container)
                    if (window.ActiveXObject || 'ActiveXObject' in window) {
                      container.style.width = viewport.width + 'px'
                      container.style.height = viewport.height + 'px'
                      container.setAttribute('data-scale', viewport.width / viewport.height)
                    } else {
                      var h = container.offsetWidth / (viewport.viewBox[2] / viewport.viewBox[3])
                      if (h > viewport.height) {
                        h = viewport.height
                      }
                      container.style.maxWidth = viewport.width + 'px'
                      container.style.maxHeight = viewport.height + 'px'
                      container.style.minHeight = h + 'px'
                      container.setAttribute('data-scale', viewport.width / viewport.height)
                    }
                  } else {
                    container = div
                  }
                  if (options.background) {
                    /*背颜色*/
                    if (options.background.color) {
                      container.style['background-color'] = options.background.color
                    }
                    /*背景图片*/
                    if (options.background.image) {
                      container.style['background-image'] = options.background.image
                    }
                    /*平铺与否*/
                    if (options.background.repeat) {
                      container.style['background-repeat'] = options.background.repeat
                    }
                    /*背景图片位置*/
                    if (options.background.position) {
                      container.style['background-position'] = options.background.position
                    }
                    /*背景图像的尺寸*/
                    if (options.background.size) {
                      container.style['background-size'] = options.background.size
                    }
                  }
                  self.cache[pageNum + ''].container = container
                  self.cache[pageNum + ''].scaledViewport = scaledViewport
                  var sum = 0,
                    containerH = self.container.offsetHeight
                  self.pages = self.viewerContainer.querySelectorAll('.pageContainer')
                  if (options.resize) {
                    self.resize()
                  }
                  if (self.pages && options.lazy) {
                    self.pages.forEach(function (item, index) {
                      var top = item.offsetTop
                      if (top <= containerH) {
                        sum = index + 1
                        self.cache[sum + ''].loaded = true
                      }
                    })
                  }

                  if (pageNum > sum && options.lazy) {
                    return
                  }
                  if (options.renderType === 'svg') {
                    return self.renderSvg(
                      page,
                      scaledViewport,
                      pageNum,
                      num,
                      container,
                      options,
                      viewport,
                    )
                  }
                  return self.renderCanvas(page, scaledViewport, pageNum, num, container, options)
                })
              }.bind(null, i),
            )
          }
        })
        .catch(function (err) {
          self.loading.style.display = 'none'
          var time = new Date().getTime()
          self.endTime = time - self.initTime
          var arr1 = self.eventType['complete']
          if (arr1 && Array.isArray(arr1)) {
            for (var i = 0; i < arr1.length; i++) {
              arr1[i] && arr1[i].call(self, 'error', err.message, self.endTime)
            }
          }
          var arr2 = self.eventType['error']
          if (arr2 && Array.isArray(arr2)) {
            for (var i = 0; i < arr2.length; i++) {
              arr2[i] && arr2[i].call(self, err.message, self.endTime)
            }
          }
        })
    },
    renderSvg: function (page, scaledViewport, pageNum, num, container, options, viewport) {
      var self = this
      return page.getOperatorList().then(function (opList) {
        var svgGfx = new pdfjsLib.SVGGraphics(page.commonObjs, page.objs)
        return svgGfx.getSVG(opList, scaledViewport).then(function (svg) {
          self.loadedCount++
          container.children[0].style.display = 'none'
          container.appendChild(svg)
          svg.style.width = '100%'
          svg.style.height = '100%'
          if (self.options.loadingBar) {
            self.progress.style.width = num * self.loadedCount + '%'
          }
          var time = new Date().getTime()
          var arr1 = self.eventType['render']
          if (arr1 && Array.isArray(arr1)) {
            for (var i = 0; i < arr1.length; i++) {
              arr1[i] && arr1[i].call(self, pageNum, time - self.initTime, container)
            }
          }
          if (self.loadedCount === self.totalNum) {
            self.finalRender(options)
          }
        })
      })
    },
    renderCanvas: function (page, viewport, pageNum, num, container, options) {
      var self = this
      var viewport = page.getViewport(options.scale)
      var scale = (self.docWidth / viewport.width).toFixed(2)
      var canvas = document.createElement('canvas')
      var obj2 = {
        Cheight: viewport.height * scale,
        width: viewport.width,
        height: viewport.height,
        canvas: canvas,
        index: self.loadedCount,
      }
      var context = canvas.getContext('2d')
      if (options.logo) {
        context.drawImage(
          self.options.logo.img,
          self.options.logo.x,
          self.options.logo.y,
          self.options.logo.width * self.options.scale,
          self.options.logo.height * self.options.scale,
        )
      }
      canvas.height = viewport.height
      canvas.width = viewport.width
      if (self.options.loadingBar) {
        self.progress.style.width = num * self.loadedCount + '%'
      }
      obj2.src = obj2.canvas.toDataURL('image/png')
      var renderObj = {
        canvasContext: context,
        viewport: viewport,
      }
      if (options.background) {
        renderObj.background = 'rgba(255, 255, 255, 0)'
      }
      return page
        .render(renderObj)
        .then(function () {
          if (options.logo) {
            context.drawImage(
              self.options.logo.img,
              self.options.logo.x,
              self.options.logo.y,
              self.options.logo.width * self.options.scale,
              self.options.logo.height * self.options.scale,
            )
          }
          self.loadedCount++
          var img = new Image()
          var time = new Date().getTime()
          var time2 = 0
          if (self.renderTime == 0) {
            time2 = time - self.startTime
          } else {
            time2 = time - self.renderTime
          }
          obj2.src = obj2.canvas.toDataURL('image/png')

          img.src = obj2.src
          img.className = 'canvasImg' + pageNum
          var img0 = self.container
            .querySelector('.pageContainer' + pageNum)
            .querySelector('.canvasImg' + pageNum)
          if (container && !img0) {
            container.appendChild(img)
          } else if (img0) {
            img0.src = obj2.src
          }
          container.children[0].style.display = 'none'
          var time = new Date().getTime()
          var arr1 = self.eventType['render']
          if (arr1 && Array.isArray(arr1)) {
            for (var i = 0; i < arr1.length; i++) {
              arr1[i] && arr1[i].call(self, pageNum, time - self.initTime, container)
            }
          }
          if (self.loadedCount === self.totalNum) {
            self.finalRender(options)
          }
        })
        .then(function () {
          return page.getTextContent()
        })
        .then(function (textContent) {
          if (!self.options.textLayer) {
            return
          }
          if (container.querySelector('.textLayer')) {
            return
          }
          var textLayerDiv = document.createElement('div')
          textLayerDiv.setAttribute('class', 'textLayer')
          container.appendChild(textLayerDiv)
          viewport.width = viewport.width * scale
          viewport.height = viewport.height * scale
          var textLayer = new TextLayerBuilder({
            textLayerDiv: textLayerDiv,
            pageIndex: page.pageIndex,
            viewport: viewport,
          })

          textLayer.setTextContent(textContent)

          textLayer.render()
        })
    },
    finalRender: function (options) {
      var time = new Date().getTime()
      var self = this
      if (self.options.loadingBar) {
        self.progress.style.width = '100%'
      }
      setTimeout(function () {
        self.loadingBar.style.display = 'none'
      }, 300)
      self.endTime = time - self.initTime
      if (options.renderType === 'svg') {
        if (self.totalNum !== 1) {
          self.cache[self.totalNum - 1 + ''].loaded = true
        } else {
          self.cache['1'].loaded = true
        }
      }
      if (options.zoomEnable) {
        if (self.pinchZoom) {
          self.pinchZoom.enable()
        }
      } else {
        if (self.pinchZoom) {
          self.pinchZoom.disable()
        }
      }
      var arr1 = self.eventType['complete']
      if (arr1 && Array.isArray(arr1)) {
        for (var i = 0; i < arr1.length; i++) {
          arr1[i] && arr1[i].call(self, 'success', 'pdf加载完成', self.endTime)
        }
      }
      var arr2 = self.eventType['success']
      if (arr2 && Array.isArray(arr2)) {
        for (var i = 0; i < arr2.length; i++) {
          arr2[i] && arr2[i].call(self, self.endTime)
        }
      }
    },
    resize: function () {
      var self = this
      if (self.resizeEvent) {
        return
      }
      self.resizeEvent = true
      var timer
      if (self.pages) {
        window.addEventListener('resize', function () {
          self.pages.forEach(function (item) {
            item.style.minHeight = 'auto'
          })
        })
      }
    },
    show: function (callback) {
      this.container.style.display = 'block'
      callback && callback.call(this)
      var arr = this.eventType['show']
      if (arr && Array.isArray(arr)) {
        for (var i = 0; i < arr.length; i++) {
          arr[i] && arr[i].call(this)
        }
      }
    },
    hide: function (callback) {
      this.container.style.display = 'none'
      callback && callback.call(this)
      var arr = this.eventType['hide']
      if (arr && Array.isArray(arr)) {
        for (var i = 0; i < arr.length; i++) {
          arr[i] && arr[i].call(this)
        }
      }
    },
    on: function (type, callback) {
      if (this.eventType[type] && Array.isArray(this.eventType[type])) {
        this.eventType[type].push(callback)
      } else {
        this.eventType[type] = [callback]
      }
    },
    off: function (type) {
      if (type !== undefined) {
        this.eventType[type] = [null]
      } else {
        for (var i in this.eventType) {
          this.eventType[i] = [null]
        }
      }
    },
    goto: function (num) {
      var self = this

      if (!isNaN(num)) {
        if (self.viewerContainer) {
          self.pages = self.viewerContainer.querySelectorAll('.pageContainer')
          if (self.pages) {
            var h = 0
            if (num - 1 > 0) {
              self.pages.forEach(function (item, index) {
                var top = item.getBoundingClientRect().top
                if (index === num - 1) {
                  h = top
                }
              })
            }
            self.viewerContainer.scrollTo({
              top: h,
              behavior: 'smooth',
            })
          }
        }
      }
    },
    scrollEnable: function (flag) {
      if (flag === false) {
        this.viewerContainer.style.overflow = 'hidden'
      } else {
        this.viewerContainer.style.overflow = 'auto'
      }
      var arr = this.eventType['scrollEnable']
      if (arr && Array.isArray(arr)) {
        for (var i = 0; i < arr.length; i++) {
          arr[i] && arr[i].call(this, flag)
        }
      }
    },
    zoomEnable: function (flag) {
      if (!self.pinchZoom) {
        return
      }
      if (flag === false) {
        self.pinchZoom.disable()
      } else {
        self.pinchZoom.enable()
      }
      var arr = this.eventType['zoomEnable']
      if (arr && Array.isArray(arr)) {
        for (var i = 0; i < arr.length; i++) {
          arr[i] && arr[i].call(this, flag)
        }
      }
    },
    reset: function (callback) {
      if (self.pinchZoom) {
        self.pinchZoom.offset.y = 0
        self.pinchZoom.offset.x = 0
        self.pinchZoom.lastclientY = 0
        self.pinchZoom.zoomFactor = 1
        self.pinchZoom.update()
      }
      if (self.viewerContainer) {
        self.viewerContainer.scrollTo({
          top: 0,
          behavior: 'smooth',
        })
      }
      callback && callback.call(this)
      var arr = this.eventType['reset']
      if (arr && Array.isArray(arr)) {
        for (var i = 0; i < arr.length; i++) {
          arr[i] && arr[i].call(this)
        }
      }
    },
    destroy: function (callback) {
      this.reset()
      this.off()
      if (self.thePDF) {
        self.thePDF.destroy()
        self.thePDF = null
      }
      if (self.viewerContainer) {
        self.viewerContainer.remove()
        self.viewerContainer = null
      }
      if (self.container) {
        self.container.innerHTML = ''
      }
      self.totalNum = null
      self.pages = null
      self.initTime = 0
      self.endTime = 0
      self.viewer = null
      self.pageNum = null
      self.pageNow = null
      self.pageTotal = null
      self.loadingBar = null
      self.progress = null
      self.loadedCount = 0
      self.timer = null
      callback && callback.call(this)
      var arr = this.eventType['destroy']
      if (arr && Array.isArray(arr)) {
        for (var i = 0; i < arr.length; i++) {
          arr[i] && arr[i].call(this)
        }
      }
    },
  }
  return Pdfh5
})
