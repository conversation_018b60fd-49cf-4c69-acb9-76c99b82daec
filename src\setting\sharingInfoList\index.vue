<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘第三方共享信息收集清单</text>
      </view>
      <view class="content">
        <text class="paragraph">尊敬的用户：</text>
        <text class="paragraph">
          为保障易直聘APP的稳定运行、提升相关功能，或为您提供更优质的服务，我们可能会使用关联应用提供的服务，或接入由第三方提供的软件开发工具包（SDK）及其他服务。
        </text>
        <text class="paragraph">
          部分第三方服务可能涉及收集您的个人信息，以实现其提供的核心功能或服务。我们将对所接入的第三方服务进行严格的安全评估和合规检测，并确保其遵守相关法律法规和监管要求。
        </text>
        <text class="paragraph">
          为持续优化我们的产品和服务，确保服务的质量和稳定性，我们可能会根据实际业务需要，对接入的第三方服务进行调整，如新增、替换或停止使用某些服务。若接入第三方服务发生变更（例如因版本升级、策略调整等），我们将在本页面及时更新说明，以便您了解第三方收集和处理个人信息的最新情况。
        </text>
        <text class="paragraph">
          建议您查阅相关第三方服务的官方网站或隐私政策，以获取其数据处理方式的详细信息。
        </text>

        <view class="table-section">
          <text class="section-title">第三方共享清单</text>

          <view class="table-container">
            <scroll-view :scroll-x="true" class="table-scroll">
              <view class="table">
                <!-- 表头 -->
                <view class="table-header">
                  <view class="table-cell header-cell company">第三方公司</view>
                  <view class="table-cell header-cell service">服务名称</view>
                  <view class="table-cell header-cell purpose">使用目的/场景</view>
                  <view class="table-cell header-cell info-type">信息类型</view>
                  <view class="table-cell header-cell share-method">共享方式</view>
                  <view class="table-cell header-cell rules">第三方处理规则</view>
                </view>

                <!-- 表格数据 -->
                <view class="table-row" v-for="(item, index) in thirdPartyList" :key="index">
                  <view class="table-cell company">{{ item.company }}</view>
                  <view class="table-cell service">{{ item.serviceName }}</view>
                  <view class="table-cell purpose">{{ item.purpose }}</view>
                  <view class="table-cell info-type">{{ item.infoType }}</view>
                  <view class="table-cell share-method">{{ item.shareMethod }}</view>
                  <view class="table-cell rules">
                    <text class="rules-text">{{ item.rules }}</text>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

// 第三方共享信息数据
const thirdPartyList = ref([
  {
    company: '三六零安全科技股份有限公司',
    serviceName: '三六零天御',
    purpose: '360加固',
    infoType: '设备标识符(ANDROID_ID)、应用信息(设备型号、系统版本、设备唯一标识符等)',
    shareMethod: 'SDK自动收集',
    rules: '官方链接：https://dev.360.cn/\n隐私政策链接：https://jiagu.360.cn/#/global/help/322',
  },
  {
    company: '维沃移动通信有限公司',
    serviceName: 'VIVO推送',
    purpose: '用于消息推送',
    infoType: '应用信息、尝试写入SD卡数据（创建）',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://dev.vivo.com.cn/?from=header\n隐私政策链接：https://dev.vivo.com.cn/documentCenter/doc/652',
  },
  {
    company: 'OPPO广东移动通信有限公司',
    serviceName: 'OPPO推送',
    purpose: '用于消息推送',
    infoType: '应用信息、尝试写入SD卡数据（创建）',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://open.oppomobile.com/\n隐私政策链接：https://open.oppomobile.com/new/developmentDoc/info?id=11228',
  },
  {
    company: '深圳市腾讯计算机系统有限公司',
    serviceName: 'QQ',
    purpose: '用于授权登录、内容分享',
    infoType: '应用信息、尝试写入SD卡数据（创建）',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://connect.qq.com/\n隐私政策链接：https://wiki.connect.qq.com/qq%e4%ba%92%e8%81%94sdk%e9%9a%90%e7%a7%81%e4%bf%9d%e6%8a%a4%e5%a3%b0%e6%98%8e',
  },
  {
    company: '华为终端有限公司',
    serviceName: '华为推送',
    purpose: '用于消息推送',
    infoType: '设备标识符(ANDROID_ID)、WiFi信息、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://developer.huawei.com/consumer/cn/\n隐私政策链接：https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/sdk-data-security-0000001050042177',
  },
  {
    company: '华为终端有限公司',
    serviceName: 'HarmonyOS开发者官网',
    purpose: '用于HarmonyOS应用的开发、上架与管理，助力开发者开发鸿蒙生态应用',
    infoType: '通过HarmonyOS开发的开发、上架与管理，助力开发者开发鸿蒙生态应用',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://developer.huawei.com/consumer/cn/\n隐私政策链接：https://developer.huawei.com/consumer/cn/doc/HMSCore-Guides/sdk-data-security-0000001050042177',
  },
  {
    company: '支付宝（中国）网络技术有限公司',
    serviceName: '支付宝',
    purpose: '帮助用户在应用内使用支付宝',
    infoType: '设备标识符（ANDROID_ID）、WiFi信息、IP地址、应用信息、传感器信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://opendocs.alipay.com/open\n隐私政策链接：https://opendocs.alipay.com/open/54/01g6qm',
  },
  {
    company: '深圳市腾讯计算机系统有限公司',
    serviceName: '微信',
    purpose: '用于帮助用户将内容分享至微信、支持微信支付',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://open.weixin.qq.com/\n隐私政策链接：https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8',
  },
  {
    company: '深圳市腾讯计算机系统有限公司',
    serviceName: '腾讯应用开放平台',
    purpose: '为APP提供在腾讯应用宝等平台的上架、推广机会，实现应用分发与变现',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官方链接：https://open.qq.com/\n隐私政策链接：https://privacy.qq.com/policy/tencent-privacypolicy\n腾讯隐私保护相关政策：https://m.baike.com/wiki/%E8%85%BE%E8%AE%AF/259627?baike_source=doubao',
  },
  {
    company: '小米科技有限责任公司',
    serviceName: '小米推送',
    purpose: '用于消息推送',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://dev.mi.com/console/appservice/push.html\n隐私政策链接：https://dev.mi.com/console/doc/detail?pId=1822',
  },
  {
    company: '小米科技有限责任公司',
    serviceName: '小米开放平台',
    purpose: '用于APP在小米应用商店等渠道的上架、推广及开发者服务提供',
    infoType: 'APP数据、开发者信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://dev.mi.com/console/appservice/push.html\n隐私政策链接：https://dev.mi.com/console/doc/detail?pId=1822',
  },
  {
    company: '苹果公司（Apple Inc.）',
    serviceName: '苹果推送',
    purpose: '用于消息推送',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '遵循苹果开发者协议以及苹果隐私政策，详情可查看：https://developer.apple.com/support/app-store/terms/ 与 https://www.apple.com/privacy/',
  },
  {
    company: '苹果公司（Apple Inc.）',
    serviceName: 'Apple Developer',
    purpose: '用于APP在苹果应用商城的上架',
    infoType: 'APP相关信息（如名称、版本、功能介绍等）、开发者账号信息',
    shareMethod: 'SDK自动收集',
    rules:
      '遵循苹果开发者协议以及苹果隐私政策，详情可查看：https://developer.apple.com/support/app-store/terms/ 与 https://www.apple.com/privacy/',
  },
  {
    company: '联想控股股份有限公司（Lenovo Holdings Co., Ltd.）',
    serviceName: '联想开放平台',
    purpose: '为APP提供在联想应用商店等的上架',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官方链接：https://dev.lenovo.com.cn/\n隐私政策链接（可在官网查找对应说明）：暂无明确公开统一链接',
  },
  {
    company: '三六零安全科技股份有限公司',
    serviceName: '360移动开放平台',
    purpose: '帮助APP在360手机助手等平台上线',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules: '官方链接：https://dev.360.cn/\n隐私政策链接：https://dev.360.cn/agreement.html',
  },
  {
    company: '魅族科技',
    serviceName: '魅族推送',
    purpose: '用于APP在魅族应用商店的上架',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules: '官方链接：https://open.flyme.cn/\n隐私政策链接：https://www.meizu.com/legal.html',
  },
  {
    company: '魅族科技',
    serviceName: '魅族开放平台',
    purpose: '用于消息推送',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules: '官方链接：https://open.flyme.cn/\n隐私政策链接：https://www.meizu.com/legal.html',
  },
  {
    company: '三星电子',
    serviceName: '三星推送',
    purpose: '用于消息推送',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官方链接：https://developer.samsung.com/cn/\n隐私政策链接：https://www.samsung.com/cn/about-us/privacy/',
  },
  {
    company: '三星电子',
    serviceName: '开放平台',
    purpose: '用于APP在魅族应用商店的上架',
    infoType: '设备标识符(ANDROID_ID)、应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官方链接：https://developer.samsung.com/cn/\n隐私政策链接：https://www.samsung.com/cn/about-us/privacy/',
  },
  {
    company: '北京亿思摩博网络科技有限公司',
    serviceName: '环信IM',
    purpose: '用于用户聊天',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules: '官网链接：https://www.easemob.com/\n隐私政策链接：https://www.easemob.com/protocol',
  },
  {
    company: '阿里云计算有限公司',
    serviceName: '对象存储OSS',
    purpose: '用于储存图片视频',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://www.aliyun.com/\n阿里云官网Cookies政策链接：https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html?spm=5176.28507329.J_4NWEMkQ5nDwOgLi8EJmHs.29.776e2868oyRK3L',
  },
  {
    company: '阿里云计算有限公司',
    serviceName: '短信服务',
    purpose: '用于发送验证码给用户',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://www.aliyun.com/\n阿里云官网Cookies政策链接：https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html?spm=5176.28507329.J_4NWEMkQ5nDwOgLi8EJmHs.29.776e2868oyRK3L',
  },
  {
    company: '阿里云计算有限公司',
    serviceName: '通义大模型',
    purpose: '用于用户提问回答',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://bailian.console.aliyun.com/\n阿里云官网Cookies政策链接：https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html?spm=5176.28507329.J_4NWEMkQ5nDwOgLi8EJmHs.29.776e2868oyRK3L',
  },
  {
    company: '阿里云计算有限公司',
    serviceName: '身份证二要素实名认证',
    purpose: '用于验证用户真实身份',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://www.aliyun.com/\n阿里云官网Cookies政策链接：https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html?spm=5176.28507329.J_4NWEMkQ5nDwOgLi8EJmHs.29.776e2868oyRK3L',
  },
  {
    company: '阿里云计算有限公司',
    serviceName: 'OCR识别',
    purpose: '用于识别营业执照信息',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://www.aliyun.com/\n阿里云官网Cookies政策链接：https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html?spm=5176.28507329.J_4NWEMkQ5nDwOgLi8EJmHs.29.776e2868oyRK3L',
  },
  {
    company: '阿里云计算有限公司',
    serviceName: 'OCR核查营业执照',
    purpose: '用于核验营业执照',
    infoType: '应用信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://www.aliyun.com/\n阿里云官网Cookies政策链接：https://terms.alicdn.com/legal-agreement/terms/platform_service/20220906101446934/20220906101446934.html?spm=5176.28507329.J_4NWEMkQ5nDwOgLi8EJmHs.29.776e2868oyRK3L',
  },
  {
    company: '中华人民共和国自然资源部',
    serviceName: '定位',
    purpose: '用于用户定位位置',
    infoType: '应用信息、位置信息',
    shareMethod: 'SDK自动收集',
    rules:
      '官网链接：https://www.tianditu.gov.cn/\n版权声明：https://www.tianditu.gov.cn/about/copyright',
  },
])
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.table-section {
  margin-top: 40rpx;
}

.section-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.section-subtitle {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
}

.table-container {
  overflow: hidden;
  background: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
}

.table-scroll {
  width: 100%;
}

.table {
  width: 100%;
  min-width: 1200rpx;
}

.table-header {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1rpx solid #e0e0e0;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;

  &:last-child {
    border-bottom: none;
  }

  &:nth-child(even) {
    background: #fafafa;
  }
}

.table-cell {
  padding: 20rpx 15rpx;
  font-size: 24rpx;
  line-height: 1.4;
  word-break: break-all;
  border-right: 1rpx solid #e0e0e0;

  &:last-child {
    border-right: none;
  }

  &.header-cell {
    font-weight: bold;
    color: #333333;
    background: #f5f5f5;
  }

  &.company {
    width: 200rpx;
    min-width: 200rpx;
  }

  &.service {
    width: 150rpx;
    min-width: 150rpx;
  }

  &.purpose {
    width: 200rpx;
    min-width: 200rpx;
  }

  &.info-type {
    width: 250rpx;
    min-width: 250rpx;
  }

  &.share-method {
    width: 120rpx;
    min-width: 120rpx;
  }

  &.rules {
    width: 280rpx;
    min-width: 280rpx;
  }
}

.rules-text {
  word-break: break-all;
  white-space: pre-line;
}
</style>
