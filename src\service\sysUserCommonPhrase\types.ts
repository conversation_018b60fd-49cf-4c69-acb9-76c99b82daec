export interface sysUserCommonPhraseQueryListDataInt {
  content?: string
}
export interface sysUserCommonPhraseQueryListInt {
  /** 常用语内容 */
  content: string
  /** 通知消息表主键id */
  id: number
  /** 排序值升序 */
  sortNo: number
  /** 是否为默认 */
  isDefault: Api.Common.EnableStatus
  /** 系统缺省key */
  systemDefaultKey: string
}

export interface sysUserCommonPhraseDeleteByIdDataInt
  extends Pick<sysUserCommonPhraseQueryListInt, 'id'> {}

export interface sysUserCommonPhraseReSortDataInt {
  commonPhraseList: Pick<sysUserCommonPhraseQueryListInt, 'id' | 'content' | 'systemDefaultKey'>[]
}

export interface sysUserCommonPhraseAddInt
  extends Partial<Omit<sysUserCommonPhraseQueryListInt, 'id'>> {}

export interface sysUserCommonPhraseUpdateInt extends Omit<sysUserCommonPhraseQueryListInt, 'id'> {
  /** 通知消息表主键id */
  id?: number
}
