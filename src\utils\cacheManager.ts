/**
 * 缓存管理工具
 * 用于获取缓存大小和清理缓存
 */

interface CacheInfo {
  totalSize: string
  storageSize: string
  fileSize: string
  availableSpace: string
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的大小字符串
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
}

/**
 * 计算字符串的字节大小（兼容所有端）
 * @param str 字符串
 * @returns 字节大小
 */
const getStringSize = (str: string): number => {
  let size = 0
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i)
    if (code <= 0x7f) {
      size += 1
    } else if (code <= 0x7ff) {
      size += 2
    } else if (code <= 0xffff) {
      size += 3
    } else {
      size += 4
    }
  }
  return size
}

/**
 * 计算对象的字节大小
 * @param obj 对象
 * @returns 字节大小
 */
const getObjectSize = (obj: any): number => {
  const str = JSON.stringify(obj)
  return getStringSize(str)
}

/**
 * 获取本地存储的缓存大小
 * @returns 缓存大小信息
 */
export const getStorageCacheSize = (): Promise<CacheInfo> => {
  return new Promise((resolve) => {
    try {
      // 获取所有存储键
      const storageInfo = uni.getStorageInfoSync()
      let totalSize = 0

      // 计算每个存储项的大小
      storageInfo.keys.forEach((key) => {
        try {
          const value = uni.getStorageSync(key)
          const size = getObjectSize(value)
          totalSize += size
        } catch (error) {
          console.warn(`获取存储项 ${key} 大小失败:`, error)
        }
      })

      // 简化版本：所有端都显示未知，因为获取设备存储信息比较复杂
      const availableSpace = '未知'

      resolve({
        totalSize: formatFileSize(totalSize),
        storageSize: formatFileSize(totalSize),
        fileSize: '0B', // 文件缓存大小，需要单独计算
        availableSpace,
      })
    } catch (error) {
      console.error('获取缓存大小失败:', error)
      resolve({
        totalSize: '0B',
        storageSize: '0B',
        fileSize: '0B',
        availableSpace: '未知',
      })
    }
  })
}

/**
 * 获取文件缓存大小（简化版本）
 * @returns 文件缓存大小
 */
export const getFileCacheSize = (): Promise<string> => {
  return new Promise((resolve) => {
    // 简化版本，返回0B
    resolve('0B')
  })
}

/**
 * 清理本地存储缓存
 * @param keys 要清理的键数组，为空则清理所有
 * @returns 清理结果
 */
export const clearStorageCache = (keys?: string[]): Promise<boolean> => {
  return new Promise((resolve) => {
    try {
      if (keys && keys.length > 0) {
        // 清理指定的键
        keys.forEach((key) => {
          try {
            uni.removeStorageSync(key)
          } catch (error) {
            console.warn(`清理存储键 ${key} 失败:`, error)
          }
        })
      } else {
        // 清理所有存储
        const storageInfo = uni.getStorageInfoSync()
        storageInfo.keys.forEach((key) => {
          try {
            uni.removeStorageSync(key)
          } catch (error) {
            console.warn(`清理存储键 ${key} 失败:`, error)
          }
        })
      }

      resolve(true)
    } catch (error) {
      console.error('清理存储缓存失败:', error)
      resolve(false)
    }
  })
}

/**
 * 清理文件缓存（简化版本）
 * @returns 清理结果
 */
export const clearFileCache = (): Promise<boolean> => {
  return new Promise((resolve) => {
    // 简化版本，直接返回成功
    resolve(true)
  })
}

/**
 * 清理所有缓存（存储 + 文件）
 * @returns 清理结果
 */
export const clearAllCache = async (): Promise<boolean> => {
  try {
    const storageResult = await clearStorageCache()
    const fileResult = await clearFileCache()
    return storageResult && fileResult
  } catch (error) {
    console.error('清理所有缓存失败:', error)
    return false
  }
}

/**
 * 获取完整的缓存信息
 * @returns 缓存信息
 */
export const getCacheInfo = async (): Promise<CacheInfo> => {
  try {
    const storageInfo = await getStorageCacheSize()
    const fileSize = await getFileCacheSize()

    // 直接使用storageInfo的totalSize，避免重复计算
    return {
      totalSize: storageInfo.totalSize,
      storageSize: storageInfo.storageSize,
      fileSize,
      availableSpace: storageInfo.availableSpace,
    }
  } catch (error) {
    console.error('获取缓存信息失败:', error)
    return {
      totalSize: '0B',
      storageSize: '0B',
      fileSize: '0B',
      availableSpace: '未知',
    }
  }
}
