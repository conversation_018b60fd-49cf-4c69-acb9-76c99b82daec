import type {
  FormItemRule,
  FormInstance,
  ErrorMessage,
} from 'wot-design-uni/components/wd-form/types'

export type RuleInt = Pick<FormItemRule, 'message' | 'pattern' | 'validator'> & {
  required?: boolean
}
/** 表单hooks */
export const useForm = () => {
  /** 实例 */
  const formRef = ref<FormInstance | null>(null)
  /** 校验 */
  async function validate(callback?: (errors: ErrorMessage[]) => void) {
    const { valid, errors } = await formRef.value.validate()
    if (!valid) {
      callback && callback(errors)
      return Promise.reject(errors)
    }
    if (valid) {
      return Promise.resolve()
    }
  }
  /** 重置表单项的验证提示 */
  async function reset() {
    formRef.value?.reset()
  }

  return {
    formRef,
    validate,
    reset,
  }
}
/** 表单验证规则 */
export const useFormRules = () => {
  function createRequiredRule(
    message: string,
    pattern?: RegExp,
    required: boolean = true,
  ): FormItemRule {
    return {
      required,
      message,
      pattern,
    }
  }
  return {
    createRequiredRule,
  }
}
