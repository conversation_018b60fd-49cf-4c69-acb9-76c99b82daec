<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar class="base_header" title="人工客服"></CustomNavBar>
    </template>
    <web-view
      :fullscreen="false"
      :src="url"
      :webview-styles="webviewStyles"
      @message="handleMessage"
      :adjust-position="true"
      :adjust-keyboard="true"
      :keyboard-height="keyboardHeight"
    ></web-view>
  </z-paging>
</template>

<script lang="ts" setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const url = ref(null)

// web-view样式配置
const webviewStyles = ref({
  progress: {
    color: '#FF3333',
  },
  // 添加键盘相关样式
  keyboard: {
    adjustPosition: true,
    adjustKeyboard: true,
  },
})

// 键盘高度
const keyboardHeight = ref(0)

// 处理web-view消息
const handleMessage = (event: any) => {
  console.log('web-view message:', event.detail)
}

onLoad((options) => {
  url.value = decodeURIComponent(options.url)
})

onMounted(() => {
  const instance = getCurrentInstance()
  let totalTop = 0 // 声明totalTop变量

  const { platform, windowHeight, safeArea } = uni.getSystemInfoSync()

  if (platform === 'android') {
    // 只在安卓下监听键盘高度变化
    uni.onKeyboardHeightChange((res) => {
      keyboardHeight.value = res.height

      if (instance && instance.proxy) {
        const currentWebview = (instance.proxy as any).$scope?.$getAppWebview()
        if (currentWebview) {
          const wv = currentWebview.children()?.[0]
          if (wv) {
            const bottomSafeArea = safeArea ? windowHeight - safeArea.bottom : 0
            const totalBottomSpace = bottomSafeArea + res.height + 20 // 键盘高度 + 缓冲

            wv.setStyle({
              height: `${windowHeight - totalTop - totalBottomSpace}px`,
            })
          }
        }
      }
    })
  }

  const query = uni.createSelectorQuery().in(instance)
  // 屏幕高度和安全区域
  // const { windowHeight, safeArea } = uni.getSystemInfoSync() // 屏幕高度和安全区域

  if (instance && instance.proxy) {
    // 使用类型断言来解决TypeScript错误
    const currentWebview = (instance.proxy as any).$scope?.$getAppWebview()
    if (currentWebview) {
      nextTick(() => {
        setTimeout(() => {
          const closeHeight = 0
          let baseHeaderHeight = 0

          query
            .select('.base_header')
            .boundingClientRect((res) => {
              // 修复类型错误，确保res是单个对象而不是数组
              const rect = Array.isArray(res) ? res[0] : res
              if (rect && rect.height) {
                baseHeaderHeight = rect.height
              } else {
                baseHeaderHeight = 50 // 默认高度
              }
            })
            .exec(() => {
              totalTop = closeHeight + baseHeaderHeight // 更新totalTop的值
              console.log('Calculated totalTop:', totalTop)

              const wv = currentWebview.children()?.[0]
              if (wv) {
                // 计算底部安全区域高度
                const bottomSafeArea = safeArea ? windowHeight - safeArea.bottom : 0
                // 为底部输入框预留更多空间，通常输入框高度约60px
                const bottomInputHeight = 20
                // 总底部预留空间
                const totalBottomSpace = bottomSafeArea + bottomInputHeight + 20 // 额外20px缓冲

                wv.setStyle({
                  top: `${totalTop}px`,
                  height: `${windowHeight - totalTop - totalBottomSpace}px`,
                  zIndex: -1,
                })
              }
            })
        }, 300)
      })
    }
  }
})
</script>

<style lang="scss"></style>
