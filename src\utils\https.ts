import { baseUrl } from '@/interceptors/request'
import { useUserInfo } from '@/hooks/common/useUserInfo'

/**
 * 流式数据处理配置接口
 */
interface StreamConfig {
  url: string
  data?: any
  onMessage?: (data: any) => void
  onError?: (error: any) => void
  onComplete?: () => void
  onProgress?: (chunk: string) => void
}

/**
 * 创建流式请求 - uni-app 兼容版本
 * @param config 流式请求配置
 * @returns 请求控制对象
 */
export function createStreamRequest(config: StreamConfig) {
  const { url, data = {}, onMessage, onError, onComplete, onProgress } = config
  const { getToken } = useUserInfo()

  const fullUrl = `${baseUrl}${url}`
  const token = getToken()

  // 检查是否在支持 XMLHttpRequest 的环境中（如 H5）
  if (typeof XMLHttpRequest !== 'undefined') {
    return createXHRStreamRequest(config, fullUrl, token)
  } else {
    // 在不支持 XMLHttpRequest 的环境中使用 uni.request 模拟
    return createSimpleUniRequest(config, fullUrl, token)
  }
}

/**
 * 使用 XMLHttpRequest 创建流式请求（H5 环境）
 */
function createXHRStreamRequest(config: StreamConfig, fullUrl: string, token: string) {
  const { data = {}, onMessage, onError, onComplete, onProgress } = config

  const xhr = new XMLHttpRequest()
  xhr.open('POST', fullUrl, true)

  // 设置请求头
  xhr.setRequestHeader('Content-Type', 'application/json')
  xhr.setRequestHeader('Accept', 'text/event-stream')
  xhr.setRequestHeader('Cache-Control', 'no-cache')
  xhr.setRequestHeader('Connection', 'keep-alive')

  if (token) {
    xhr.setRequestHeader('token', token)
  }

  let buffer = ''
  let lastProcessedLength = 0

  xhr.onprogress = function (event) {
    try {
      const chunk = xhr.responseText.substring(lastProcessedLength)
      lastProcessedLength = xhr.responseText.length
      buffer += chunk

      onProgress?.(chunk)
      processStreamData(buffer, onMessage)
    } catch (error) {
      console.error('处理流式数据时出错:', error)
      onError?.(error)
    }
  }

  xhr.onreadystatechange = function () {
    if (xhr.readyState === XMLHttpRequest.DONE) {
      if (xhr.status === 200) {
        if (buffer) {
          processStreamData(buffer, onMessage, true)
        }
        onComplete?.()
      } else {
        const error = new Error(`请求失败: ${xhr.status} ${xhr.statusText}`)
        onError?.(error)
      }
    }
  }

  xhr.onerror = function (event) {
    const error = new Error('网络请求失败')
    onError?.(error)
  }

  xhr.ontimeout = function () {
    const error = new Error('请求超时')
    onError?.(error)
  }

  xhr.timeout = 300000

  const postData = JSON.stringify(data)
  xhr.send(postData)

  return xhr
}

/**
 * 使用 uni.request 模拟流式请求（小程序/App 环境）
 */
function createSimpleUniRequest(config: StreamConfig, fullUrl: string, token: string) {
  const { data = {}, onMessage, onError, onComplete, onProgress } = config

  let isAborted = false

  const requestTask = uni.request({
    url: fullUrl,
    method: 'POST',
    data,
    header: {
      'Content-Type': 'application/json',
      Accept: 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      ...(token ? { token } : {}),
    },
    timeout: 300000,
    success: (res) => {
      if (isAborted) return

      try {
        // 在非流式环境中，我们收到完整响应后模拟流式输出
        const responseText = typeof res.data === 'string' ? res.data : JSON.stringify(res.data)

        // 模拟流式输出
        simulateStreamOutput(responseText, onMessage, onProgress, onComplete, () => isAborted)
      } catch (error) {
        console.error('处理响应数据时出错:', error)
        onError?.(error)
      }
    },
    fail: (error) => {
      if (isAborted) return
      console.error('请求失败:', error)
      onError?.(new Error(`请求失败: ${error.errMsg || '未知错误'}`))
    },
  })

  // 返回控制对象
  return {
    abort: () => {
      isAborted = true
      requestTask?.abort?.()
    },
    readyState: 4,
    status: 200,
  }
}

/**
 * 模拟流式输出（用于不支持真正流式的环境）
 */
async function simulateStreamOutput(
  responseText: string,
  onMessage?: (data: any) => void,
  onProgress?: (chunk: string) => void,
  onComplete?: () => void,
  isAborted?: () => boolean,
) {
  try {
    // 首先尝试解析为完整的响应
    let content = ''

    // 尝试从不同格式中提取内容
    try {
      const parsed = JSON.parse(responseText)
      if (parsed.data) {
        content = parsed.data
      } else if (parsed.content) {
        content = parsed.content
      } else if (parsed.text) {
        content = parsed.text
      } else if (typeof parsed === 'string') {
        content = parsed
      } else {
        content = responseText
      }
    } catch (e) {
      content = responseText
    }

    // 模拟逐字符输出
    const chars = content.split('')
    let accumulatedContent = ''

    for (let i = 0; i < chars.length; i++) {
      if (isAborted?.()) break

      const char = chars[i]
      accumulatedContent += char

      // 触发进度回调
      onProgress?.(char)

      // 触发消息回调
      onMessage?.({
        content: char,
        type: 'text',
        accumulated: accumulatedContent,
      })

      // 模拟网络延迟
      await new Promise((resolve) => setTimeout(resolve, 50))
    }

    if (!isAborted?.()) {
      onComplete?.()
    }
  } catch (error) {
    console.error('模拟流式输出时出错:', error)
    onMessage?.({
      content: responseText,
      type: 'text',
    })
    onComplete?.()
  }
}

/**
 * 处理流式数据
 * @param buffer 缓冲区数据
 * @param onMessage 消息回调
 * @param isComplete 是否为最后一次处理
 */
function processStreamData(buffer: string, onMessage?: (data: any) => void, isComplete = false) {
  // 按行分割数据
  const lines = buffer.split('\n')

  lines.forEach((line, index) => {
    // 跳过空行
    if (!line.trim()) return

    // 处理 [DONE] 标记
    if (line.includes('[DONE]')) {
      return
    }

    // 处理 data: 开头的行
    if (line.startsWith('data:')) {
      const content = line.substring(5).trim()
      if (content) {
        try {
          // 尝试解析JSON
          const parsedData = JSON.parse(content)
          onMessage?.(parsedData)
        } catch (e) {
          // 如果不是JSON，直接作为文本处理
          onMessage?.({ content, type: 'text' })
        }
      }
    } else {
      // 处理其他格式的数据
      try {
        const parsedData = JSON.parse(line)
        onMessage?.(parsedData)
      } catch (e) {
        // 如果解析失败，作为纯文本处理
        if (line.trim()) {
          onMessage?.({ content: line, type: 'text' })
        }
      }
    }
  })
}
