<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="微信号"></CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="flex-c border-b p-b-40rpx p-t-40rpx">
        <view class="text-30rpx m-r-10rpx w-40 font-500">微信号</view>
        <wd-input v-model="wxCode" no-border placeholder="请输入微信号"></wd-input>
      </view>
      <view v-if="wxCodeOrigin" class="flex-c p-t-20rpx text-24rpx c-#666">
        当前微信号：{{ wxCodeOrigin }}
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="submit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { updateWxCode, queryWxCode } from '@/interPost/my'
import { updateHrWxCode, queryHrWxCode } from '@/service/my'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { userRoleIsBusiness } = useUserInfo()
// 微信号脱敏
const wxCode = ref('')
// 微信号
const wxCodeOrigin = ref('')
// 获取微信
// const getWxCode = async () => {
//   const res: any = userRoleIsBusiness.value ? await queryHrWxCode() : await queryWxCode()
//   if (res.code === 0) {
//     wxCodeOrigin.value = res?.data ? res.data : ''
//   }
// }
onLoad(async (options) => {
  await uni.$onLaunched
  wxCodeOrigin.value = options.wxCode
  // getWxCode()
})

const submit = async () => {
  // 用户名
  if (!wxCode.value) {
    uni.showToast({
      title: '请输入微信名',
      icon: 'none',
    })
    return
  }

  const res: any = userRoleIsBusiness.value
    ? await updateHrWxCode({ wxCode: wxCode.value })
    : await updateWxCode({ wxCode: wxCode.value })
  console.log(res, 'res====')
  if (res.code === 0) {
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  background-color: transparent;
}

.labelName {
  width: 200rpx;
  text-align: left;
}
.position-r {
  position: absolute;
  top: 10rpx;
  right: 0rpx;
}

.pageContaner {
  padding: 0rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
