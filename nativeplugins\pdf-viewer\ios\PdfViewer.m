#import "PdfViewer.h"
#import <PDFKit/PDFKit.h>

@interface PdfViewer()
@property (nonatomic, strong) PDFView *pdfView;
@end

@implementation PdfViewer

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        self.pdfView = [[PDFView alloc] initWithFrame:self.bounds];
        self.pdfView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self addSubview:self.pdfView];
        self.pdfView.autoScales = YES;
    }
    return self;
}

- (void)setSrc:(NSString *)path {
    if (!path) return;
    NSURL *url = [NSURL fileURLWithPath:path];
    PDFDocument *document = [[PDFDocument alloc] initWithURL:url];
    [self.pdfView setDocument:document];
}

@end