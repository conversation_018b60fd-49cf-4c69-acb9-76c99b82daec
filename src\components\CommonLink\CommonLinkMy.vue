<template>
  <view class="box_width">
    <view class="containner-item">
      <view class="p-r-40rpx c-#ACACAC text-24rpx" @click="show1 = true">法律顾问</view>
      <view class="p-r-40rpx c-#ACACAC text-24rpx" @click="businesslicense">营业执照</view>
      <view class="c-#ACACAC text-24rpx" @click="gorlzyPAGE">人力资源服务许可证</view>
    </view>
    <view class="containner-item">
      <view class="p-r-40rpx c-#ACACAC text-24rpx" @click="goWZ">渝B2-20250575</view>
      <view class="c-#ACACAC text-24rpx" @click="goICP">渝ICP备**********号-2A</view>
    </view>
    <view class="containner-item">
      <view class="p-r-40rpx c-#ACACAC text-24rpx">客服电话:400-965-9675</view>
      <view class="c-#ACACAC text-24rpx">工作时间:09:00-18:00</view>
    </view>
    <wd-popup
      v-model="show1"
      position="bottom"
      closable
      custom-style="border-radius:32rpx 32rpx 0rpx 0rpx;padding:60rpx 40rpx 180rpx;"
    >
      <view class="">
        <view class="subText p-b-20rpx">法律顾问</view>
        <view class="mainText font-w-500">重庆立标律师事务所</view>
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const show1 = ref(false)
const businesslicense = () => {
  uni.navigateTo({
    url: '/setting/certificateImage/businesslicense',
  })
}
const gorlzyPAGE = () => {
  uni.navigateTo({
    url: '/setting/certificateImage/humanResources',
  })
}
const goWZ = () => {
  uni.navigateTo({
    url: '/setting/certificateImage/TelServices',
  })
}
const goICP = () => {
  uni.navigateTo({
    url:
      '/loginSetting/Externalfiling/index?url=' + encodeURIComponent('https://beian.miit.gov.cn/'),
  })
}
</script>

<style scoped lang="scss">
.box_width {
  width: 100%;
  .containner-item {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5rpx;
  }
}
</style>
