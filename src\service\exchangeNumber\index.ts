import { POST } from '../index'
import { exchangeDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** C端用户发起交换电话的请求接口 */
export const exchangeNumberExchange = (data: exchangeDataInt, config?: HttpRequestConfig) =>
  POST<number>('/easyzhipin-api/exchangeNumber/exchange', data, config)

/** C端用户同意HR发送的交换电话请求处理接口 */
export const exchangeNumberDoExchange = (
  data: Api.IM.CustomMessage.ModifyCustomMessage,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/exchangeNumber/doExchange', data, config)
