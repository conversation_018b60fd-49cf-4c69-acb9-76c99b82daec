import { POSTPaging, POST } from '../index'
import { HttpRequestConfig } from 'luch-request'
import {
  interviewDetailIntId,
  interviewListInt,
  unInterestListInt,
  removeUnInterestInt,
} from './types'

/** 被收藏列表 */
export const beenCollectList = (data: unInterestListInt, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/hrResume/beenCollectList', data, config)
// 不合适列表
export const unInterestList = (data: unInterestListInt, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/hrResume/unInterestList', data, config)
// 移出不合适
export const removeUnInterest = (data: removeUnInterestInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrResume/removeUnInterest', data, config)
// 沟通过
export const ditchThroughList = (data: unInterestListInt, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/hrResume/ditchThroughList', data, config)
// 面试列表
export const hrInterviewRecordList = (data: interviewListInt, config?: HttpRequestConfig) =>
  POSTPaging('/easyzhipin-api/hrInterviewRecord/queryList', data, config)
// 面试详情b
export const hrInterviewRecordDetail = (data: interviewDetailIntId, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrInterviewRecord/queryById', data, config)
// 面试详情
export const queryByInterViewId = (data: interviewDetailIntId, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/myDetails/queryByInterViewId', data, config)
