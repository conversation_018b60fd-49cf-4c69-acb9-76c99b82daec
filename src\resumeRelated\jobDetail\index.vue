<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar>
        <template #right>
          <wd-img
            v-if="objJibDeatil.collectStatus === 0 && userType !== 'business'"
            :height="22"
            :src="collect"
            :width="22"
            @click="collectFun"
          />
          <wd-img
            v-if="objJibDeatil.collectStatus === 1 && userType !== 'business'"
            :height="22"
            :src="noCollect"
            :width="22"
            @click="nocollectFun"
          />
        </template>
      </CustomNavBar>
    </template>

    <view class="jobDetail-list">
      <view class="jobDetail-title flex-between">
        <view class="jobDetail-title-main">
          {{
            objJibDeatil.positionMarkName
              ? objJibDeatil.positionMarkName
              : objJibDeatil.positionName
          }}
        </view>
        <view class="jobDetail-title-salary">
          {{ handleSalaryDisplay(objJibDeatil.workSalaryBegin, objJibDeatil.workSalaryEnd) }}
          <text v-if="objJibDeatil?.salaryMonths === 12 && objJibDeatil.workSalaryBegin !== '面议'">
            {{ objJibDeatil?.salaryMonths === 12 ? '/月' : '·' + objJibDeatil.salaryMonths + '薪' }}
          </text>
        </view>
      </view>
      <view class="jobDetail-subtitle">
        <view class="flex-c">
          <wd-icon color="#888" name="location" size="16px"></wd-icon>
          <view class="m-l-2rpx subText">
            {{
              handleAddress(
                objJibDeatil.provinceName,
                objJibDeatil.cityName,
                objJibDeatil.districtName,
              )
            }}
          </view>
        </view>
        <view v-if="objJibDeatil.isToDayPublish" class="subText jobDetail-subtitle-text">
          该岗位于今日新发布
        </view>
      </view>
      <view class="jobDetail-card flex-c" @click="goChat">
        <!-- <image class="jobDetail-card-img" src="/static/img/1.jpg"></image> -->

        <!-- <view class="'border-twinkle bg_left_icon_box mt-10rpx'"> -->
        <view
          :class="
            objJibDeatil.hxUserInfoVO?.isOnline || objJibDeatil.activityStatus === 1
              ? 'border-twinkle bg_left_icon_box'
              : 'mt-10rpx'
          "
        >
          <image
            v-if="objJibDeatil.sex === 1"
            :src="objJibDeatil.hrUrl ? objJibDeatil.hrUrl : '/static/header/hrheader1.png'"
            class="jobDetail-card-img"
            mode="aspectFill"
          ></image>
          <image
            v-else
            :src="objJibDeatil.hrUrl ? objJibDeatil.hrUrl : '/static/header/hrheader2.png'"
            class="jobDetail-card-img"
            mode="aspectFill"
          ></image>
        </view>
        <view class="jobDetail-card-bg">
          <view class="jobDetail-card-info">
            <view class="jobDetail-card-content">
              <view class="jobDetail-card-name text-28rpx p-b-6rpx flex items-center">
                {{ objJibDeatil.hrPositionName }}
                <text v-if="objJibDeatil.hrPosition">·</text>
                <text class="c-#666 text-24rpx">
                  {{ truncateText(objJibDeatil.hrPosition, 10) }}
                </text>
                <view
                  v-if="objJibDeatil.hxUserInfoVO?.isOnline || objJibDeatil.activityStatus === 1"
                  class="m-l-10rpx border-#34A715 border-1rpx border-solid c-#34A715 text-22rpx inline-block rounded-8rpx p-r-10rpx p-l-10rpx"
                >
                  在线
                </view>
              </view>
              <view class="subText">{{ truncateText(objJibDeatil.company, 14) }}</view>
              <!-- <view class="subText">今日回复6次</view> -->
            </view>
            <view v-if="userType !== 'business'" class="jobDetail-chat-icon">
              <image
                class="chat-icon"
                mode="aspectFill"
                src="/static/images/home/<USER>"
              ></image>
              <!-- 三个点动画效果 -->
              <view
                v-if="objJibDeatil.hxUserInfoVO?.isOnline || objJibDeatil.activityStatus === 1"
                class="chat-dots-animation"
              >
                <view class="dot dot1"></view>
                <view class="dot dot2"></view>
                <view class="dot dot3"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="jobDetail-content">
        <view class="jobDetail-content-title p-t-20rpx">岗位详情</view>
        <view v-if="objJibDeatil.positionKey" class="jobDetail-tag-list flex-c m-b-20rpx">
          <view
            v-for="(item, index) in objJibDeatil.positionKey"
            :key="index"
            class="jobDetail-tag"
          >
            {{ item }}
          </view>
        </view>
        <!-- <view class="jobDetail-content-name">岗位要求</view> -->
        <div class="jobDetail-content-name text-pre-wrap">{{ objJibDeatil.positionDesc }}</div>
      </view>
      <view v-if="objJibDeatil.positionBenefitVOList?.length" class="jobDetail-content">
        <view class="jobDetail-content-title p-t-40rpx">福利待遇</view>
        <view class="jobDetail-tag-list flex-c">
          <view
            v-for="(item, index) in objJibDeatil.positionBenefitVOList"
            :key="index"
            class="jobDetail-tag"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <!-- 工作地址 -->
      <view class="jobDetail-content-title p-t-30rpx">工作地址</view>
      <view class="jobDetail-address-card" @click="goMap">
        <view class="jobDetail-address-content">
          <view class="jobDetail-address-info">
            <view class="jobDetail-address-text">{{ objJibDeatil.regAddress }}</view>
          </view>
          <view v-if="objJibDeatil.distanceMeters" class="jobDetail-distance-info">
            <wd-icon color="#888" name="location" size="14px"></wd-icon>
            <view class="jobDetail-distance-text">
              {{ `距离我${objJibDeatil.distanceMeters}` }}
            </view>
          </view>
        </view>
        <view class="jobDetail-address-icon">
          <image
            class="address-icon"
            mode="aspectFill"
            src="/static/images/home/<USER>"
          ></image>
        </view>
      </view>
      <!-- 公司信息 -->
      <view class="jobDetail-content-title p-t-40rpx">公司信息</view>
      <view class="jobDetail-conpany flex-between" @click="goCompany">
        <view class="flex-c">
          <!-- <image class="jobDetail-conpany-img" src="/static/img/1.jpg"></image> -->
          <image
            :src="
              objJibDeatil.companyLogoUrl ? objJibDeatil.companyLogoUrl : '/static/header/logo.png'
            "
            class="jobDetail-conpany-img"
            mode="aspectFill"
          ></image>
          <view class="jobDetail-conpany-text">
            <view class="jobDetail-conpany-title">
              {{ truncateText(objJibDeatil.company, 12) }}
            </view>
            <view class="flex-c">
              <view class="jobDetail-conpany-subtitle p-r-40rpx">{{ objJibDeatil.sizeName }}</view>
              <view class="jobDetail-conpany-subtitle">
                {{ truncateText(objJibDeatil.industryName, 8) }}
              </view>
            </view>
          </view>
        </view>
        <view v-if="userType !== 'business'" class="jobDetail-card-icon">
          <wd-icon class="arrow-right-1" color="#317CFF" name="chevron-right" size="15px"></wd-icon>
        </view>
      </view>
      <view
        class="notice-box bg-#fff p-36rpx m-t-58rpx m-b-40rpx border-rd-40rpx flex items-center"
      >
        <wd-img :height="30" :src="notice" :width="30" class="m-r-20rpx"></wd-img>
        <view class="text-24rpx c-#ff0000 flex-1 text-left">
          以任何形式向求职者收取财(物)的行为均违反《中华人民共和国劳动法》，请注意防范，避免损失！
        </view>
      </view>
    </view>

    <template #bottom>
      <view v-if="userType !== 'business'" class="btn_fixed" @click="goChat">
        <view class="btn_box">
          <view class="btn_bg">立即沟通</view>
        </view>
      </view>
      <view v-else-if="userType === 'business'">
        <view class="btn_fixed" @click="handleEditPosition">
          <view class="btn_box">
            <view class="btn_bg">修改岗位</view>
          </view>
        </view>
        <view>
          <view v-if="jobStatus === 1" class="btn_fixed" @click="handleClosePosition">
            <view class="btn_box btn_box_close">
              <view class="btn_bg btn_bg_close">关闭岗位</view>
            </view>
          </view>
          <view
            v-else-if="jobStatus === 2 || jobStatus === 0"
            class="btn_fixed"
            @click="handlePublishPosition"
          >
            <view class="btn_box btn_box_publish">
              <view class="btn_bg btn_bg_publish">付费发布</view>
            </view>
          </view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { handleNavigation } from '@/utils/open-map-url'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { ChatUIKit } from '@/ChatUIKit/index'
import {
  positionInfoDetil,
  queryKey,
  cancelPosition,
  collectPosition,
  closePosition,
  publishPosition,
  getPositionDetail,
  publishPositionDraft,
} from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import { truncateText } from '@/utils/util'
// import collect from '@/static/img/positionSc.png'
// import noCollect from '@/static/img/positionQxsc.png'
import collect from '@/static/img/positionSc.png'
import noCollect from '@/static/img/noCollect.png'
import notice from '@/resumeRelated/img/notice.png'

const message = useMessage()
const { getDictLabel } = useDictionary()
const appUserStore = ChatUIKit.appUserStore
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage } = useIMConversation()
// id
const id = ref(null)
// companyId
const companyId = ref(null)
const objJibDeatil = ref<AnyObject>({})
const imgMap = ref('')
const userType = ref('')
const jobStatus = ref(null)
const queryParams = ref<any>({})

// 详情
const getDetail = async () => {
  const res: any = await positionInfoDetil({ id: id.value })
  if (res.code === 0) {
    res.data.positionKey = res.data.positionKey ? res.data.positionKey.split(',') : ''
    res.data.workSalaryBegin =
      res.data.workSalaryBegin === 0 ? '面议' : numberTokw(res.data.workSalaryBegin + '')
    res.data.workSalaryEnd =
      res.data.workSalaryEnd === 0 ? '面议' : numberTokw(res.data.workSalaryEnd + '')
    res.data.sizeName = await getDictLabel(100, res.data.sizeName)
    res.data.workEducational = await getDictLabel(10, res.data.workEducational)
    // 判断 positionKey 是否是数组
    if (Array.isArray(res.data.positionKey)) {
      // 安全判断工作年限字段是否存在
      if (
        typeof res.data.workExperienceStart === 'number' &&
        typeof res.data.workExperienceEnd === 'number'
      ) {
        if (res.data.workExperienceStart === 0 && res.data.workExperienceEnd === 0) {
          res.data.positionKey.unshift('经验不限')
        } else {
          res.data.positionKey.unshift(
            `${res.data.workExperienceStart} - ${res.data.workExperienceEnd}年`,
          )
        }
      }

      // 安全判断学历字段是否存在
      if (typeof res.data.workEducational === 'string') {
        if (res.data.workEducational === '全部') {
          res.data.positionKey.unshift('学历不限')
        } else {
          res.data.positionKey = [res.data.workEducational, ...res.data.positionKey]
        }
      }
    }

    jobStatus.value = res.data.status
    try {
      const eleData = appUserStore.getUserInfoFromStore(res.data.hxUserInfoVO.username)
      res.data.hxUserInfoVO.isOnline = !!eleData?.isOnline
    } catch (error) {
      // res.data.hxUserInfoVO.isOnline = false
    } // 距离处理
    if (res.data.distanceMeters) {
      const distance = Math.floor(parseInt(res.data.distanceMeters) / 1000)
      if (distance === 0) {
        res.data.distanceMeters = '<1km'
      } else {
        res.data.distanceMeters = distance + 'km'
      }
    }
    objJibDeatil.value = res.data
  }
}

// 处理薪资显示
const handleSalaryDisplay = (workSalaryBegin: any, workSalaryEnd: any) => {
  const isBeginNegotiable = workSalaryBegin === '面议'
  const isEndNegotiable = workSalaryEnd === '面议'
  if (isBeginNegotiable && isEndNegotiable) {
    return '面议'
  }
  if (!workSalaryBegin && !workSalaryEnd) {
    return ''
  }

  if (isBeginNegotiable || isEndNegotiable) {
    return isBeginNegotiable ? workSalaryEnd || '面议' : workSalaryBegin || '面议'
  }

  if (workSalaryBegin && !workSalaryEnd) return `${workSalaryBegin}`
  if (!workSalaryBegin && workSalaryEnd) return `${workSalaryEnd}`

  return `${workSalaryBegin}-${workSalaryEnd}`
}

// 处理地址显示
const handleAddress = (provinceName: any, cityName: any, districtName: any) => {
  const province = provinceName || ''
  let city = cityName || ''
  if (province && city && province === city) {
    city = ''
  }
  const parts = [province, city].filter(Boolean)
  if (districtName) {
    parts.push(districtName)
  }
  return parts.join('·')
}

// 收藏
const collectFun = async () => {
  const res: any = await collectPosition({ id: objJibDeatil.value.id })
  if (res.code === 0) {
    objJibDeatil.value.collectStatus = 1
  }
}
// 取消收藏
const nocollectFun = async () => {
  const res: any = await cancelPosition({ id: objJibDeatil.value.id })
  if (res.code === 0) {
    objJibDeatil.value.collectStatus = 0
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  // 主健id
  id.value = options.id
  // 公司id
  companyId.value = options.companyId
  // 用户类型
  userType.value = options.type
  await getDetail()
  await getMapKet()
})
const goMap = () => {
  handleNavigation({
    latitude: objJibDeatil.value.lat,
    longitude: objJibDeatil.value.lon,
    name: objJibDeatil.value.company,
  })
  // uni.openLocation({
  //   latitude: Number(objJibDeatil.value.lat), // 目标纬度
  //   longitude: Number(objJibDeatil.value.lon), // 目标经度
  //   name: objJibDeatil.value.company, // 显示在地图上的标记名称
  //   address: objJibDeatil.value.regAddress, // 辅助信息
  //   success: () => console.log('跳转成功'),
  //   fail: (err) => console.error('跳转失败', err),
  // })
}
// 获取ket
const getMapKet = async () => {
  const res = await queryKey()
  // 纬度
  const lat = objJibDeatil.value.lat
  // 精度
  const lon = objJibDeatil.value.lon
  const staticKey = res.data.staticKey
  imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${lon},${lat}&width=300&height=200&zoom=12&tk=${staticKey}&markers=${lon},${lat}`
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
// 去沟通
const goChat = async () => {
  // 企业用户不能沟通
  if (userType.value === 'business') {
    return
  }
  try {
    await userToRealName()
    const hxUserInfoVO = objJibDeatil.value?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, objJibDeatil.value)
    }
  } catch (error) {}
}

// 修改岗位
const handleEditPosition = async () => {
  uni.navigateTo({
    url: `/sub_business/pages/release/index?id=${objJibDeatil.value.id}`,
  })
}

// 关闭岗位
const handleClosePosition = async () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要关闭岗位吗?',
    })
    .then(async () => {
      const res: any = await closePosition({ id: objJibDeatil.value.id })
      if (res.code === 0) {
        uni.showToast({
          title: '操作成功',
          icon: 'success',
        })
        uni.navigateBack()
      } else {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 3000,
        })
      }
    })
}

// 付费发布
const handlePublishPosition = async () => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要付费发布吗?',
    })
    .then(async () => {
      const res: any =
        jobStatus.value === 0
          ? await publishPositionDraft({ positionId: objJibDeatil.value.id, publishMonth: 1 })
          : await publishPosition({ id: objJibDeatil.value.id })
      if (res.code === 0) {
        uni.showToast({
          title: '操作成功',
          icon: 'success',
        })
        uni.navigateBack()
      } else {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 3000,
        })
      }
    })
}

const goCompany = () => {
  if (userType.value === 'business') {
    return
  }
  uni.navigateTo({
    url: `/resumeRelated/company/index?companyId=${companyId.value}&id=${objJibDeatil.value.id}`,
  })
}
</script>

<style lang="scss" scoped>
.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 40rpx 20rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }

  .btn_box_close {
    margin-top: 5rpx;

    .btn_bg_close {
      color: #fff;
      background: #ff6b6b;
    }
  }

  .btn_box_publish {
    margin-top: 5rpx;

    .btn_bg_publish {
      color: #fff;
      background: #527bff;
    }
  }
}

.jobDetail-list {
  padding: 40rpx;
  border-radius: 36rpx;

  .jobDetail-title {
    .jobDetail-title-main {
      font-size: 36rpx;
      font-weight: bold;
      line-height: 44rpx;
      color: #000;
    }

    .jobDetail-title-salary {
      font-size: 28rpx;
      color: #ff5050;
    }
  }

  .jobDetail-subtitle {
    padding-top: 20rpx;

    .jobDetail-subtitle-text {
      line-height: 44rpx;
    }
  }

  .jobDetail-card {
    padding: 40rpx 0rpx 20rpx;

    .jobDetail-card-img {
      width: 104rpx;
      height: 104rpx;
      border-radius: 30rpx;
      box-shadow: 0px 20rpx 42rpx 0px rgba(0, 0, 0, 0.3);
    }

    .border-twinkle {
      position: relative;

      &::before {
        position: absolute;
        top: -2rpx;
        right: -2rpx;
        bottom: -2rpx;
        left: -2rpx;
        z-index: -1;
        content: '';
        background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
        border-radius: 30rpx;
        animation: twinkle 2s infinite;
      }
    }

    .bg_left_icon_box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 104rpx;
      height: 108rpx;
      border: 2rpx solid #0ea500;
      border-radius: 30rpx;
    }

    @keyframes twinkle {
      0%,
      100% {
        opacity: 1;
      }
      50% {
        opacity: 0.5;
      }
    }

    @keyframes dotPulse {
      0%,
      80%,
      100% {
        opacity: 0.3;
        transform: scale(0.8);
      }
      40% {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    .jobDetail-card-bg {
      z-index: -3;
      display: flex;
      flex-direction: column;
      align-items: left;
      justify-content: center;
      width: 100%;
      height: 140rpx;
      padding: 20rpx 20rpx 20rpx 70rpx;
      margin: 2rpx 0 0 -50rpx;
      background-color: #ffffff;
      border-radius: 36rpx;
      box-shadow: 0 10rpx 40rpx 0 rgba(0, 0, 0, 0.15);
    }

    .jobDetail-card-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .jobDetail-card-content {
        flex: 1;
      }

      .jobDetail-chat-icon {
        position: relative;
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 112rpx;
        height: 74rpx;
        text-align: center;
        background: #fff4f4;
        border-radius: 20rpx 20rpx 20rpx 20rpx;

        .chat-icon {
          width: 48rpx;
          height: 48rpx;
          opacity: 0.7;

          &_icon {
            width: 40rpx;
            height: 40rpx;
          }

          &_icon-1 {
            width: 40rpx;
            height: 40rpx;
          }
        }

        .chat-dots-animation {
          position: absolute;
          bottom: 8rpx;
          left: 50%;
          display: flex;
          gap: 3rpx;
          align-items: center;
          transform: translateX(-50%);

          .dot {
            width: 8rpx;
            height: 8rpx;
            background-color: #ff6b6b;
            border-radius: 50%;
            animation: dotPulse 1.5s infinite ease-in-out;
          }

          .dot1 {
            animation-delay: 0s;
          }

          .dot2 {
            animation-delay: 0.3s;
          }

          .dot3 {
            animation-delay: 0.6s;
          }
        }
      }
    }

    .jobDetail-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      margin-left: -40rpx;
      background-color: #aeffb1;
      border-radius: 20rpx;

      .arrow-right-1 {
        line-height: 60rpx;
      }
    }
  }

  .jobDetail-tag-list {
    flex-wrap: wrap;
    padding: 30rpx 0rpx 0rpx;

    .jobDetail-tag {
      // display: flex;
      // flex-wrap: wrap;
      // align-items: center;
      padding: 5rpx 30rpx;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      font-size: 26rpx;
      color: #333333;
      text-align: center;
      background-color: #ebebeb;
      border-radius: 16rpx;
    }
  }

  .jobDetail-content {
    .jobDetail-content-name {
      font-size: 28rpx;
      line-height: 50rpx;
      color: #333333;
    }
  }

  .jobDetail-content-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #000000;
  }

  .jobDetail-conpany {
    padding: 20rpx;
    margin: 40rpx 0;
    background: #ffffff;
    border-radius: 36rpx;
    box-shadow: 0 10rpx 40rpx 0 rgba(0, 0, 0, 0.15);

    .jobDetail-conpany-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 34rpx;
    }

    .jobDetail-conpany-text {
      margin-left: 20rpx;

      .jobDetail-conpany-title {
        padding-bottom: 10rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
      }

      .jobDetail-conpany-subtitle {
        font-size: 24rpx;
        color: #000000;
      }
    }

    .jobDetail-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      background-color: #bce9ff;
      border-radius: 20rpx;

      .arrow-right-1 {
        line-height: 60rpx;
      }
    }
  }

  .jobDetail-address-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 40rpx;
    margin: 30rpx 0 0 0;
    background: #ffffff;
    border-radius: 36rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

    .jobDetail-address-content {
      flex: 1;

      .jobDetail-company-title {
        margin-bottom: 16rpx;
        font-size: 32rpx;
        font-weight: 600;
        line-height: 1.2;
        color: #000000;
      }

      .jobDetail-address-info {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .jobDetail-address-text {
          margin-left: 8rpx;
          font-size: 28rpx;
          font-weight: 500;
          line-height: 1.3;
          color: #333333;
        }
      }

      .jobDetail-distance-info {
        display: flex;
        align-items: center;

        .jobDetail-distance-text {
          margin-left: 8rpx;
          font-size: 24rpx;
          color: #888888;
        }
      }
    }

    .jobDetail-address-icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      margin-left: 20rpx;
      background-color: #f5f5f5;
      border-radius: 20rpx;

      .address-icon {
        width: 48rpx;
        height: 48rpx;
      }
    }
  }

  .notice-box {
    box-shadow: 0 10rpx 40rpx 0 rgba(0, 0, 0, 0.15);
  }
}
</style>
