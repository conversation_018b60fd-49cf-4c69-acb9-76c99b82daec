import { POST } from '../index'
import {
  hrCompanySizeInt,
  hrCompanyShortName,
  hrCompanyIndustry,
  hrCompanyTime,
  hrCompanyProfile,
  hrCompanywelfare,
  hrCompanyLogo,
  hrStyleAddStyle,
  hrStyleList,
  hrStyleDel,
  hrStylePosition,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** 更新公司规模接口 */
export const hrupdateSizeName = (data: hrCompanySizeInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateSizeName', data, config)

// 公司信息
export const queryCompanyInfo = (config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/queryCompanyInfo', {}, config)

// 更新公司简称接口
export const updateShortName = (data: hrCompanyShortName, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateShortName', data, config)
// 更新公司所属行业接口
export const updateIndustry = (data: hrCompanyIndustry, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateIndustry', data, config)

// 更新公司工作时间接口
export const updateWorkTime = (data: hrCompanyTime, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateWorkTime', data, config)

// 更新公司介绍接口
export const updateProfile = (data: hrCompanyProfile, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateProfile', data, config)
// 更新公司福利待遇接口
export const updateWelfare = (data: hrCompanywelfare, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateWelfare', data, config)
// 更新公司LOGO接口
export const updateLogo = (data: hrCompanyLogo, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompany/updateLogo', data, config)

// HR-公司风采相新增
export const hrCompanyStyleAdd = (data: hrStyleAddStyle, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompanyStyle/add', data, config)
// HR-公司风采相册列表
export const hrCompanyStyleList = (data: hrStyleList, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompanyStyle/queryByCompanyId', data, config)
// HR-公司风采相册删除
export const hrCompanyStyleDel = (data: hrStyleDel, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrCompanyStyle/delete', data, config)
// hr岗位信息
export const hrupdateHrPosition = (data: hrStylePosition, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/hrUser/updateHrPosition', data, config)
