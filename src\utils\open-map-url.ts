import { wgs84ToBd09, gcj02ToBd09, wgs84ToGcj02 } from './geo'

/** 地图应用信息 */
interface MapApp {
  title: string
  name: string
  androidName: string
  iosName: string
}

/** 导航参数 */
interface NavigationParams {
  latitude: number
  longitude: number
  name: string
  address?: string
}

/** 地图URL参数 */
interface MapUrlParams extends NavigationParams {
  map: MapApp
}

/** 支持的地图应用列表 */
const MAP_APPS: MapApp[] = [
  {
    title: '高德地图',
    name: 'amap',
    androidName: 'com.autonavi.minimap',
    iosName: 'iosamap://',
  },
  {
    title: '百度地图',
    name: 'baidumap',
    androidName: 'com.baidu.BaiduMap',
    iosName: 'baidumap://',
  },
  {
    title: '腾讯地图',
    name: 'qqmap',
    androidName: 'com.tencent.map',
    iosName: 'qqmap://',
  },
]

/** 获取坐标转换结果 */
async function getCoordinates(latitude: number, longitude: number, osName: string) {
  const { getCurrentLocation } = useLocationPermission()
  const userLocationInfo = await getCurrentLocation()
  const userCoord =
    osName === 'ios'
      ? gcj02ToBd09(userLocationInfo.latitude, userLocationInfo.longitude)
      : wgs84ToGcj02(userLocationInfo.latitude, userLocationInfo.longitude)
  const wgs84ToGcj02Coord = wgs84ToGcj02(latitude, longitude)
  const wgs84ToBd09Coord = wgs84ToBd09(latitude, longitude)
  const iosWgs84ToBd09 = gcj02ToBd09(wgs84ToGcj02Coord.latitude, wgs84ToGcj02Coord.longitude)
  return {
    userCoord: { lat: userCoord.latitude, lng: userCoord.longitude },
    wgs84ToGcj02: { lat: wgs84ToGcj02Coord.latitude, lng: wgs84ToGcj02Coord.longitude },
    wgs84ToBd09Coord: { lat: wgs84ToBd09Coord.latitude, lng: wgs84ToBd09Coord.longitude },
    iosWgs84ToBd09: { lat: iosWgs84ToBd09.latitude, lng: iosWgs84ToBd09.longitude },
  }
}

/** 生成地图URL */
async function generateMapUrls(
  coordinates: Awaited<ReturnType<typeof getCoordinates>>,
  name: string,
  osName: string,
) {
  const { userCoord, wgs84ToGcj02, wgs84ToBd09Coord, iosWgs84ToBd09 } = coordinates

  const urlTemplates = {
    android: {
      amap: `amapuri://route/plan/?sid=&did=&dlat=${wgs84ToGcj02?.lat}&dlon=${wgs84ToGcj02?.lng}&dname=${name}&dev=0&t=0`,
      qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${wgs84ToGcj02?.lat},${wgs84ToGcj02?.lng}&referer=fuxishan_uni_client`,
      baidumap: `baidumap://map/direction?origin=${userCoord?.lat},${userCoord?.lng}&destination=name:${name}|latlng:${wgs84ToBd09Coord?.lat},${wgs84ToBd09Coord?.lng}&coord_type=wgs84&mode=driving&src=andr.baidu.openAPIdemo`,
    },
    ios: {
      amap: `iosamap://path?sourceApplication=fuxishan_uni_client&dlat=${wgs84ToGcj02?.lat}&dlon=${wgs84ToGcj02?.lng}&dname=${name}&dev=0&t=0`,
      qqmap: `qqmap://map/routeplan?type=drive&to=${name}&tocoord=${wgs84ToGcj02?.lat},${wgs84ToGcj02?.lng}&referer=fuxishan_uni_client`,
      baidumap: `baidumap://map/direction?origin=${userCoord?.lat},${userCoord?.lng}&destination=name:${name}|latlng:${iosWgs84ToBd09?.lat},${iosWgs84ToBd09?.lng}&mode=driving&src=ios.baidu.openAPIdemo`,
    },
  }

  return urlTemplates[osName as keyof typeof urlTemplates] || urlTemplates.android
}

const openMapURL = async ({ map, latitude, longitude, name }: MapUrlParams) => {
  try {
    const { osName } = await uni.getSystemInfo()
    const coordinates = await getCoordinates(latitude, longitude, osName)
    const urlTemplates = await generateMapUrls(coordinates, name, osName)

    const mapUrl = urlTemplates[map.name as keyof typeof urlTemplates]
    if (!mapUrl) {
      throw new Error(`不支持的地图应用: ${map.name}`)
    }

    const encodedUrl = encodeURI(mapUrl)

    plus.runtime.openURL(
      encodedUrl,
      (err) => {
        console.error('打开地图失败:', err)
        uni.showToast({
          title: '打开地图失败',
          icon: 'none',
        })
      },
      map.androidName || '',
    )
  } catch (error) {
    console.error('地图导航错误:', error)
    uni.showToast({
      title: '地图导航失败',
      icon: 'none',
    })
  }
}

/** 获取已安装的地图应用 */
function getInstalledMaps(osName: string): MapApp[] {
  return MAP_APPS.filter((mapApp) => {
    const identifier = osName === 'ios' ? mapApp.iosName : mapApp.androidName
    console.log(`检查 ${mapApp.title} 是否安装:`, identifier)
    const isInstalled =
      osName === 'ios'
        ? plus.runtime.isApplicationExist({ action: identifier })
        : plus.runtime.isApplicationExist({ pname: identifier })
    console.log(`${mapApp.title} 安装状态:`, isInstalled)
    return isInstalled
  })
}

/** 跳转地图导航 */
export const handleNavigation = async ({ latitude, longitude, name }: NavigationParams) => {
  try {
    const { osName } = await uni.getSystemInfo()
    console.log('当前操作系统:', osName)
    const installedMaps = getInstalledMaps(osName)
    console.log('已安装的地图应用:', installedMaps)
    if (installedMaps.length === 0) {
      uni.showToast({
        title: '请安装地图软件',
        icon: 'none',
      })
      return
    }

    // 如果只有一个地图应用，直接打开
    if (installedMaps.length === 1) {
      await openMapURL({
        map: installedMaps[0],
        latitude,
        longitude,
        name,
      })
      return
    }

    // 多个地图应用时显示选择菜单
    plus.nativeUI.actionSheet(
      {
        title: '请选择地图',
        cancel: '取消',
        buttons: installedMaps,
      },
      async (e) => {
        if (e.index >= 1) {
          const selectedMap = installedMaps[e.index - 1]
          await openMapURL({
            map: selectedMap,
            latitude,
            longitude,
            name,
          })
        }
      },
    )
  } catch (error) {
    console.error('导航处理错误:', error)
    uni.showToast({
      title: '导航功能异常',
      icon: 'none',
    })
  }
}
