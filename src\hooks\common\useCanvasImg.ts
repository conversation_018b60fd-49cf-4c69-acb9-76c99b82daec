import { formatDateDayFmt } from '@/utils/common'
export default function useCanvasImg() {
  // 辅助函数：画下划线并写变量内容
  function drawUnderlineText(
    ctx,
    text,
    x,
    y,
    width,
    fontSize = 38,
    textYOffset = -10,
    underlineYOffset = 8,
  ) {
    // 先画下划线
    ctx.beginPath()
    ctx.moveTo(x, y + underlineYOffset)
    ctx.lineTo(x + width, y + underlineYOffset)
    ctx.setStrokeStyle('#000')
    ctx.setLineWidth(2)
    ctx.stroke()
    // 再写字，字在下划线上方
    ctx.setFontSize(fontSize)
    ctx.setFillStyle('#000')
    ctx.setTextAlign('center')
    ctx.fillText(text || '', x + width / 2, y + textYOffset)
  }

  const downloadTemplate = (trueName, idCard, companyName, hrPosition) => {
    const width = 2480
    const height = 3508
    // #ifdef MP-WEIXIN || APP-PLUS
    const ctx = uni.createCanvasContext('a4Template')
    // 白底
    ctx.setFillStyle('#fff')
    ctx.fillRect(0, 0, width, height)

    // 标题居中
    ctx.setFillStyle('#000')
    ctx.setFontSize(100)
    ctx.setTextAlign('center')
    ctx.fillText('任 职 证 明', width / 2, 520)
    ctx.setFontSize(40)
    ctx.fillText('PROOF OF EMPLOYMENT', width / 2, 620)

    // 正文
    ctx.setFontSize(42)
    ctx.setTextAlign('left')
    // 正文左右边距相等
    const contentMargin = 500
    const marginTop = 900
    // 文字内容分段
    const text1 = '兹证明：'
    // 样式参数统一
    const underlineWidth1 = 300
    const underlineWidth2 = 600
    const gapAfterUnderline1 = 120
    const idLabel = '身份证号：'
    const underlineOffset2 = -100 // 身份证号下划线偏移
    const comma = '，'
    const text3One = '在我公司任职：'
    const underlineWidth3 = 300 // 新增下划线宽度
    const textAfterUnderline = '职位，' // 下划线后的文字

    // 计算每一段宽度
    const measure1 = ctx.measureText(text1).width
    const measureIdLabel = ctx.measureText(idLabel).width
    const measureComma = ctx.measureText(comma).width
    const measure3One = ctx.measureText(text3One).width
    const measureAfterUnderline = ctx.measureText(textAfterUnderline).width

    const totalWidth =
      measure1 +
      underlineWidth1 +
      gapAfterUnderline1 +
      measureIdLabel +
      underlineWidth2 +
      measureComma +
      measure3One +
      underlineWidth3 +
      measureAfterUnderline

    const startX = contentMargin + (width - 2 * contentMargin - totalWidth) / 2
    let x = startX

    ctx.fillText(text1, x, marginTop)
    x += measure1
    drawUnderlineText(ctx, trueName || '', x, marginTop, underlineWidth1, 42, -10, 12)
    x += underlineWidth1
    x += gapAfterUnderline1
    ctx.fillText(idLabel, x, marginTop)
    x += ctx.measureText(idLabel).width
    drawUnderlineText(
      ctx,
      idCard || '',
      x + underlineOffset2,
      marginTop,
      underlineWidth2,
      42,
      -10,
      12,
    )
    x += underlineWidth2
    ctx.fillText(comma, x, marginTop)
    x += measureComma
    ctx.fillText(text3One, x, marginTop)
    x += measure3One
    x += -150 // 增加间隔
    drawUnderlineText(ctx, hrPosition || '', x, marginTop, underlineWidth3, 42, -10, 12)
    x += underlineWidth3
    x += 80 // 增加间隔
    ctx.fillText(textAfterUnderline, x, marginTop)

    // 说明文字左对齐
    ctx.setTextAlign('left')
    ctx.setFontSize(36)
    ctx.fillText(
      '此证明仅限易直聘平台招聘使用，不作为任何形式的担保证明文件，复印无效。',
      startX,
      marginTop + 80,
    )

    // 第三行"特此证明。"左对齐
    ctx.setTextAlign('left')
    ctx.fillText('特此证明。', startX, marginTop + 200)

    // 落款
    ctx.setTextAlign('right')
    const marginX = contentMargin
    // 加盖公章下划线与文字分离
    drawUnderlineText(ctx, companyName || '', width - marginX - 600, height - 600, 700, 36, 0, 18)
    ctx.setFontSize(36)
    ctx.fillText('（加盖公章）', width - marginX - 400 + 400 + 200, height - 600)

    // 日期整体右对齐
    const dateObj = formatDateDayFmt()
    const dateX = width - marginX
    // 年
    drawUnderlineText(ctx, dateObj.year, dateX - 350, height - 480, 120, 36, -10, 12)
    ctx.fillText('年', dateX - 220, height - 470)
    // 月
    drawUnderlineText(ctx, dateObj.month, dateX - 180, height - 480, 80, 36, -10, 12)
    ctx.fillText('月', dateX - 80, height - 470)
    // 日
    drawUnderlineText(ctx, dateObj.day, dateX - 40, height - 480, 80, 36, -10, 12)
    ctx.fillText('日', dateX + 60, height - 470)

    ctx.draw(false, () => {
      uni.canvasToTempFilePath({
        canvasId: 'a4Template',
        width,
        height,
        destWidth: width,
        destHeight: height,
        success: function (res) {
          uni.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: function () {
              uni.showToast({ title: '已保存到本地相册', icon: 'none' })
            },
            fail: function () {
              uni.showToast({ title: '下载失败', icon: 'none' })
            },
          })
        },
        fail: function () {
          uni.showToast({ title: '生成图片失败', icon: 'none' })
        },
      })
    })
    // #endif

    // #ifdef H5
    // H5端使用Canvas API
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    const ctxH5 = canvas.getContext('2d')

    // 白底
    ctxH5.fillStyle = '#fff'
    ctxH5.fillRect(0, 0, width, height)

    // 标题居中
    ctxH5.fillStyle = '#000'
    ctxH5.font = '100px Arial'
    ctxH5.textAlign = 'center'
    ctxH5.fillText('任 职 证 明', width / 2, 520)
    ctxH5.font = '40px Arial'
    ctxH5.fillText('PROOF OF EMPLOYMENT', width / 2, 620)

    // 正文
    ctxH5.font = '42px Arial'
    ctxH5.textAlign = 'left'
    // 正文左右边距相等
    const contentMarginH5 = 500
    const marginTopH5 = 900
    // H5端同步修正
    const text1H5 = '兹证明：'
    const underlineWidth1H5 = 300
    const idLabelH5 = '身份证号：'
    const underlineWidth2H5 = 600
    const commaH5 = '，'
    const text3OneH5 = '在我公司任职：'
    const underlineWidth3H5 = 300 // 新增下划线宽度
    const textAfterUnderlineH5 = '职位，' // 下划线后的文字

    const measure1H5 = ctxH5.measureText(text1H5).width
    const measureIdLabelH5 = ctxH5.measureText(idLabelH5).width
    const measureCommaH5 = ctxH5.measureText(commaH5).width
    const measure3OneH5 = ctxH5.measureText(text3OneH5).width
    const measureAfterUnderlineH5 = ctxH5.measureText(textAfterUnderlineH5).width

    const totalWidthH5 =
      measure1H5 +
      underlineWidth1H5 +
      measureIdLabelH5 +
      underlineWidth2H5 +
      measureCommaH5 +
      measure3OneH5 +
      underlineWidth3H5 +
      measureAfterUnderlineH5

    const startXH5 = contentMarginH5 + (width - 2 * contentMarginH5 - totalWidthH5) / 2
    let xH5 = startXH5

    // H5端辅助函数：画下划线并写变量内容
    function drawUnderlineTextH5(
      ctx,
      text,
      x,
      y,
      width,
      fontSize = 38,
      textYOffset = -10,
      underlineYOffset = 8,
    ) {
      // 先画下划线
      ctx.beginPath()
      ctx.moveTo(x, y + underlineYOffset)
      ctx.lineTo(x + width, y + underlineYOffset)
      ctx.strokeStyle = '#000'
      ctx.lineWidth = 2
      ctx.stroke()
      // 再写字，字在下划线上方
      ctx.font = `${fontSize}px Arial`
      ctx.fillStyle = '#000'
      ctx.textAlign = 'center'
      ctx.fillText(text || '', x + width / 2, y + textYOffset)
    }
    // 下划线与文字的间距
    const gapAfterUnderline1H5 = 120
    ctxH5.fillText(text1H5, xH5, marginTopH5)
    xH5 += measure1H5
    // 兹证明下划线，调整下划线的起始位置，让下划线整体右移
    drawUnderlineTextH5(
      ctxH5,
      trueName || '',
      xH5 + 10,
      marginTopH5,
      underlineWidth1H5,
      42,
      -10,
      12,
    )
    xH5 += underlineWidth1H5
    xH5 += gapAfterUnderline1H5 // 增加间隔
    ctxH5.fillText(idLabelH5, xH5, marginTopH5)
    xH5 += measureIdLabelH5
    drawUnderlineTextH5(ctxH5, idCard || '', xH5 - 100, marginTopH5, underlineWidth2H5, 42, -10, 12)
    xH5 += underlineWidth2H5
    ctxH5.fillText(commaH5, xH5, marginTopH5)
    xH5 += measureCommaH5
    ctxH5.fillText(text3OneH5, xH5, marginTopH5)
    xH5 += measure3OneH5
    xH5 += -150 // 增加间隔
    drawUnderlineTextH5(ctxH5, hrPosition || '', xH5, marginTopH5, underlineWidth3H5, 42, -10, 12)
    xH5 += underlineWidth3H5
    xH5 += 80 // 增加间隔
    ctxH5.fillText(textAfterUnderlineH5, xH5, marginTopH5)

    // 说明文字左对齐
    ctxH5.textAlign = 'left'
    ctxH5.font = '36px Arial'
    ctxH5.fillText(
      '此证明仅限易直聘平台招聘使用，不作为任何形式的担保证明文件，复印无效。',
      startXH5,
      marginTopH5 + 80,
    )

    // 第三行"特此证明。"左对齐
    ctxH5.fillText('特此证明。', startXH5, marginTopH5 + 200)

    // 落款
    ctxH5.textAlign = 'right'
    const marginXH5 = contentMarginH5
    // 加盖公章下划线与文字分离
    drawUnderlineTextH5(
      ctxH5,
      companyName || '',
      width - marginXH5 - 600,
      height - 600,
      700,
      36,
      0,
      18,
    )
    ctxH5.font = '36px Arial'
    ctxH5.fillText('（加盖公章）', width - marginXH5 - 400 + 400 + 200, height - 600)

    // 日期整体右对齐
    const dateObjH5 = formatDateDayFmt()
    const dateXH5 = width - marginXH5
    // 年
    drawUnderlineTextH5(ctxH5, dateObjH5.year, dateXH5 - 350, height - 480, 120, 36, -10, 12)
    ctxH5.fillText('年', dateXH5 - 220, height - 470)
    // 月
    drawUnderlineTextH5(ctxH5, dateObjH5.month, dateXH5 - 180, height - 480, 80, 36, -10, 12)
    ctxH5.fillText('月', dateXH5 - 80, height - 470)
    // 日
    drawUnderlineTextH5(ctxH5, dateObjH5.day, dateXH5 - 40, height - 480, 80, 36, -10, 12)
    ctxH5.fillText('日', dateXH5 + 60, height - 470)

    // H5端下载图片
    canvas.toBlob((blob) => {
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = '任职证明.png'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      uni.showToast({ title: '已下载', icon: 'success' })
    }, 'image/png')
    // #endif
  }
  return {
    downloadTemplate,
  }
}
