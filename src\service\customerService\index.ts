import { POST } from '../index'
import { HttpRequestConfig } from 'luch-request'

/** 问题分类 */
export const querySimpleData = (config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/artificialServiceMessage/querySimpleData', {}, config)

/** 猜您想问 */
export const queryAll = (config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/artificialServiceMessage/queryAll', {}, config)

/** 简要回答 */
export const queryType = (data: any, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/artificialServiceMessage/queryType ', data, config)

/** B端问题分类 */
export const querySimpleDataB = (config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/artificialServiceMessage/querySimpleDataBussiness', {}, config)

/** B端猜您想问 */
export const queryTypeB = (data: any, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/artificialServiceMessage/queryType', data, config)

// 获取用户免登Token(返回data是token)接口
export const generateUserAccessToken = (config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/aliKefu/generateUserAccessToken', {}, config)
