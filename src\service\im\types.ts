export interface imDomainInfoInt {
  /** appKey */
  appKey: string
  /** rest国内1区 */
  basePath: string
  /** rest国内2区  */
  basePathSta: string
  /** websocket国内1区 */
  baseWsPath: string[]
  /** websocket国内2区 */
  baseWsPathSta: string[]
}
export interface imCreateAccountDataInt extends Pick<Api.User.IUserInfo, 'type'> {}
export interface imCreateAccountInt {
  /** 用户名 */
  username: string
  /** 用户授权token */
  accessToken: string
}

export interface imModifyCustomMessageDataInt {
  /** 自定义扩展信息 */
  customExts: {
    [key: string]: any
  }
  /** 消息id */
  msgId: string
  /** 修改人username */
  username?: string
}

export interface imUnInterestUserNameListInt {
  /** HR姓名 */
  hrName?: string
  /** HR用户ID */
  hrUserId?: number
  /** HR用户名 */
  hrUsername?: string
  /** 记录ID */
  id?: number
  /** 公司名称 */
  name?: string
  /** 职位信息ID */
  positionInfoId?: number
  /** 职位名称 */
  positionName?: string
  /** 用户ID */
  userId?: number
}
