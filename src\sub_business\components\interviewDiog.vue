<template>
  <wd-popup
    :model-value="show"
    position="bottom"
    custom-style="height: 500rpx;"
    safe-area-inset-bottom
    :close-on-click-modal="true"
  >
    <view class="flex items-center justify-between p-t-20rpx p-l-40rpx p-r-40rpx">
      <view class="text-28rpx c-#666 p-20rpx" @click="cancek">取消</view>
      <view class="text-28rpx c-#333 p-20rpx">取消面试</view>
      <view class="text-28rpx c-#4d8fff p-20rpx" @click="confirmCancel">确认</view>
    </view>
    <view
      class="shadow-[0px_4px_10px_0px_rgba(0,0,0,0.3)] rounded-[16rpx] py-10rpx bg-#fff m-t-20rpx m-l-40rpx m-r-40rpx"
    >
      <wd-textarea
        custom-class="custom-class"
        v-model="remark"
        custom-textarea-container-class="custom-textarea-container-class"
        placeholder="请输入取消原因"
      />
    </view>
  </wd-popup>
  <wd-message-box />
</template>
<script setup lang="ts">
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'
import { cancelById, userInterviewRecord } from '@/interPost/resume'
const message = useMessage()

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  rejectType: {
    type: String,
    default: '',
  },
  item: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:show'])
const remark = ref('')

const cancek = () => {
  emit('update:show', false)
}

// 确认取消hr
const confirmCancel = () => {
  message
    .confirm({
      title: '确定取消面试吗？',
    })
    .then(() => {
      console.log('props.rejectType==========', props.rejectType)
      if (props.rejectType === 'cancelToBusiness') {
        cancelById({
          id: props.item.id,
          remark: remark.value,
          status: '3',
        })
          .then((res: any) => {
            console.log('cancelById API 响应：', res)
            if (res.code === 0) {
              console.log('cancelById 成功，开始清理和刷新')
              remark.value = ''
              cancek()
              uni.$emit('refreshInterview')
              uni.$emit('refreshInterviewList')
              uni.$emit('refreshInterviewListToPerson')
            } else {
              console.log('cancelById 失败，错误码：', res.code, '错误信息：', res.msg)
            }
          })
          .catch((error: any) => {
            console.log('cancelById API 请求失败：', error)
          })
      } else if (props.rejectType === 'cancelToPerson') {
        console.log('执行 cancelToPerson 分支')
        console.log('调用 userInterviewRecord API，参数：', {
          id: props.item.id,
          status: '4',
        })

        userInterviewRecord({
          id: props.item.id,
          status: '4',
        })
          .then((res: any) => {
            console.log('userInterviewRecord API 响应：', res)
            remark.value = ''
            cancek()
            uni.$emit('refreshInterview')
            uni.$emit('refreshInterviewList')
            uni.$emit('refreshInterviewListToPerson')
          })
          .catch((error: any) => {
            console.log('userInterviewRecord API 请求失败：', error)
          })
      } else if (props.rejectType === 'refuseToPerson') {
        console.log('执行 refuseToPerson 分支')
        console.log('调用 userInterviewRecord API，参数：', {
          id: props.item.id,
          status: '2',
        })

        userInterviewRecord({
          id: props.item.id,
          status: '2',
        })
          .then((res: any) => {
            console.log('userInterviewRecord API 响应：', res)
            remark.value = ''
            cancek()
            uni.$emit('refreshInterview')
            uni.$emit('refreshInterviewList')
            uni.$emit('refreshInterviewListToPerson')
          })
          .catch((error: any) => {
            console.log('userInterviewRecord API 请求失败：', error)
          })
      } else {
        console.log('props.rejectType 不匹配任何条件：', props.rejectType)
        console.log(
          'props.rejectType 的字符编码：',
          props.rejectType.split('').map((c) => c.charCodeAt(0)),
        )
      }
    })
    .catch(() => {
      console.log('用户点击了取消按钮')
    })
}
</script>
