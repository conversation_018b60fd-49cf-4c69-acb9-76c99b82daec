import type { Message } from 'wot-design-uni/components/wd-message-box/types'
import type { Toast } from 'wot-design-uni/components/wd-toast/types'

interface MessageOptions {
  chatWotMessage: (selector?: string) => Message
  chatWotToast: (selector?: string) => Toast
}

class ChatMessageManager {
  private static instance: ChatMessageManager
  private _chatWotMessage?: (selector?: string) => Message
  private _chatWotToast?: (selector?: string) => Toast

  private constructor() {}

  static getInstance(): ChatMessageManager {
    if (!ChatMessageManager.instance) {
      ChatMessageManager.instance = new ChatMessageManager()
    }
    return ChatMessageManager.instance
  }

  init(options: MessageOptions) {
    this._chatWotMessage = options.chatWotMessage
    this._chatWotToast = options.chatWotToast
  }

  // 检查是否已初始化
  get isInitialized() {
    return !!this._chatWotMessage && !!this._chatWotToast
  }

  get chatWotMessage() {
    if (!this._chatWotMessage) {
      throw new Error('ChatMessage not initialized. Please call useChatMessage() first.')
    }
    return this._chatWotMessage
  }

  get chatWotToast() {
    if (!this._chatWotToast) {
      throw new Error('ChatToast not initialized. Please call useChatMessage() first.')
    }
    return this._chatWotToast
  }

  // 安全的全局访问方法
  safeGetChatMessage() {
    return this.isInitialized ? this._chatWotMessage!() : null
  }

  safeGetChatToast() {
    return this.isInitialized ? this._chatWotToast!() : null
  }
}

const manager = ChatMessageManager.getInstance()

export const useChatMessage = (options: MessageOptions) => {
  manager.init(options)
  return {
    chatWotMessage: manager.chatWotMessage,
    chatWotToast: manager.chatWotToast,
  }
}

// 导出全局访问方法 - 安全版本
export const getChatMessage = () => {
  const message = manager.safeGetChatMessage()
  if (!message) {
    console.warn('ChatMessage not initialized. Some features may not work properly.')
    // 返回一个空的代理对象，避免报错
    return new Proxy(
      {},
      {
        get() {
          console.warn('ChatMessage method called but not initialized.')
          return () => Promise.resolve()
        },
      },
    ) as Message
  }
  return message
}

export const getChatToast = () => {
  const toast = manager.safeGetChatToast()
  if (!toast) {
    console.warn('ChatToast not initialized. Some features may not work properly.')
    // 返回一个空的代理对象，避免报错
    return new Proxy(
      {},
      {
        get() {
          console.warn('ChatToast method called but not initialized.')
          return () => Promise.resolve()
        },
      },
    ) as Toast
  }
  return toast
}
