<template>
  <wd-popup v-model="showPicker" position="bottom" round :close-on-click-overlay="false">
    <view class="picker-header">
      <text class="picker-cancel" @click="cancel">取消</text>
      <text class="picker-title">选择日期范围</text>
      <text class="picker-confirm" @click="confirm">确定</text>
    </view>
    <view class="picker-container">
      <!-- 自定义选中区域指示器 -->
      <view class="picker-indicator"></view>
      <picker-view
        class="picker-view"
        :indicator-style="indicatorStyle"
        :mask-style="maskStyle"
        :value="pickVal"
        @change="handlerChange"
      >
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in range.fyears" :key="index">
            {{ item }}年
          </view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in range.fmonths" :key="index">
            {{ item }}月
          </view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item picker-separator">至</view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in range.tyears" :key="index">
            {{ item }}年
          </view>
        </picker-view-column>
        <picker-view-column>
          <view class="picker-item" v-for="(item, index) in range.tmonths" :key="index">
            {{ item }}月
          </view>
        </picker-view-column>
      </picker-view>
    </view>
  </wd-popup>
</template>

<script setup>
import { ref, watch, computed } from 'vue'

const props = defineProps({
  itemHeight: {
    type: String,
    default: '100rpx',
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
  current: {
    type: Boolean,
    default: false,
  },
  startYear: {
    type: [String, Number],
    default: 1970,
  },
  endYear: {
    type: [String, Number],
    default: new Date().getFullYear() + 10,
  },
})

const emit = defineEmits(['update:modelValue', 'change', 'confirm', 'cancel'])

const showPicker = ref(false)
const pickVal = ref([])
const range = ref({})
const checkObj = ref({})
const tempValue = ref([])

const indicatorStyle = computed(() => {
  return `
    height: ${props.itemHeight};
    line-height: ${props.itemHeight};
    border: none;
    background-color: transparent;
    box-sizing: border-box;
  `
})

const maskStyle = `
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.2));
`

const formatNum = (n) => {
  return Number(n) < 10 ? '0' + Number(n) : Number(n) + ''
}

const checkValue = (value) => {
  const strReg = /^\d{4}-\d{2}$/
  const example = '2020-09'
  if (!strReg.test(value[0]) || !strReg.test(value[1])) {
    console.log(new Error('请传入与mode匹配的value值，例[' + example + ',' + example + ']'))
  }
  return strReg.test(value[0]) && strReg.test(value[1])
}

const getDval = () => {
  const value = props.modelValue
  const aDate = new Date()
  const currentYear = aDate.getFullYear()

  // Set default to last year's July to current year's September
  const fyear = formatNum(currentYear - 1)
  const fmonth = formatNum(7) // July

  const tyear = formatNum(currentYear)
  const tmonth = formatNum(9) // September

  if (value && value.length > 0 && checkValue(value)) {
    return [...value[0].split('-'), '-', ...value[1].split('-')]
  } else {
    return [fyear, fmonth, '-', tyear, tmonth]
  }
}

const getData = (dVal) => {
  const start = props.startYear * 1
  const end = props.endYear * 1
  const value = dVal
  const fyears = []
  const fmonths = []
  const tyears = []
  const tmonths = []

  for (let s = start; s <= end; s++) {
    fyears.push(formatNum(s))
  }

  for (let m = 1; m <= 12; m++) {
    fmonths.push(formatNum(m))
    tmonths.push(formatNum(m))
  }

  // Initialize tyears based on selected fyear
  const selectedFyear = value[0] ? value[0] * 1 : new Date().getFullYear() - 1
  for (let s = selectedFyear; s <= end; s++) {
    tyears.push(formatNum(s))
  }

  const pickVal = [
    fyears.indexOf(value[0]) === -1
      ? fyears.indexOf(formatNum(new Date().getFullYear() - 1))
      : fyears.indexOf(value[0]),
    fmonths.indexOf(value[1]) === -1 ? 6 : fmonths.indexOf(value[1]), // July is index 6 (0-based)
    0,
    tyears.indexOf(value[3]) === -1
      ? tyears.indexOf(formatNum(new Date().getFullYear()))
      : tyears.indexOf(value[3]),
    tmonths.indexOf(value[4]) === -1 ? 8 : tmonths.indexOf(value[4]), // September is index 8 (0-based)
  ]

  return {
    fyears,
    fmonths,
    tyears,
    tmonths,
    pickVal,
  }
}

const initData = () => {
  const dVal = getDval()
  const dateData = getData(dVal)

  range.value = {
    fyears: dateData.fyears,
    fmonths: dateData.fmonths,
    tyears: dateData.tyears,
    tmonths: dateData.tmonths,
  }

  pickVal.value = dateData.pickVal

  const fyear = range.value.fyears[pickVal.value[0]]
  const fmonth = range.value.fmonths[pickVal.value[1]]
  const tyear = range.value.tyears[pickVal.value[3]]
  const tmonth = range.value.tmonths[pickVal.value[4]]

  checkObj.value = {
    fyear,
    fmonth,
    tyear,
    tmonth,
  }

  const result = `${fyear}-${fmonth}至${tyear}-${tmonth}`
  tempValue.value = result.split('至')

  emit('change', {
    result,
    value: tempValue.value,
    obj: checkObj.value,
  })
}

const handlerChange = (e) => {
  const arr = [...e.detail.value]
  const fyear = range.value.fyears[arr[0]] || range.value.fyears[0]
  const fmonth = range.value.fmonths[arr[1]] || range.value.fmonths[0]

  // Update tyears when fyear changes
  if (fyear !== checkObj.value.fyear) {
    const tyears = []
    for (let s = fyear * 1; s <= props.endYear; s++) {
      tyears.push(formatNum(s))
    }
    range.value.tyears = tyears

    // Reset tyear and tmonth if fyear is after current tyear
    const currentTyear = range.value.tyears[arr[3]] || range.value.tyears[0]
    if (fyear * 1 > currentTyear * 1) {
      arr[3] = 0
      arr[4] = 0
    }
  }

  const tyear = range.value.tyears[arr[3]] || range.value.tyears[0]
  const tmonth = range.value.tmonths[arr[4]] || range.value.tmonths[0]

  const result = `${fyear}-${fmonth}至${tyear}-${tmonth}`
  const obj = {
    fyear,
    fmonth,
    tyear,
    tmonth,
  }

  checkObj.value = obj
  pickVal.value = arr
  tempValue.value = result.split('至')

  emit('change', {
    result,
    value: tempValue.value,
    obj,
  })
}

const open = () => {
  showPicker.value = true
}

const close = () => {
  showPicker.value = false
}

const confirm = () => {
  emit('update:modelValue', tempValue.value)
  emit('confirm', {
    value: tempValue.value,
    obj: checkObj.value,
  })
  close()
}

const cancel = () => {
  emit('cancel')
  close()
}

defineExpose({
  open,
  close,
})

watch(
  () => props.modelValue,
  () => {
    initData()
  },
  { immediate: true },
)
</script>

<style lang="scss" scoped>
.picker-container {
  position: relative;
  width: 100%;
  height: 500rpx;
  overflow: hidden;
  background-color: #fff;
}

.picker-indicator {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  z-index: 1;
  height: 100rpx;
  margin-top: -50rpx;
  pointer-events: none;
  background-color: rgba(0, 122, 255, 0.03);
  border-top: 2rpx solid #666;
  border-bottom: 2rpx solid #666;
}

.picker-view {
  width: 100%;
  height: 100%;
  /* 优化真机滚动性能 */
  -webkit-overflow-scrolling: touch;
}

.picker-item {
  /* 确保文字垂直居中 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 100rpx;
  color: #333;
  text-align: center;
  /* 确保文字在选中区域清晰可见 */
  text-shadow: 0 0 1rpx rgba(0, 0, 0, 0.1);
  /* 优化渲染性能 */
  transform: translateZ(0);
  backface-visibility: hidden;
  /* 防止文字模糊 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.picker-separator {
  font-weight: bold;
  color: #333;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
}

.picker-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.picker-cancel,
.picker-confirm {
  font-size: 30rpx;
  color: #1989fa;
}
</style>
