import { ref, onUnmounted, computed } from 'vue'
import useBoolean from './useBoolean'

const baseUrl = import.meta.env.VITE_SERVER_BASEURL
interface WebSocketMessage {
  type: 'ai_response' | 'ai_complete' | 'ai_error' | 'pong'
  data: any
  requestId?: string
}

interface UseWebSocketOptions {
  /** WebSocket服务器地址 */
  url: string
  /** 是否自动连接，默认为 true */
  autoConnect?: boolean
  /** 重连间隔时间（毫秒），默认为 3000ms */
  reconnectInterval?: number
  /** 最大重连尝试次数，默认为 5 次 */
  maxReconnectAttempts?: number
}

/**
 * WebSocket Hook - 用于AI流式响应
 * @param options WebSocket配置选项
 * @returns WebSocket相关状态和方法
 */
const useWebsocket = (options: UseWebSocketOptions) => {
  const { url, autoConnect = true, reconnectInterval = 3000, maxReconnectAttempts = 5 } = options
  const { getToken } = useUserInfo()
  let socketTask: UniApp.SocketTask | null = null
  /** 重连定时器 */
  let reconnectTimer: number | null = null
  /** 心跳定时器 */
  let heartbeatTimer: number | null = null
  /** 心跳间隔时间（毫秒） */
  const heartbeatInterval = 30000
  /** 重连尝试次数计数器 */
  const reconnectAttempts = ref(0)
  /** 当前AI响应内容 */
  const responseChunks = ref<string[]>([])
  /** 计算属性：完整的AI响应 */
  const currentResponse = computed(() => responseChunks.value.join(''))
  /** 是否正在接收AI响应 */
  const { bool: isReceiving, setTrue: setReceiving, setFalse: setNotReceiving } = useBoolean()
  /** WebSocket连接状态 */
  const { bool: isConnected, setTrue: setConnected, setFalse: setDisconnected } = useBoolean()
  /** WebSocket连接中状态 */
  const { bool: isConnecting, setTrue: setConnecting, setFalse: setNotConnecting } = useBoolean()

  /**
   * 连接WebSocket
   * 建立WebSocket连接并设置事件监听器
   */
  const connect = () => {
    if (isConnecting.value || isConnected.value) return
    setConnecting()
    const wsBaseUrl = baseUrl.replace('http://', 'ws://').replace('https://', 'wss://')
    const wsUrl = `${wsBaseUrl}${url}`
    socketTask = uni.connectSocket({
      url: wsUrl,
      header: {
        token: getToken(),
      },
      success: () => {
        console.log('WebSocket connecting...')
      },
      fail: (error) => {
        console.error('WebSocket connection failed:', error)
        setNotConnecting()
      },
    })

    socketTask.onOpen(() => {
      setConnected()
      setNotConnecting()
      reconnectAttempts.value = 0
      startHeartbeat()
      console.log('WebSocket connected')
    })

    socketTask.onMessage((event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data as string)
        switch (message.type) {
          case 'pong':
            break
          case 'ai_response':
            responseChunks.value.push(message.data)
            if (!isReceiving.value) setReceiving()
            break
          case 'ai_complete':
            setNotReceiving()
            break
          case 'ai_error':
            setNotReceiving()
            console.error('AI Error:', message.data)
            break
        }
      } catch (error) {
        console.error('WebSocket message parse error:', error)
      }
    })

    /**
     * WebSocket连接关闭事件处理
     * 处理连接断开后的状态更新和自动重连逻辑
     */
    socketTask.onClose((event) => {
      stopHeartbeat()
      setDisconnected()
      setNotConnecting()
      console.log('WebSocket disconnected', event.code, event.reason)
      if (reconnectTimer) {
        clearTimeout(reconnectTimer)
        reconnectTimer = null
      }
      /**
       * 重连策略：
       * - event.code !== 1000: 非正常关闭（1000为正常关闭码）
       * - reconnectAttempts.value < maxReconnectAttempts: 未达到最大重连次数
       */
      if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
        reconnectAttempts.value++
        console.log(`Reconnecting... attempt ${reconnectAttempts.value}/${maxReconnectAttempts}`)
        reconnectTimer = setTimeout(() => {
          reconnectTimer = null
          connect()
        }, reconnectInterval)
      } else if (reconnectAttempts.value >= maxReconnectAttempts) {
        console.error('Max reconnect attempts reached')
      }
    })

    socketTask.onError((error) => {
      console.error('WebSocket error:', error)
      setNotConnecting()
    })
  }

  /**
   * 开始心跳
   * 定时发送心跳包保持连接活跃
   */
  const startHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
    }
    heartbeatTimer = setInterval(() => {
      if (isConnected.value && socketTask) {
        socketTask.send({
          data: JSON.stringify({ type: 'ping' }),
        })
      }
    }, heartbeatInterval)
  }

  /** 停止心跳 */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  /**
   * 断开WebSocket连接
   * 主动关闭连接并清理资源
   */
  const disconnect = () => {
    stopHeartbeat()
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    reconnectAttempts.value = maxReconnectAttempts
    if (socketTask) {
      socketTask.close({
        code: 1000,
        reason: 'Manual disconnect',
      })
      socketTask = null
    }
  }

  /**
   * 发送AI请求
   * 通过WebSocket发送消息给AI服务
   * @param prompt 用户输入的提示词
   * @param requestId 可选的请求ID
   */
  const sendAIRequest = (prompt: string, requestId?: string) => {
    if (!isConnected.value) {
      console.error('WebSocket not connected')
      return false
    }
    responseChunks.value = []
    setReceiving()
    const message = {
      type: 'ai_request',
      data: { prompt },
      requestId: requestId || Date.now().toString(),
    }
    socketTask?.send({
      data: JSON.stringify(message),
    })
    return true
  }

  const clearResponse = () => {
    responseChunks.value = []
    setNotReceiving()
  }

  if (autoConnect) {
    connect()
  }

  onUnmounted(() => {
    disconnect()
  })

  return {
    isConnected,
    isConnecting,
    isReceiving,
    currentResponse,
    reconnectAttempts,
    connect,
    disconnect,
    sendAIRequest,
    clearResponse,
  }
}

export default useWebsocket
