<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '更多记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" v-model="pageData" @query="queryList" ref="pagingRef">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">更多记录</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="p-40rpx">
      <view
        class="flex p-b-20rpx items-center justify-between border-b-1rpx border-b-solid border-b-[#CECECE] m-b-40rpx"
        v-for="item in pageData"
        :key="item.id"
        @click="handleClick(item.id, item.propCategoryName)"
      >
        <view>
          <view class="text-28rpx c-#333 p-b-5rpx">
            {{ item.propCategoryName }} - {{ formatDuration(item.durationTime, item.durationType) }}
          </view>
          <view class="text-24rpx c-#888">{{ item.endTime }}</view>
        </view>

        <wd-icon name="chevron-right" size="22px" color="#555"></wd-icon>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import { payPropUseRecord } from '@/service/payPropUseRecord'

const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const params = reactive({
  entity: {
    propId: 1,
  },
  orderBy: {},
  page: 1,
  size: 10,
})
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await payPropUseRecord({ ...params, page: pageInfo.page, size: pageInfo.size })
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}

// 格式化持续时间显示
const formatDuration = (durationTime: number, durationType: number): string => {
  const timeUnits = ['小时', '天', '月', '年']
  const unit = timeUnits[durationType] || ''
  return `${durationTime} ${unit}`
}

function handleClick(id, name) {
  uni.navigateTo({
    url: `/sub_business/pages/prop/redetail?id=${id}&name=${name}`,
  })
}
function handleClickLeft() {
  uni.navigateBack()
}
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
onLoad(async (options) => {
  await uni.$onLaunched
  params.entity.propId = options.propId
  await pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
:deep(.record-collapse) {
  margin: 40rpx;
  background: #ffffff;
  border: 0px solid #ededed;
  border-radius: 20px;
  box-shadow: 0px 4px 13.5px 0px rgba(0, 0, 0, 0.15);
}
:deep(.wd-collapse-item__body) {
  padding: 0rpx 30rpx 30rpx 30rpx;
}
:deep(.wd-collapse-item__header.is-expanded[data-v-27ee4d1d]::after) {
  background: transparent !important;
}
:deep(.wd-collapse-item__header) {
  padding: 10rpx 10rpx 0rpx !important;
}
</style>
