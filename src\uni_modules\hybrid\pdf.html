<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <title>详情</title>
    <link rel="stylesheet" href="html/pdf/style.css" />
    <link rel="stylesheet" href="html/pdf/pdfh5.css" />
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
      }
    </style>
  </head>

  <body>
    <div id="pdf-view"></div>
    <script src="html/pdf/pdf.js"></script>
    <script src="html/pdf/pdf.worker.js"></script>
    <!-- <script src="html/pdf/jquery-1.11.3.min.js" type="text/javascript" charset="utf-8"></script> -->
    <script src="html/pdf/pdfh5.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript">
      var pdfh5
      document.addEventListener('DOMContentLoaded', function () {
        document.getElementById('pdf-view').style.display = 'block'
        var url = getQuery('url')
        pdfh5 = new Pdfh5('#pdf-view', {
          pdfurl: url,
          // background:{
          // 	image:"url('pdfh5.png')",
          // 	repeat:"no-repeat",
          // 	position:"left top",
          // 	size:"40px 40px"
          // },
          URIenable: true, //开启浏览器地址栏file参数获取
        })

        //pdf准备开始渲染，此时可以拿到pdf总页数
        pdfh5.on('ready', function () {
          console.log('pdf准备开始渲染，总页数：' + this.totalNum)
        })
        //监听pdf渲染成功
        pdfh5.on('success', function (time) {
          time = time / 1000
          console.log('pdf渲染完成，总耗时' + time + '秒')
        })
      })
      function getQuery(name) {
        // 正则：[找寻'&' + 'url参数名字' = '值' + '&']（'&'可以不存在）
        let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
        let r = window.location.search.substr(1).match(reg)
        if (r != null) {
          // 对参数值进行解码
          return decodeURIComponent(r[2])
        }
        return null
      }
    </script>
  </body>
</html>
