<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar title="身份切换"></CustomNavBar>
    </template>
    <view class="containner">
      <image
        class="containner-img"
        :src="userRoleIsBusiness ? changeIdentCompany : changeIdentCompany"
        mode="scaleToFill"
      />
      <view class="m-t-40rpx font-bold text-32rpx">
        您当前的身份是{{ userRoleIsBusiness ? '伯乐' : '“黑马”' }}
      </view>
    </view>
    <view class="center">
      <wd-config-provider :themeVars="themeVars">
        <wd-button
          :loading="changeIdentLoading"
          @click="changeIdentChange"
          :round="false"
          size="large"
          custom-class="!rounded-28rpx w-590rpx"
        >
          <text class="c-#333333 text-28rpx font500" v-if="changeIdentLoading">切换中...</text>
          <text v-else class="c-#333333 text-28rpx font500">
            切换为{{ userRoleIsBusiness ? '“黑马”' : '“伯乐”' }}身份
          </text>
        </wd-button>
      </wd-config-provider>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import changeIdentCompany from '@/setting/img/<EMAIL>'
import { useResumeStore } from '@/store'
const resumeStore = useResumeStore()
const { userRoleIsBusiness } = useUserInfo()
const { changeIdent, changeIdentLoading } = useChangeIdent()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const changeIdentChange = () => {
  resumeStore.setfillterObg({})
  changeIdent()
}
const themeVars: ConfigProviderThemeVars = {
  buttonPrimaryBgColor: 'linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%)',
}
</script>

<style lang="scss" scoped>
.containner {
  padding: 40rpx;
  text-align: center;
  .containner-img {
    width: 320rpx;
    height: 320rpx;
    margin: auto;
    margin-top: 200rpx;
  }
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
