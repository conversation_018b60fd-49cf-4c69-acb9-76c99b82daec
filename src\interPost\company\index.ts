import { POST } from '@/service'
// 营业执照OCR识别(传附件的id)接口
export const identifyLicenseOcr = (data: any) => {
  return POST('/easyzhipin-api/ocrRecord/identifyLicenseOcr', data)
}
// 企业信息提交接口
export const applyOne = (data: any) => {
  return POST('/easyzhipin-api/companyApply/applyOne', data)
}
// 身份证二要素验证接口
export const idCardVerify = (data: any) => {
  return POST('/easyzhipin-api/companyApply/idCardVerify', data)
}
// 提交自己和公司的关系(上传附件id和Hr id)

export const commitJobRelation = (data: any) => {
  return POST('/easyzhipin-api/companyApply/commitJobRelation', data)
}
