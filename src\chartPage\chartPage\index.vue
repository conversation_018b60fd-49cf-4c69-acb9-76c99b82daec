<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img-h chart">
    <CustomNavBar>
      <template #left>
        <view class="flex-c">
          <!-- <u-icon name="arrow-left" @click="goBank" color="#000000" size="20"></u-icon> -->
          <wd-icon
            name="arrow-left"
            class="back-button"
            color="#000000"
            size="20"
            @click="goBank"
          />
          <!-- <u-badge bgColor="#888888" max="99" :value="value"></u-badge> -->
        </view>
      </template>
      <template #content>
        <view class="chart-title">徐女士</view>
        <view class="chart-subtitle">某某某传媒有限公司·人事经理1</view>
      </template>
      <template #right>
        <view class="flex-c">
          <wd-icon name="ellipsis" size="22px" color="#000000"></wd-icon>
        </view>
      </template>
    </CustomNavBar>
    <scroll-view scroll-y :style="{ height: `calc(100vh - ${customBar}px)` }">
      <view class="uScrollList">
        <scroll-view scroll-x class="uScrollList-scroll-view">
          <view class="uScrollList-item" v-for="(item, index) in list" :key="index">
            <view class="flex-c m-b-20rpx">
              <image class="uScrollList-item-img" src="/static/img/Group_1171275010.png"></image>
              <view class="">
                {{ item.name }}
              </view>
            </view>
            <view class="flex-c">
              <image class="uScrollList-item-img" src="/static/img/Group_1171275010.png"></image>
              <view class="">
                {{ item.subName }}
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="chart-content">
        <view class="chart-content-top flex-center m-b-40rpx">
          <view class="subTitle m-r-20rpx">05-10</view>
          <view class="subTitle">15:38</view>
        </view>
        <view class="chart-content-right">
          <view class="chart-content-right-text">
            Ok. Should I send it over email as well after
          </view>
          <image src="/static/img/1.jpg" class="chart-content-right-img"></image>
        </view>
        <view class="chart-content-left">
          <image src="/static/img/1.jpg" class="chart-content-right-img"></image>
          <view class="chart-content-right-text">
            Ok. Should I send it over email as well after filling the details.
          </view>
        </view>
        <view class="chart-content-left">
          <image src="/static/img/1.jpg" class="chart-content-right-img"></image>
          <view class="chart-content-right-text">Ok. Should I</view>
        </view>
      </view>
      <view class="fiex-end flex-between">
        <view class="flex-c">
          <view class="chart-content-hf">常</view>
          <wd-input placeholder="回复消息" no-border v-model="value1"></wd-input>
        </view>
        <view class="flex-c">
          <wd-icon name="add-circle" size="22px" color="#000000" class="m-r-20rpx"></wd-icon>
          <wd-icon name="add-circle" size="22px" color="#000000"></wd-icon>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { getCustomBar } from '@/utils/storage'
import { ref } from 'vue'
const customBar = ref(getCustomBar())
const value = ref(14)
const value1 = ref('')
const list = ref([
  {
    name: '视频运营总监电商运营',
    subName: '双休 · 年假 · 弹性工作',
  },
  {
    name: '视频运营总监电商运营',
    subName: '双休 · 年假 · 弹性工作',
  },
  {
    name: '视频运营总监电商运营',
    subName: '双休 · 年假 · 弹性工作',
  },
])

const goBank = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.subTitle {
  font-size: 22rpx;
  color: #8888;
}
::v-deep .wd-input {
  width: 100%;
  background-color: transparent;
}
.chart-title {
  color: #000000;
}

.chart-subtitle {
  font-size: 22rpx;
  color: #888888;
}

.uScrollList {
  width: 100%;
  padding: 40rpx 0rpx 40rpx 40rpx;

  .uScrollList-scroll-view {
    white-space: nowrap;
  }

  .uScrollList-item {
    display: inline-block;
    padding: 20rpx 40rpx;
    margin-right: 40rpx;
    background-color: #ffffff;
    border-radius: 40rpx;

    .uScrollList-item-img {
      width: 32rpx;
      height: 32rpx;
      padding-right: 10rpx;
    }
  }
}

.chart-content {
  position: relative;
  height: 1500rpx;
  padding: 40rpx 40rpx 40rpx;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0rpx 0rpx;

  .chart-content-right {
    display: flex;
    align-items: flex-end;
    justify-content: right;
    padding-bottom: 60rpx;

    .chart-content-right-img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }

    .chart-content-right-text {
      padding: 20rpx 20rpx;
      margin-right: 20rpx;
      color: #fff;
      background-color: #4399ff;
      border-radius: 20rpx 20rpx 0rpx 20rpx;
    }
  }

  .chart-content-left {
    display: flex;
    align-items: flex-end;
    justify-content: left;
    padding-bottom: 60rpx;

    .chart-content-right-img {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }

    .chart-content-right-text {
      max-width: 500rpx;
      padding: 20rpx 20rpx;
      margin-left: 20rpx;
      color: #333;
      background-color: #efefef;
      border-radius: 20rpx 20rpx 20rpx 0rpx;
    }
  }
}

.fiex-end {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  left: 40rpx;
  padding: 20rpx 40rpx;
  background-color: #f0f0f0;
  border-radius: 30rpx;

  .chart-content-hf {
    width: 80rpx;
    height: 80rpx;
    margin-right: 20rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #d2e2ff;
    border-radius: 20rpx;
  }
}
</style>
