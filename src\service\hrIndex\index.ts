import { POSTPaging } from '../index'
import { hrIndexResumeUserListDataInt, hrIndexResumeUserListInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 简历用户列表查询接口 */
export const hrIndexResumeUserList = (
  data: Api.Request.IResPagingDataParamsInt<hrIndexResumeUserListDataInt>,
  config?: HttpRequestConfig,
) => POSTPaging<hrIndexResumeUserListInt>('/easyzhipin-api/hrIndex/resumeUserList', data, config)
