import { dictionaryQueryById } from '@/service/dictionary'
import type { dictionaryQueryByIdInt } from '@/service/dictionary/types'

// 请求状态枚举
enum RequestStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error',
}

// 配置选项
interface DictConfig {
  /** 缓存时间（毫秒），默认30分钟 */
  cacheTimeout?: number
  /** 是否启用本地存储 */
  enableStorage?: boolean
  /** 存储key前缀 */
  storagePrefix?: string
  /** 是否启用调试模式 */
  debug?: boolean
  /** 请求超时时间 */
  timeout?: number
  /** 重试次数 */
  retryCount?: number
  /** 重试延迟（毫秒） */
  retryDelay?: number
  /** 最大缓存数量 */
  maxCacheSize?: number
}

// 缓存项
interface CacheItem<T = dictionaryQueryByIdInt> {
  data: T
  timestamp: number
  expireTime: number
  accessCount: number
  lastAccess: number
}

// 请求状态追踪
interface RequestState {
  status: RequestStatus
  error?: string
  retryCount: number
}

// 性能监控数据
interface PerformanceMetrics {
  totalRequests: number
  cacheHits: number
  cacheMisses: number
  averageResponseTime: number
  errorCount: number
}
type DictTextType = string | number | AnyObject
export interface DictOption {
  value: string | number
  text: DictTextType
}
// 字典管理器类
class DictionaryManager {
  private static instance: DictionaryManager | null = null
  private cache = new Map<string | number, CacheItem>()
  private config: Required<DictConfig>
  private loadingPromises = new Map<string | number, Promise<dictionaryQueryByIdInt>>()
  private subscribers = new Set<() => void>()
  private requestStates = new Map<string | number, RequestState>()
  private performanceMetrics: PerformanceMetrics = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0,
    averageResponseTime: 0,
    errorCount: 0,
  }

  private cleanupTimer: number | null = null

  private constructor(config: DictConfig = {}) {
    this.config = {
      cacheTimeout: 30 * 60 * 1000,
      enableStorage: true,
      storagePrefix: 'uni_dict_',
      debug: false,
      timeout: 10000,
      retryCount: 3,
      retryDelay: 1000,
      maxCacheSize: 100,
      ...config,
    }

    this.restoreFromStorage()
    this.startCleanupTimer()
  }

  static getInstance(config?: DictConfig): DictionaryManager {
    if (!DictionaryManager.instance) {
      DictionaryManager.instance = new DictionaryManager(config)
    }
    return DictionaryManager.instance
  }

  /**
   * 启动定期清理定时器
   */
  private startCleanupTimer(): void {
    if (this.cleanupTimer) return

    this.cleanupTimer = setInterval(
      () => {
        this.cleanExpiredCache()
      },
      5 * 60 * 1000,
    ) as unknown as number
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now()
    const expiredKeys: (string | number)[] = []

    this.cache.forEach((item, key) => {
      if (now >= item.expireTime) {
        expiredKeys.push(key)
      }
    })

    if (expiredKeys.length > 0) {
      expiredKeys.forEach((key) => this.cache.delete(key))
      this.saveToStorage()

      if (this.config.debug) {
        console.log('[Dictionary] 清理过期缓存:', expiredKeys.length, '项')
      }
    }
  }

  /**
   * LRU缓存淘汰策略
   */
  private evictLRUCache(): void {
    if (this.cache.size < this.config.maxCacheSize) return

    const entries = Array.from(this.cache.entries()).sort(
      (a, b) => a[1].lastAccess - b[1].lastAccess,
    )

    const removeCount = Math.max(1, this.cache.size - this.config.maxCacheSize + 1)
    for (let i = 0; i < removeCount && i < entries.length; i++) {
      const [key] = entries[i]
      this.cache.delete(key)

      if (this.config.debug) {
        console.log('[Dictionary] LRU淘汰缓存:', key)
      }
    }
  }

  /**
   * 订阅缓存更新
   */
  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback)
    return () => this.subscribers.delete(callback)
  }

  /**
   * 通知订阅者
   */
  private notify(): void {
    nextTick(() => {
      this.subscribers.forEach((callback) => {
        try {
          callback()
        } catch (error) {
          console.error('[Dictionary] 订阅者回调错误:', error)
        }
      })
    })
  }

  /**
   * 从本地存储恢复缓存
   */
  private restoreFromStorage(): void {
    if (!this.config.enableStorage) return

    try {
      const storage = uni.getStorageSync('__dict_cache__')
      if (!storage || typeof storage !== 'object') return

      const now = Date.now()
      let restoredCount = 0

      Object.entries(storage).forEach(([dictId, cacheItem]) => {
        try {
          const item = cacheItem as CacheItem
          if (
            item &&
            typeof item === 'object' &&
            item.data &&
            typeof item.expireTime === 'number' &&
            typeof item.timestamp === 'number'
          ) {
            if (now < item.expireTime) {
              const restoredItem: CacheItem = {
                data: item.data,
                timestamp: item.timestamp,
                expireTime: item.expireTime,
                accessCount: item.accessCount || 0,
                lastAccess: item.lastAccess || item.timestamp,
              }
              this.cache.set(dictId, restoredItem)
              restoredCount++
            }
          }
        } catch (itemError) {
          console.warn('[Dictionary] 恢复单个缓存项失败:', dictId, itemError)
        }
      })

      if (this.config.debug) {
        console.log('[Dictionary] 从存储恢复缓存:', restoredCount, '项')
      }
    } catch (error) {
      console.warn('[Dictionary] 恢复存储失败:', error)
      try {
        uni.removeStorageSync('__dict_cache__')
      } catch (removeError) {
        console.warn('[Dictionary] 清除损坏存储失败:', removeError)
      }
    }
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage(): void {
    if (!this.config.enableStorage) return

    try {
      const storage: Record<string, CacheItem> = {}
      const now = Date.now()

      this.cache.forEach((value, key) => {
        if (now < value.expireTime) {
          storage[key.toString()] = value
        }
      })

      uni.setStorageSync('__dict_cache__', storage)
    } catch (error) {
      console.warn('[Dictionary] 保存存储失败:', error)

      if (error.message?.includes('quota') || error.message?.includes('QuotaExceededError')) {
        this.clearOldestCache(10)
        try {
          const storage: Record<string, CacheItem> = {}
          this.cache.forEach((value, key) => {
            storage[key.toString()] = value
          })
          uni.setStorageSync('__dict_cache__', storage)
        } catch (retryError) {
          console.error('[Dictionary] 重试保存存储失败:', retryError)
        }
      }
    }
  }

  /**
   * 清理最老的缓存项
   */
  private clearOldestCache(count: number): void {
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp)
      .slice(0, count)

    entries.forEach(([key]) => this.cache.delete(key))

    if (this.config.debug) {
      console.log('[Dictionary] 清理最老缓存:', count, '项')
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheItem: CacheItem): boolean {
    return Date.now() < cacheItem.expireTime
  }

  /**
   * 更新缓存访问统计
   */
  private updateCacheAccess(dictId: string | number): void {
    const cached = this.cache.get(dictId)
    if (cached) {
      cached.accessCount++
      cached.lastAccess = Date.now()
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * 发起网络请求获取字典数据
   */
  private async fetchDictionary(dictId: string | number): Promise<dictionaryQueryByIdInt> {
    const startTime = Date.now()
    let lastError: Error

    this.requestStates.set(dictId, {
      status: RequestStatus.LOADING,
      retryCount: 0,
    })

    for (let attempt = 0; attempt <= this.config.retryCount; attempt++) {
      try {
        const result = await this.makeRequest(dictId)

        this.performanceMetrics.totalRequests++
        const responseTime = Date.now() - startTime

        if (this.performanceMetrics.totalRequests === 1) {
          this.performanceMetrics.averageResponseTime = responseTime
        } else {
          this.performanceMetrics.averageResponseTime =
            (this.performanceMetrics.averageResponseTime *
              (this.performanceMetrics.totalRequests - 1) +
              responseTime) /
            this.performanceMetrics.totalRequests
        }

        this.requestStates.set(dictId, {
          status: RequestStatus.SUCCESS,
          retryCount: attempt,
        })

        return result
      } catch (error) {
        lastError = error as Error

        this.requestStates.set(dictId, {
          status: RequestStatus.LOADING,
          retryCount: attempt + 1,
        })

        if (attempt < this.config.retryCount) {
          if (this.config.debug) {
            console.warn(
              `[Dictionary] 请求失败，${this.config.retryDelay * Math.pow(2, attempt)}ms后重试 (${attempt + 1}/${this.config.retryCount}):`,
              error,
            )
          }
          const delay = this.config.retryDelay * Math.pow(2, attempt)
          await this.delay(delay)
        }
      }
    }

    this.performanceMetrics.errorCount++
    this.requestStates.set(dictId, {
      status: RequestStatus.ERROR,
      error: lastError.message,
      retryCount: this.config.retryCount,
    })

    throw lastError
  }

  /**
   * 执行实际的网络请求
   */
  private async makeRequest(id: string | number): Promise<dictionaryQueryByIdInt> {
    try {
      const { data } = await dictionaryQueryById(
        {
          id,
        },
        {
          custom: {
            catch: true,
          },
        },
      )
      if (this.config.debug) {
        console.log(`[Dictionary] 获取字典 ${id}:`, data)
      }
      return data
    } catch (error) {
      throw new Error(`[Dictionary] 获取字典 ${id} 失败: ${error.message}`)
    }
  }

  /**
   * 获取字典数据
   */
  async getDictionary(dictId: string | number): Promise<dictionaryQueryByIdInt> {
    const cached = this.cache.get(dictId)
    if (cached && this.isCacheValid(cached)) {
      this.updateCacheAccess(dictId)
      this.performanceMetrics.cacheHits++
      if (this.config.debug) {
        console.log(`[Dictionary] 缓存命中 ${dictId}`)
      }
      return cached.data
    }

    this.performanceMetrics.cacheMisses++

    const existingPromise = this.loadingPromises.get(dictId)
    if (existingPromise) {
      return existingPromise
    }

    const promise = this.fetchDictionary(dictId)
    this.loadingPromises.set(dictId, promise)

    try {
      const data = await promise

      const now = Date.now()
      const cacheItem: CacheItem = {
        data,
        timestamp: now,
        expireTime: now + this.config.cacheTimeout,
        accessCount: 1,
        lastAccess: now,
      }

      this.evictLRUCache()

      this.cache.set(dictId, cacheItem)
      this.saveToStorage()
      this.notify()

      return data
    } finally {
      this.loadingPromises.delete(dictId)
    }
  }

  /**
   * 获取字典项标签
   */
  async getDictLabel(dictId: string | number, value: string | number): Promise<DictTextType> {
    try {
      const dict = await this.getDictionary(dictId)
      const item = dict[value]

      if (item === undefined || item === null) {
        if (this.config.debug) {
          console.warn(`[Dictionary] 未找到字典项: dictId=${dictId}, value=${value}`)
        }
        return value
      }

      return item
    } catch (error) {
      console.error(`[Dictionary] 获取字典标签失败: dictId=${dictId}, value=${value}`, error)
      return value
    }
  }

  /**
   * 清除指定字典缓存
   */
  clearCache(dictId?: string | number): void {
    if (dictId !== undefined) {
      this.cache.delete(dictId)
      this.requestStates.delete(dictId)
    } else {
      this.cache.clear()
      this.requestStates.clear()
      this.performanceMetrics = {
        totalRequests: 0,
        cacheHits: 0,
        cacheMisses: 0,
        averageResponseTime: 0,
        errorCount: 0,
      }
    }
    this.saveToStorage()
    this.notify()
  }

  /**
   * 预加载字典
   */
  async preload(dictIds: (string | number)[]): Promise<void> {
    try {
      await Promise.allSettled(dictIds.map((id) => this.getDictionary(id)))
      if (this.config.debug) {
        console.log('[Dictionary] 预加载完成:', dictIds)
      }
    } catch (error) {
      console.warn('[Dictionary] 预加载失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const now = Date.now()
    let validCount = 0
    let expiredCount = 0
    let totalAccessCount = 0

    this.cache.forEach((item) => {
      if (now < item.expireTime) {
        validCount++
      } else {
        expiredCount++
      }
      totalAccessCount += item.accessCount
    })

    const hitRate =
      this.performanceMetrics.totalRequests > 0
        ? (
            (this.performanceMetrics.cacheHits / this.performanceMetrics.totalRequests) *
            100
          ).toFixed(2)
        : '0.00'

    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount,
      hitRate: `${hitRate}%`,
      totalAccess: totalAccessCount,
      performance: { ...this.performanceMetrics },
    }
  }

  /**
   * 获取请求状态
   */
  getRequestState(dictId: string | number): RequestState | undefined {
    return this.requestStates.get(dictId)
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopCleanupTimer()
    this.cache.clear()
    this.loadingPromises.clear()
    this.subscribers.clear()
    this.requestStates.clear()
    if (this.config.enableStorage) {
      try {
        uni.removeStorageSync('__dict_cache__')
      } catch (error) {
        console.warn('[Dictionary] 清理存储失败:', error)
      }
    }
    DictionaryManager.instance = null
  }
}

// 字典管理 Hook
export function useDictionary(config?: DictConfig) {
  const manager = DictionaryManager.getInstance(config)
  const state = reactive({
    loading: new Set<string | number>(),
    errors: new Map<string | number, string>(),
  })
  let unsubscribe: (() => void) | null = null

  onMounted(() => {
    unsubscribe = manager.subscribe(() => {
      state.loading = new Set(state.loading)
      state.errors = new Map(state.errors)
    })
  })

  onUnmounted(() => {
    unsubscribe?.()
  })

  /**
   * 获取字典标签/文本
   * @param dictId 字典ID
   * @param value 字典值
   * @returns 字典标签
   * @example
   */
  const getDictLabel = async (
    dictId: string | number,
    value: string | number,
  ): Promise<DictTextType> => {
    const key = `${dictId}_${value}`
    state.loading.add(key)
    state.errors.delete(key)
    try {
      const label = await manager.getDictLabel(dictId, value)
      if (typeof label === 'string' && !isNaN(Number(label)) && String(Number(label)) === label) {
        return Number(label)
      }
      return label
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '获取字典标签失败'
      state.errors.set(key, errorMsg)
      throw error
    } finally {
      state.loading.delete(key)
    }
  }

  /**
   * 获取字典数据
   * @param dictId 字典ID
   * @returns 完整字典数据对象
   */
  const getDictData = async (dictId: string | number): Promise<dictionaryQueryByIdInt> => {
    state.loading.add(dictId)
    state.errors.delete(dictId)
    try {
      const data = await manager.getDictionary(dictId)
      return data
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '获取字典数据失败'
      state.errors.set(dictId, errorMsg)
      throw error
    } finally {
      state.loading.delete(dictId)
    }
  }

  /**
   * 获取字典选项数组
   * @param dictId 字典ID
   * @returns 选项数组
   */
  const getDictOptions = async (dictId: string | number): Promise<DictOption[]> => {
    const data = await getDictData(dictId)
    return Object.entries(data).map(([value, text]) => ({
      value: !isNaN(Number(value)) && String(Number(value)) === value ? Number(value) : value,
      text: (() => {
        if (typeof text === 'string' && !isNaN(Number(text)) && String(Number(text)) === text) {
          return Number(text)
        }
        return text
      })(),
    }))
  }

  const isLoading = computed(() => state.loading.size > 0)
  const hasErrors = computed(() => state.errors.size > 0)
  const cacheStats = computed(() => manager.getCacheStats())

  return {
    /** 获取字典标签 */
    getDictLabel,
    /** 获取字典数据 */
    getDictData,
    /** 获取字典选项数组 */
    getDictOptions,
    /** 是否加载中 */
    isLoading,
    /** 是否有错误 */
    hasErrors,
    /** 错误信息 */
    errors: computed(() => Object.fromEntries(state.errors)),
    /** 缓存统计信息 */
    cacheStats,
    /** 清除缓存 */
    clearCache: (dictId?: string | number) => manager.clearCache(dictId),
    /** 预加载字典数据 */
    preload: (dictIds: (string | number)[]) => manager.preload(dictIds),
  }
}
