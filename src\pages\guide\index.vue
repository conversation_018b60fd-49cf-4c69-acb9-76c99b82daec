<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-Guide">
    <view class="guide" :style="{ paddingTop: `${sysStatusBarHeight + 200}px` }">
      <view class="guide-box" />
    </view>
  </view>
</template>

<script setup lang="ts">
const { newInfoStepPage } = useNewInfoAll()
const { getUserIsLogin } = useUserInfo()
const { sysStatusBarHeight } = useSystemInfo()

async function reload() {
  await uni.$onLaunched
  if (!getUserIsLogin.value) {
    uni.reLaunch({
      url: '/pages/login/index',
    })
    return
  }
  newInfoStepPage()
}

onMounted(() => {
  reload()
})
</script>

<style scoped lang="scss">
.bg-Guide {
  width: 100%;
  height: 100vh;
  background-color: #fff;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
  @include graph-img('@/static/images/login/guide');

  .guide {
    .guide-box {
      width: 160rpx;
      height: 160rpx;
      margin: auto;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 40rpx;

      .guide-box-img {
        width: 120rpx;
        height: 120rpx;
        padding: 20rpx;
      }
    }

    .guide-name {
      margin-top: 20rpx;
      font-size: 40rpx;
      font-weight: 700;
      color: #000000;
      text-align: center;
    }
  }
}
</style>
