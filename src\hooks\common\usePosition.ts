import { useLoginStore } from '@/store'
import { randomInt } from '@/utils/util'
export const usePosition = () => {
  // 存储列表数据
  const setmyjobList = (list: any) => {
    if (!list || list.length === 0) return

    // 创建新的数组，避免累积问题
    const newJobList: any[] = []

    list.forEach((ele: any) => {
      const competitiveness = randomInt(80, 99)
      const matchingDegree = randomInt(1, 150)
      const count =
        matchingDegree >= 3
          ? [randomInt(1, 12), randomInt(1, 12), randomInt(1, 12)]
          : matchingDegree === 2
            ? [randomInt(1, 12), randomInt(1, 12)]
            : [randomInt(1, 12)]
      const id = ele.id

      // 确保id存在才添加
      if (id) {
        newJobList.push({
          id,
          count,
          competitiveness,
          matchingDegree,
        })
      }
    })

    // 传递新创建的数组到store
    useLoginStore().setmyjobList(newJobList)
  }

  return {
    setmyjobList,
  }
}
