import { POST } from '../index'
import { hrExchangeNumberExchangeDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** B端用户发起交换电话的请求接口 */
export const hrExchangeNumberExchange = (
  data: hrExchangeNumberExchangeDataInt,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/hrExchangeNumber/exchange', data, config)

/** B端HR同意或拒绝应聘者发送的交换电话请求处理接口 */
export const hrExchangeNumberDoExchange = (
  data: Api.IM.CustomMessage.ModifyCustomMessage,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/hrExchangeNumber/doExchange', data, config)
