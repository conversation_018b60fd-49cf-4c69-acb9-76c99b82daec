<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-navbar
        left-arrow
        :bordered="false"
        safe-area-inset-top
        custom-class="!bg-transparent"
        @click-left="handleClickLeft"
      />
    </template>
    <view class="mt-66rpx flex flex-col items-center gap-44rpx">
      <view class="flex items-center relative ml-60rpx">
        <view
          class="absolute left--60rpx shadow-[0rpx_22rpx_43rpx_0rpx_rgba(0,0,0,0.3)] rounded-30rpx overflow-hidden"
        >
          <wd-img :src="userInfo.avatar" height="121rpx" width="121rpx" />
        </view>
        <view
          class="flex flex-col justify-center gap-10rpx w-433rpx h-144rpx bg-white shadow-[0rpx_36rpx_58rpx_0rpx_rgba(0,0,0,0.03)] rounded-35rpx pl-88rpx"
        >
          <text class="c-#333333 text-28rpx">{{ userInfo?.name }}</text>
        </view>
      </view>
      <view
        class="flex items-center w-640rpx h-118rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx px-30rpx"
      >
        <text class="flex-1 c-#333333 text-28rpx">设置备注</text>
        <wd-icon name="arrow-right" color="#333333" size="30rpx" />
      </view>
      <wd-config-provider :themeVars="themeVars">
        <view
          class="bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx w-640rpx px-30rpx py-25rpx"
        >
          <view class="py-25rpx flex items-center">
            <view class="flex-1">
              <text class="c-#333333 text-28rpx">置顶联系人</text>
            </view>
            <wd-switch
              v-model="checked1"
              size="small"
              active-color="#FFFFFF"
              inactive-color="#FFFFFF"
              custom-class="shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] switch-top-contact"
            />
          </view>
          <view class="py-25rpx flex items-center">
            <view class="flex-1 flex flex-col gap-6rpx">
              <text class="c-#333333 text-28rpx">加入黑名单</text>
              <text class="c-#888888 text-22rpx">加入黑名单后，将不再接受对方发送的消息</text>
            </view>
            <wd-switch
              v-model="checked2"
              size="small"
              active-color="#FFFFFF"
              inactive-color="#FFFFFF"
              custom-class="shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] switch-add-top-blacklist"
            />
          </view>
          <view class="py-25rpx flex items-center">
            <view class="flex-1">
              <text class="c-#333333 text-28rpx">不感兴趣</text>
            </view>
            <wd-switch
              v-model="checked3"
              size="small"
              active-color="#FFFFFF"
              inactive-color="#FFFFFF"
              custom-class="shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] switch-uninterested"
            />
          </view>
        </view>
      </wd-config-provider>
      <view
        class="flex items-center w-640rpx h-118rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx px-30rpx"
      >
        <text class="flex-1 c-#333333 text-28rpx">举报对方</text>
        <wd-icon name="arrow-right" color="#333333" size="30rpx" />
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'

const { pageStyle } = usePaging({
  style: {
    background: '#F0F0F0',
  },
})
const checked1 = ref(false)
const checked2 = ref(false)
const checked3 = ref(false)
const themeVars: ConfigProviderThemeVars = {
  switchWidth: '100rpx',
  switchHeight: '40rpx',
  switchCircleSize: '46rpx',
}
const userInfo = computed(() => {
  const conv = uni.$UIKit.convStore.currConversation
  const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(conv.conversationId)
  return {
    name: userInfo?.name,
    id: conv.conversationId,
    avatar: userInfo?.avatar,
    conversationType: conv.conversationType,
    presenceExt: userInfo?.presenceExt,
    isOnline: userInfo?.isOnline,
    ext: JSON.parse(userInfo?.ext || '{}') as Api.IM.UserBusinessExtInfo,
  }
})

function handleClickLeft() {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
:deep(.switch-top-contact) {
  .wd-switch__circle {
    width: 46rpx;
    height: 32rpx;
    background: #3e9cff;
    border-radius: 20rpx;
    &::after {
      display: none;
    }
  }
}
:deep(.switch-add-top-blacklist) {
  .wd-switch__circle {
    width: 46rpx;
    height: 32rpx;
    background: #ff3e3e;
    border-radius: 20rpx;
    &::after {
      display: none;
    }
  }
}
:deep(.switch-uninterested) {
  .wd-switch__circle {
    width: 46rpx;
    height: 32rpx;
    background: #777777;
    border-radius: 20rpx;
    &::after {
      display: none;
    }
  }
}
</style>
