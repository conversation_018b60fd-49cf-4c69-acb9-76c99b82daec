import type { pagingDataInt } from '@/service/types'
interface usePagingInt {
  style?: Partial<CSSStyleDeclaration>
}
/** 分页长用hooks */
export const usePaging = <T = AnyObject>(paging?: usePagingInt) => {
  /** z-paging组件实例 */
  const pagingRef = ref<ZPagingInstance>()
  /** 分页信息 */
  const pageInfo = reactive<Pick<pagingDataInt, 'page' | 'size'>>({
    page: 1,
    size: uni.$zp.config['default-page-size'] as number,
  })
  /** 分页数据 */
  const pageData = ref<T[]>([]) as Ref<T[]>
  /** 滚动信息 */
  const pageScrollInfo = ref<ZPagingParams.ScrollInfo>({} as ZPagingParams.ScrollInfo)
  /** z-paging的style */
  const pageStyle = paging?.style || {}
  const pageSetInfo = (page: number, size: number) => {
    pageInfo.page = page
    pageInfo.size = size
  }
  const pageSetScrollInfo = (scrollInfo: ZPagingParams.ScrollInfo) => {
    pageScrollInfo.value = scrollInfo
  }
  return {
    pagingRef,
    pageInfo,
    pageSetInfo,
    pageSetScrollInfo,
    pageData,
    pageStyle,
    pageScrollInfo,
  }
}
