<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="规则中心"></CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-list border-b flex-between" @click="goUserAgreement">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘用户协议</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goUserRules">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘企业端用户规则</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goEnterpriseResourses">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘增值服务协议</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goRecruitmentbehavior">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘招聘行为管理规范</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月27日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goCommAgreement">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘沟通行为规范</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>

      <view class="setting-list border-b flex-between" @click="goPrivacyPolicy">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘隐私政策</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>

      <view class="setting-list border-b flex-between" @click="goHumanResources">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘人力资源协议</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list border-b flex-between" @click="goResumeUser">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘简历用户协议</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>

      <view class="setting-list border-b flex-between" @click="goCancelAgreement">
        <view class="">
          <view class="flex-c align-baseline">
            <view class="list-item-text text-32rpx m-r-20rpx">易直聘注销协议</view>
            <view class="subText-1 subText-ver">版本：ver202505</view>
          </view>
          <view class="subText-1 m-t-10rpx">生效时间：2025年05月24日</view>
        </view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 注销
const goCancelAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/CancelAgreement',
  })
}
// 增值
const goEnterpriseResourses = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/EnterpriseResourses',
  })
}
// 招聘行为
const goRecruitmentbehavior = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/Recruitmentbehavior',
  })
}
// 用户
const goUserRules = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/UserRules',
  })
}
// 用户协议
const goUserAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/UserAgreement',
  })
}
// 人力资源
const goHumanResources = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/HumanResources',
  })
}
// 沟通
const goCommAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/CommAgreement',
  })
}
const goResumeUser = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/ResumeUser',
  })
}
const goPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/PrivacyPolicy',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 20rpx 20rpx;
  .setting-list {
    padding: 30rpx 20rpx;
    .list-item-text {
      color: #333;
    }
  }
}
.subText-1 {
  font-size: 24rpx;
  color: #555;
}

.align-baseline {
  align-items: baseline;
  .subText-ver {
    font-size: 20rpx;
  }
}

.m-t-10rpx {
  margin-top: 10rpx;
}
.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 80rpx;
  left: 15%;
  width: 70%;
  margin: auto;
}

::v-deep .u-button {
  height: 80rpx;
  border-radius: 30rpx;
}

::v-deep .u-button__text {
  font-size: 28rpx !important;
  font-weight: bold;
  color: #fff !important;
}
</style>
