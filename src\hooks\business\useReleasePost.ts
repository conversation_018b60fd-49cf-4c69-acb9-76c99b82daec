import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'

interface releaseActivePostInt extends hrPositionQueryOptionListInt, Record<string, any> {}

const releaseActivePost = ref<releaseActivePostInt>({})
const releaseSeniorPostActivePost = ref<releaseActivePostInt>({})
/** 发布岗位hooks */
export const useReleasePost = () => {
  const releaseIsHavePost = computed(() => !!releaseActivePost.value?.id)
  const releaseIsHaveSeniorPost = computed(() => !!releaseSeniorPostActivePost.value?.id)
  return {
    releaseActivePost,
    releaseIsHavePost,
    releaseSeniorPostActivePost,
    releaseIsHaveSeniorPost,
  }
}
