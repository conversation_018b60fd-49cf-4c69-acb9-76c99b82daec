<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="login-containner bg-img">
    <view :style="{ paddingTop: statusBar * 2 + 'rpx' }" class="login-top">
      <image :src="logoImg" class="logo-img"></image>

      <view class="login-input">
        <view class="login-b relative">
          <view class="text-34rpx p-l-10rpx absolute left-10rpx bottom-0rpx">+86</view>
          <wd-input
            v-model="phone"
            :maxlength="11"
            no-border
            placeholder="请输入手机号"
            type="number"
          />
        </view>
        <!-- <view class="login-xy-constr">首次登录将自动注册</view> -->
        <view class="login-xy flex-c p-t-30rpx">
          <wd-checkbox
            v-model="checked"
            checked-color="#44BCC6"
            placeholderClass="placeholderClass"
            shape="square"
            size="30"
          ></wd-checkbox>

          <view class="login-xy-text">
            请阅读并同意
            <text class="c-#0275ff text-24rpx" @click="uesrAgreement">《用户协议》</text>
            <text class="c-#0275ff text-24rpx" @click="privacyAgreement">《隐私协议》</text>
          </view>
        </view>
      </view>
    </view>
    <view class="login-container">
      <view class="login-btn">
        <view class="btn_fixed" @click="setCode">
          <view class="btn_box">
            <view class="btn_bg font-600">手机验证登录</view>
          </view>
        </view>
        <view class="login-text-nomal" @click="accountLogin">账户密码登录</view>
      </view>
      <view class="login-ohter">
        <wd-divider>其他登录选项</wd-divider>
        <view class="login-ohter-icon">
          <view class="t-icon t-icon-weixin"></view>
          <view class="t-icon t-icon-QQ"></view>
          <view class="t-icon t-icon-zhifubaozhifu"></view>
        </view>
      </view>
    </view>
    <common-link></common-link>
    <!-- <wd-message-box /> -->
    <wd-message-box selector="wd-message-box-slot">
      <view class="text-26rpx flex-c flex-wrap flex-just line-20 dark-color font-500">
        请阅读并同意
      </view>
      <view class="flex-c flex-wrap flex-just line-20 dark-color font-500">
        <view class="color-b text-26rpx font-500" @click="uesrAgreement">《用户协议》</view>

        <view class="color-b text-26rpx font-500" @click="privacyAgreement">《隐私协议》</view>
      </view>
    </wd-message-box>
    <AgreementDiog
      :show="showDialog"
      @update:show="showDialog = $event"
      :type="type"
      v-if="type"
      :phone="phone"
      @changePhoneAgree="changePhoneAgree"
    />
  </view>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { setLoginPhone, getStatusBar } from '@/utils/storage'
import { phoneCheck } from '@/interPost/login/index'
import CommonLink from '@/components/CommonLink/CommonLink.vue'
import logoImg from '@/static/images/login/logo.png'
import AgreementDiog from '@/components/agreementDiog/index.vue'
import { regPhone } from '@/utils/rule'
import { useUserStore } from '@/store/user'

const message = useMessage('wd-message-box-slot')
const { aLiAuthInit } = useALiAuth()

// 手机号
// const phone = ref('')
const phone = ref('')
// 复选框
const checked = ref(false)
// 协议弹窗
const showDialog = ref(false)
const type = ref('')
// 状态栏
const statusBar = ref(0)
onLoad(() => {
  // 状态栏赋值
  statusBar.value = getStatusBar()
})
// 账户密码登录
const accountLogin = () => {
  uni.navigateTo({
    url: '/loginSetting/accountLogin/index',
  })
}
const changePhoneAgree = () => {
  checked.value = true
}
const userStore = useUserStore()
// 手机号登陆提交
const setCode = async () => {
  if (!regPhone.test(phone.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  setLoginPhone(phone.value)
  if (!userStore.hasShownAgreement) {
    showDialog.value = true
    type.value = 'phoneLogin'
    userStore.setHasShownAgreement(true)
    return
  }
  checked.value = true
  phoneCheck({
    phone: phone.value,
  }).then((res: any) => {
    if (res.code === 0) {
      if (res.data.code === '0' || res.data.code === '1') {
        uni.navigateTo({
          url: '/loginSetting/verifiCode/index',
        })
      } else {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 3000,
        })
      }
    }
  })
}
const uesrAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/ResumeUser',
  })
}
const privacyAgreement = () => {
  uni.navigateTo({
    url: '/setting/PrivacyAgreement/PrivacyPolicy',
  })
}

onMounted(() => {
  aLiAuthInit()
})
</script>

<style lang="scss" scoped>
::v-deep .wd-checkbox__label {
  margin-left: 4rpx !important;
}

::v-deep .wd-input {
  width: 100%;
  padding-left: 10rpx;
  text-align: center;
  background-color: transparent;
}

::v-deep .wd-input__placeholder {
  font-size: 36rpx !important;
}

::v-deep .wd-input__inner {
  font-size: 36rpx !important;
  color: #18181b !important;
}

.color-b {
  color: #145ad1;
}

::v-deep .wd-button__text {
  color: #000;
}

::v-deep .wd-button.is-primary {
  color: #fff;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
}

.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 0rpx 0rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx 30rpx;
      font-size: 14px;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.logo-img {
  width: 156rpx;
  height: 156rpx;
  padding-top: 170rpx;
  padding-bottom: 70rpx;
  margin: 0rpx auto 0rpx;
  border-radius: 44rpx;
}

.login-input {
  padding-bottom: 10rpx;
  margin: 0rpx 120rpx 0rpx;
}

.login-b {
  border-bottom: 1rpx solid #d6d6d6;
}

.login-top {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-xy-constr {
  padding-top: 20rpx;
  padding-bottom: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  // padding-left: 126rpx;
  text-align: center;
}

.login-xy {
  font-weight: 400;

  .login-xy-text {
    font-size: 24rpx;
    color: #000;
    text-align: center;
  }
}

.login-container {
  padding-top: 150rpx;

  .login-btn {
    padding: 0rpx 44rpx 0rpx;
    text-align: center;

    .login-text-nomal {
      margin: 20rpx 0rpx 0rpx;
      font-size: 28rpx;
    }
  }
}

.login-ohter {
  padding: 120rpx 140rpx 0rpx;
  color: #52525b;

  .login-ohter-icon {
    padding-top: 20rpx;
    text-align: center;
    // background-color: red;

    .t-icon {
      display: inline-block !important;
      width: 50rpx;
      height: 50rpx;
      margin: 0px 30rpx;
    }
  }
}

.login-input-l {
  font-size: 40rpx;
  font-weight: 600;
}

.textName {
  padding-bottom: 10rpx;
  color: $uni-text-color-grey;
  text-align: center;
}
</style>
