<template>
  <view
    :build="build"
    :show="show"
    :close="close"
    :scene="scene"
    :prefix="prefix"
    :change:build="test.toBuild"
    :change:close="test.toClose"
    :change:show="test.toShow"
  >
    <view id="captcha-element"></view>
    <view id="captcha-button" class="captcha-trigger">触发验证</view>
  </view>
</template>

<script lang="renderjs" module="test">
// ['scene', 'prefix', 'sig', 'token']
let captchaInstance = null
export default {
  mounted() {
    const script = document.createElement('script')
    script.src = 'https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js'
    script.onload = () => {
      window.__aliyunCaptchaInit = window.initAliyunCaptcha
      this.$ownerInstance.callMethod('getVerificationCode')
    }
    document.head.appendChild(script)
  },
  watch: {
    scene() {
      this.initCaptcha()
    },
    prefix() {
      this.initCaptcha()
    },
    // sig() {
    // 	this.initCaptcha()
    // },
    // token() {
    // 	this.initCaptcha()
    // }
  },
  methods: {
    initCaptcha() {
      if (typeof window.__aliyunCaptchaInit !== 'function') return

      try {
        // 清理旧实例
        if (captchaInstance) {
          captchaInstance.destroy()
          document.getElementById('aliyunCaptcha-mask')?.remove()
          document.getElementById('aliyunCaptcha-window-popup')?.remove()
        }
        // 从props获取最新参数
        // const params = {
        // 	scene: '7n7tuynm',
        // 	prefix: '198dz0'
        // }
        // console.log('验证码参数-==:', ...params)

        // 初始化新实例
        captchaInstance = window.__aliyunCaptchaInit({
          mode: 'popup',
          element: '#captcha-element',
          button: '#captcha-button',
          scene: '7n7tuynm',
          prefix: '198dz0',
          // ...params,
          captchaVerifyCallback: (param) => {
            console.log(param, 'param==')
            this.$ownerInstance.callMethod('callback', param)
          },
          slideStyle: {
            width: '320px',
            height: '40px',
          },
          language: 'cn',
        })

        this.$ownerInstance.callMethod('buildEnd')
      } catch (e) {
        console.error('验证码初始化失败:', e)
      }
    },

    toBuild(newValue) {
      if (newValue) this.initCaptcha()
    },

    toShow(newValue) {
      if (newValue && captchaInstance) {
        const btn = document.getElementById('captcha-button')
        btn?.click()
        setTimeout(() => {
          const closeBtn = document.getElementById('aliyunCaptcha-btn-close')
          closeBtn?.addEventListener('click', () => {
            this.$ownerInstance.callMethod('showEnd')
          })
        }, 300)
      }
    },

    toClose(newValue) {
      if (newValue && captchaInstance) {
        captchaInstance.reset()
        this.$ownerInstance.callMethod('closeEnd')
      }
    },
  },
  beforeUnmount() {
    // captchaInstance?.destroy()
  },
}
</script>

<script>
export default {
  data() {
    return {
      build: null,
      show: null,
      close: null,
      scene: '',
      prefix: '',
      // sig: '',
      // token: ''
    }
  },
  methods: {
    async getVerificationCode() {
      // 模拟接口请求
      const mockData = {
        scene: '7n7tuynm',
        prefix: '198dz0',
        // sig: '22',
        // token: '998'
      }

      // 使用$nextTick确保DOM更新
      this.$nextTick(() => {
        this.scene = mockData.scene
        this.prefix = mockData.prefix
        // this.sig = mockData.sig
        // this.token = mockData.token
        this.build = true // 触发build变更
      })
    },
    showCap() {
      this.show = true
    },
    buildEnd() {
      this.build = false
    },
    closeEnd() {
      this.close = false
      this.build = true
    },
    showEnd() {
      this.show = false
    },
    callback(param) {
      console.log('验证结果:', param)
      // 这里处理验证成功的逻辑
    },
  },
}
</script>
