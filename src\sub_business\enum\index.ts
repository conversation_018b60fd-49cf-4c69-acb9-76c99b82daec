import exposureRefreshIcon from '@/sub_business/static/prop/exposure-refresh-icon.png'
import exposureRefreshTitle from '@/sub_business/static/prop/exposure-refresh-title.png'
import exposureRefreshUsTitle from '@/sub_business/static/prop/exposure-refresh-us-title.png'
import exposureRefreshRight from '@/sub_business/static/prop/exposure-refresh-right.png'
import magicBombIcon from '@/sub_business/static/prop/magic-bomb-icon.png'
import magicBombTitle from '@/sub_business/static/prop/magic-bomb-title.png'
import magicBombUsTitle from '@/sub_business/static/prop/magic-bomb-us-title.png'
import magicBombRight from '@/sub_business/static/prop/magic-bomb-right.png'
import speedTopIcon from '@/sub_business/static/prop/speed-top-icon.png'
import speedTopTitle from '@/sub_business/static/prop/speed-top-title.png'
import speedTopUsTitle from '@/sub_business/static/prop/speed-top-us-title.png'
import speedTopRight from '@/sub_business/static/prop/speed-top-right.png'
import unlimitedChatIcon from '@/sub_business/static/prop/unlimited-chat-icon.png'
import unlimitedChatTitle from '@/sub_business/static/prop/unlimited-chat-title.png'
import unlimitedChatUsTitle from '@/sub_business/static/prop/unlimited-chat-us-title.png'
import unlimitedChatRight from '@/sub_business/static/prop/unlimited-chat-right.png'

/** 道具卡 枚举属性 */
export const enumProp = [
  /** 无限畅聊卡 */
  {
    key: 'UNLIMITED_CHAT',
    name: '无限畅聊卡',
    value: 1,
    backgroundColor: '#FF4545',
    icon: unlimitedChatIcon,
    title: unlimitedChatTitle,
    titleUs: unlimitedChatUsTitle,
    rightInfo: {
      src: unlimitedChatRight,
      width: '280rpx',
      height: '128rpx',
    },
  },
  /** 极速置顶卡 */
  {
    key: 'SPEED_TOP',
    name: '极速置顶卡',
    value: 2,
    backgroundColor: '#FF5F29',
    icon: speedTopIcon,
    title: speedTopTitle,
    titleUs: speedTopUsTitle,
    rightInfo: {
      src: speedTopRight,
      width: '398rpx',
      height: '90rpx',
    },
  },
  /** 曝光刷新卡 */
  {
    key: 'EXPOSURE_REFRESH',
    name: '曝光刷新卡',
    value: 3,
    backgroundColor: '#3E68FF',
    icon: exposureRefreshIcon,
    title: exposureRefreshTitle,
    titleUs: exposureRefreshUsTitle,
    rightInfo: {
      src: exposureRefreshRight,
      width: '410rpx',
      height: '86rpx',
    },
  },
  /** 神马炸弹卡 */
  {
    key: 'MAGIC_BOMB',
    name: '神马炸弹卡',
    value: 4,
    backgroundColor: '#353535',
    icon: magicBombIcon,
    title: magicBombTitle,
    titleUs: magicBombUsTitle,
    rightInfo: {
      src: magicBombRight,
      width: '446rpx',
      height: '100rpx',
    },
  },
] as const
