<template>
  <view class="fixed right-0 bottom-[286rpx]">
    <view
      class="relative w-[81px] h-[161px] overflow-visible select-none"
      @touchstart="onTouchStart"
      @touchmove="onTouchMove"
      @touchend="onTouchEnd"
    >
      <view
        class="dashed-semicircle"
        :style="{ width: size / 2 + 'px', height: size + 'px' }"
      ></view>
      <view
        v-for="(item, idx) in visibleItems"
        :key="idx"
        class="selector-item flex items-center justify-center w-[74rpx] h-[74rpx] text-[18px] font-bold bg-white rounded-full"
        :class="{ 'selector-item-active': idx === 1 }"
        :style="getItemStyle(idx)"
      >
        {{ item }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { CSSProperties } from 'vue'

const allData = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] // 可任意扩展
const visibleCount = 3 // 只显示3个
const size = 161
const radius = size / 2
const angleRange = Math.PI // 半圆
const oneStep = angleRange / (visibleCount - 1) // 预先计算单步角度

const currentIndex = ref(1) // 当前中间选中项
const angleOffset = ref(0) // 半圆角度偏移
const dragging = ref(false)
let lastY = 0
let accumulatedDeltaY = 0
let animationTimer: number | null = null
// 节流标记
let ticking = false

const visibleItems = computed(() => {
  const arr = []
  const middleIdx = currentIndex.value
  const topIdx = (middleIdx - 1 + allData.length) % allData.length
  const bottomIdx = (middleIdx + 1) % allData.length
  arr.push(allData[topIdx])
  arr.push(allData[middleIdx])
  arr.push(allData[bottomIdx])
  return arr
})

function onTouchStart(e: TouchEvent) {
  dragging.value = true
  lastY = e.touches[0].clientY // 添加记录Y坐标
  accumulatedDeltaY = 0 // 添加重置Y方向累积值
}

function onTouchMove(e: TouchEvent) {
  if (!dragging.value) return
  // 获取当前触摸坐标
  const clientY = e.touches[0].clientY
  // 计算垂直方向的移动距离
  const deltaY = clientY - lastY
  // 累积垂直方向的移动
  accumulatedDeltaY += deltaY

  // setTimeout 节流，提高性能 (16ms约等于60fps)
  if (!ticking) {
    setTimeout(() => {
      // 左半圆布局中，垂直方向滑动映射到角度变化
      // 向下滑动(deltaY为正)应该使元素沿轨道向下移动
      // 向上滑动(deltaY为负)应该使元素沿轨道向上移动
      angleOffset.value -= (accumulatedDeltaY / size) * angleRange * 1.0
      accumulatedDeltaY = 0 // 重置累积值
      ticking = false
    }, 16)
    ticking = true
  }

  // 更新上次Y坐标
  lastY = clientY
}

function onTouchEnd() {
  dragging.value = false
  if (angleOffset.value > oneStep / 2) {
    // 在左半圆布局中，正向偏移意味着向下滑动
    // 应选择下方元素(如C)，使其成为新的中间元素
    currentIndex.value = (currentIndex.value + 1) % allData.length
    animateReset(angleOffset.value - oneStep)
  } else if (angleOffset.value < -oneStep / 2) {
    // 在左半圆布局中，负向偏移意味着向上滑动
    // 应选择上方元素(如A)，使其成为新的中间元素
    currentIndex.value = (currentIndex.value - 1 + allData.length) % allData.length
    animateReset(angleOffset.value + oneStep)
  } else {
    animateReset(angleOffset.value)
  }
}

function animateReset(startOffset: number) {
  // 优化：如果偏移很小，直接重置而不是动画
  if (Math.abs(startOffset) < 0.01) {
    angleOffset.value = 0
    return
  }

  const duration = 300
  const start = Date.now() // 使用Date.now()替代performance.now()

  // 清理之前的动画
  if (animationTimer) clearTimeout(animationTimer)

  function animate() {
    const elapsed = Math.min((Date.now() - start) / duration, 1)
    angleOffset.value = startOffset * (1 - easeOutCubic(elapsed))

    if (elapsed < 1) {
      animationTimer = setTimeout(animate, 16)
    } else {
      angleOffset.value = 0
      animationTimer = null
    }
  }

  animationTimer = setTimeout(animate, 16)
}

// 缓存缓动函数结果以提高性能
const easeOutCubicCache = new Map<number, number>()
function easeOutCubic(t: number) {
  // 四舍五入到小数点后两位，减少不必要的计算
  const rounded = Math.round(t * 100) / 100

  if (!easeOutCubicCache.has(rounded)) {
    easeOutCubicCache.set(rounded, 1 - Math.pow(1 - rounded, 3))
  }

  return easeOutCubicCache.get(rounded)!
}

// 计算点的位置和缩放
const baseAngles = computed(() => {
  // 调整为左半圆的角度范围 (270度到90度，即-π/2到π/2)
  const reducedAngleRange = Math.PI * 0.6 // 约108度，保持紧凑的弧度
  // 计算角度偏移，让轨道保持居中，以π/2为中心点
  const centerOffset = (Math.PI - reducedAngleRange) / 2
  // 动态生成索引数组，根据visibleCount大小
  const indexes = Array.from({ length: visibleCount }, (_, i) => i)
  // 返回调整后的角度值（从底部开始，逆时针分布）
  return indexes.map(
    (idx) => Math.PI / 2 - centerOffset - (reducedAngleRange / (visibleCount - 1)) * idx,
  )
})

function getItemStyle(idx: number): Partial<CSSProperties> {
  const centerX = size / 2
  const centerY = size / 2
  const angle = baseAngles.value[idx] + angleOffset.value
  // 对于左半圆，x和y的计算需要调整
  const x = centerX - radius * Math.cos(angle)
  const y = centerY - radius * Math.sin(angle)

  const isActive = idx === 1

  return {
    left: `${x}px`,
    top: `${y}px`,
    transform: `translate(-50%, -50%) scale(${isActive ? 1.3 : 1})`,
    zIndex: isActive ? 2 : 1,
    transition: dragging.value
      ? 'none'
      : 'transform 0.35s cubic-bezier(.35,1.44,.69,.97), color 0.3s',
    position: 'absolute',
    willChange: 'transform',
  }
}

// 组件卸载时清理动画定时器
onUnmounted(() => {
  if (animationTimer) clearTimeout(animationTimer)
})
</script>

<style scoped>
/* 复杂的样式保留为CSS */
.dashed-semicircle {
  @apply absolute top-0 left-0 box-border w-full h-full pointer-events-none;
  border-top: 1px dashed #acacac;
  border-right: none;
  border-bottom: 1px dashed #acacac;
  border-left: 1px dashed #acacac;
  border-radius: 150px 0 0 150px / 150px 0 0 150px;
}

.selector-item {
  @apply transition-transform duration-350 text-[#ffffff]  bg-[#FF7373];
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
  transition-timing-function: cubic-bezier(0.35, 1.44, 0.69, 0.97);
}

.selector-item-active {
  @apply z-3 text-[24px]border-[3px];
  /* box-shadow: 0 6px 18px rgba(64, 158, 255, 0.2); */
}
</style>
