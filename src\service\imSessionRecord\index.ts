import { POST } from '../index'
import { imSessionRecordSaveSessionDataInt, imSessionRecordSaveSessionInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 业务会话记录自用 */
export const imSessionRecordSaveSession = (
  data: imSessionRecordSaveSessionDataInt,
  config?: HttpRequestConfig,
) =>
  POST<imSessionRecordSaveSessionInt>('/easyzhipin-api/imSessionRecord/saveSession', data, config)
