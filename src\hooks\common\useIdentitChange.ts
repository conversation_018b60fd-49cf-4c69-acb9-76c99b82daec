import { CommonUtil } from 'wot-design-uni'
import { changeToBAuth, changeToCAuth } from '@/interPost/common'
import { USER_TYPE } from '@/enum'

export const useChangeIdent = () => {
  const { userIntel, setUserIntel } = useUserInfo()
  const { newInfoStepPage } = useNewInfoAll()
  const {
    bool: changeIdentLoading,
    setFalse: changeIdentLoadingFalse,
    setTrue: changeIdentLoadingTrue,
  } = useBoolean()

  const changeIdent = CommonUtil.debounce(async () => {
    try {
      changeIdentLoadingTrue()
      const identConfig = {
        [USER_TYPE.APPLICANT]: { authFn: changeToBAuth, type: USER_TYPE.HR },
        [USER_TYPE.HR]: { authFn: changeToCAuth, type: USER_TYPE.APPLICANT },
      }
      const config = identConfig[userIntel.value.type]
      const { data } = await config.authFn()
      setUserIntel({ ...data, type: config.type })
      await newInfoStepPage(true, data.requiredFinishStatus)
      changeIdentLoadingFalse()
    } catch (error) {
      changeIdentLoadingFalse()
    }
  }, 500)

  return {
    changeIdent,
    changeIdentLoading,
  }
}
