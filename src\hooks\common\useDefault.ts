import { USER_TYPE, Gender } from '@/enum'
import applicantMan from '@/static/header/jobhunting1.png'
import applicantWoman from '@/static/header/jobhunting2.png'
import hrMan from '@/static/header/hrheader1.png'
import hrWoman from '@/static/header/hrheader2.png'

interface defaultOptions {
  roleType: USER_TYPE
}

export const useDefault = (options?: defaultOptions) => {
  const { roleType = USER_TYPE.APPLICANT } = options || {}

  const ROLE_AVATAR_MAP = {
    [USER_TYPE.APPLICANT]: {
      [Gender.MALE]: applicantMan,
      [Gender.FEMALE]: applicantWoman,
    },
    [USER_TYPE.HR]: {
      [Gender.MALE]: hrMan,
      [Gender.FEMALE]: hrWoman,
    },
  } as const

  const defaultRoleLogo = (sex: Gender, url: string): string => {
    return url || ROLE_AVATAR_MAP[roleType]?.[sex] || applicantMan
  }

  return {
    defaultRoleLogo,
  }
}
