import { POST, POSTPaging, GET_WITH_BODY } from '@/service'
import { HttpRequestConfig } from 'luch-request'
// ai 普通请求
// export const chat = (data: any, config?: HttpRequestConfig) => {
//   return GET_WITH_BODY('/easyzhipin-ai/aliChat/chat', { ...config, timeout: 30 * 1000, data })
// }
export const chat = (data: any, config?: HttpRequestConfig) => {
  return POST('/easyzhipin-ai/aliChat/chat', data, { ...config, timeout: 30 * 1000 })
}

// 历史记录
export const historyList = (data?: any) => {
  return POSTPaging('/easyzhipin-api/ai/historyList', data)
}
