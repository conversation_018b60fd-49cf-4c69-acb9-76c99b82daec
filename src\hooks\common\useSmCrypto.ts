import { sm4 } from 'sm-crypto'

type Sm4Type = 'default' | 'password'

interface UseSmCryptoOptions {
  type?: Sm4Type
}

const SM4_KEY = import.meta.env.VITE_APP_SM4_KEY
const PASSWORD_KEY = import.meta.env.VITE_APP_SM4_PASSWORD_KEY

/** 国密加解密hooks */
export const useSmCrypto = (options: UseSmCryptoOptions = {}) => {
  const { type = 'default' } = options
  const currentKey = type === 'default' ? SM4_KEY : PASSWORD_KEY

  /** 加密数据 */
  const sm4Encrypt = <T>(data: T) => {
    if (data === null || data === undefined) return ''
    const stringData = typeof data === 'string' ? data : JSON.stringify(data)
    return sm4.encrypt(stringData, currentKey)
  }
  /** 解密数据为字符串 */
  const sm4DecryptToString = (data: string) => {
    if (!data) return ''
    try {
      return sm4.decrypt(data, currentKey)
    } catch (error) {
      return ''
    }
  }
  /** 解密数据并转换为对象 */
  const sm4Decrypt = <T>(data: string) => {
    if (!data) return {} as T
    try {
      const decryptedString = sm4DecryptToString(data)
      return decryptedString ? (JSON.parse(decryptedString) as T) : ({} as T)
    } catch (error) {
      return {} as T
    }
  }

  return {
    sm4Encrypt,
    sm4Decrypt,
    sm4DecryptToString,
  }
}
