<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="资质公示"></CustomNavBar>
      <view class="m-40rpx">
        <view class="card-swiper">
          <wd-swiper
            :autoplay="false"
            v-model:current="current"
            custom-indicator-class="custom-indicator-class"
            custom-image-class="custom-image"
            custom-next-image-class="custom-image-prev"
            custom-prev-image-class="custom-image-prev"
            :indicator="{ type: 'none' }"
            :list="companyStyleVOLists"
            value-key="attachIdUrl"
            previousMargin="44px"
            nextMargin="44px"
            height="300rpx"
          ></wd-swiper>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import yyzz from '@/setting/img/yyzz.png'
import rlzy from '@/setting/img/rlzy.png'
import tel from '@/setting/img/tel.png'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const current = ref(1)
const companyStyleVOLists = ref([
  {
    attachIdUrl: yyzz,
  },
  {
    attachIdUrl: rlzy,
  },
  {
    attachIdUrl: tel,
  },
])
</script>

<style scoped lang="scss">
.card-swiper {
  --wot-swiper-radius: 0;
  --wot-swiper-item-padding: 0 24rpx;
  --wot-swiper-nav-dot-color: #e7e7e7;
  --wot-swiper-nav-dot-active-color: #4d80f0;
  padding-bottom: 24rpx;
  :deep(.custom-indicator-class) {
    bottom: -16px;
  }
  :deep(.custom-image) {
    border-radius: 20rpx;
  }
}
</style>
