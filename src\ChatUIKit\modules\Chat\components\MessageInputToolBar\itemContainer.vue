<template>
  <view class="tool-item-wrap flex flex-col items-center mb-12rpx text-center">
    <view class="size-76rpx rounded-10rpx mb-20rpx center bg-#f0f0f0">
      <image class="size-44rpx" :src="iconUrl"></image>
    </view>
    <view class="c-#000000 text-28rpx">{{ title }}</view>
  </view>
</template>

<script lang="ts" setup>
interface Props {
  title: string
  iconUrl: string
}
const props = defineProps<Props>()

const { title, iconUrl } = props
</script>

<style lang="scss" scoped></style>
