<template>
  <view class="chat-item">
    <text v-if="item.createTime" class="chat-time">
      {{ item.createTime }}
    </text>
    <view class="chat-container" :class="{ 'chat-location-me': item.type === 0 }">
      <view class="chat-icon-container">
        <image
          class="chat-icon"
          :src="item.type === 0 ? headImg : '/static/img/deepseek2.png'"
          mode="aspectFill"
        />
      </view>
      <view class="chat-content-container">
        <view
          class="chat-text-container-super"
          :style="{ justifyContent: item.type === 0 ? 'flex-end' : 'flex-start' }"
        >
          <view class="chat-text-container" :class="{ 'chat-text-container-me': item.type === 0 }">
            <text class="chat-text" :class="{ 'chat-text-me': item.type === 0 }">
              {{ item.content }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'chat-item',
  props: {
    item: {
      type: Object,
      default: () => ({
        createTime: '',
        icon: '',
        content: '',
        isMe: false,
      }),
    },
    headImg: {
      type: String,
      default: '',
    },
  },
}
</script>

<style scoped lang="scss">
.chat-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx;
}

.chat-time {
  padding: 4rpx 0;
  font-size: 22rpx;
  color: #aaaaaa;
  text-align: center;
}

.chat-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start !important;
}

.chat-location-me {
  flex-direction: row-reverse;
}

.chat-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  // background-color: #eeeeee;
}

.chat-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.chat-content-container {
  flex: 1;
  margin: 0 15rpx;
}

.chat-text-container-super {
  display: flex;
  flex-direction: row;
}

.chat-text-container {
  max-width: 500rpx;
  padding: 10rpx 15rpx;
  margin-top: 10rpx;
  text-align: left;
  background-color: #f1f1f1;
  border-radius: 8rpx;
}

.chat-text-container-me {
  background-color: #007aff;
}

.chat-text {
  font-size: 28rpx;
  word-break: break-word;
}

.chat-text-me {
  color: white;
}
</style>
