import { activityLogin } from '@/service/common'

export const useKeepAlive = () => {
  // 响应式状态
  const timerId = ref<number | null>(null)
  const isActive = ref(false)
  const lastCallTime = ref<Date | null>(null)
  const isRequesting = ref(false)

  // 启动保活机制
  const startKeepAlive = () => {
    // 如果已启动则不再重复启动
    if (isActive.value) return

    isActive.value = true
    console.log('保活机制启动')

    // 立即执行一次保活请求
    callKeepAliveAPI()

    // 设置定时器（10分钟 = 600000毫秒）
    timerId.value = setInterval(() => {
      callKeepAliveAPI()
    }, 600000)
  }

  // 停止保活机制
  const stopKeepAlive = () => {
    if (timerId.value) {
      clearInterval(timerId.value)
      timerId.value = null
      isActive.value = false
      console.log('保活机制已停止')
    }
  }

  // 调用保活API
  const callKeepAliveAPI = async () => {
    // 防止重复请求
    if (isRequesting.value) return

    try {
      isRequesting.value = true

      // 调用API
      const res = await activityLogin()
      lastCallTime.value = new Date()
      console.log('保活成功', res.data)
    } catch (error) {
      console.error('保活失败', error)
      // 如果请求失败，停止保活
      stopKeepAlive()
    } finally {
      isRequesting.value = false
    }
  }

  // 获取当前状态
  const getKeepAliveStatus = () => {
    return {
      isActive: isActive.value,
      lastCallTime: lastCallTime.value,
      isRequesting: isRequesting.value,
    }
  }

  return {
    // 状态
    isActive,
    lastCallTime,
    isRequesting,
    // 方法
    startKeepAlive,
    stopKeepAlive,
    callKeepAliveAPI,
    getKeepAliveStatus,
  }
}

/*
使用示例：

<script setup>
import { useKeepAlive } from '@/hooks/common/useKeepAlive'

// 在组件中使用 hook
const { 
  isActive, 
  lastCallTime, 
  isRequesting, 
  startKeepAlive, 
  stopKeepAlive, 
  callKeepAliveAPI, 
  getKeepAliveStatus 
} = useKeepAlive()

// 手动控制保活（可选）
const handleStartKeepAlive = () => {
  startKeepAlive()
}

const handleStopKeepAlive = () => {
  stopKeepAlive()
}

const handleManualCall = async () => {
  await callKeepAliveAPI()
}

// 获取状态
const status = getKeepAliveStatus()
console.log('保活状态:', status)
</script>

<template>
  <div>
    <p>保活状态: {{ isActive ? '运行中' : '已停止' }}</p>
    <p>最后调用时间: {{ lastCallTime }}</p>
    <p>请求状态: {{ isRequesting ? '请求中' : '空闲' }}</p>
    
    <button @click="handleStartKeepAlive">启动保活</button>
    <button @click="handleStopKeepAlive">停止保活</button>
    <button @click="handleManualCall">手动调用</button>
  </div>
</template>
*/
