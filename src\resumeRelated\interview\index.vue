<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="待面试">
      <template #right>
        <view class="text-28rpx c-#000" @click="goHistory">历史面试</view>
      </template>
    </CustomNavBar>
    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    >
      <view class="pageList">
        <view class="c-#888 text-32rpx p-b-20rpx p-l-40rpx" v-if="pageData.length > 0">时间</view>
        <view v-for="(item, index) in pageData" :key="index">
          <InterviewCard :item="item" />
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryWaitMeetingList } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import { getCustomBar } from '@/utils/storage'
import InterviewCard from '@/resumeRelated/component/interviewCard.vue'
import { truncateText } from '@/utils/util'
const { pagingRef, pageData, pageStyle } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '40rpx',
  },
})
const customBar = ref(null)
// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const queryList = async () => {
  const res: any = await queryWaitMeetingList()
  if (res.code === 0) {
    res.data &&
      res.data.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryStart =
          ele.workSalaryStart === 0 ? '面议' : numberTokw(ele.workSalaryStart + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
        if (!ele.headImgUrl) {
          ele.headImgUrl =
            ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
        }
      })

    pagingRef.value.complete(res.data)
  }
}
uni.$on('refreshInterviewListToPerson', () => {
  pagingRef.value.reload()
})
onUnload(async () => {
  uni.$off('refreshInterviewListToPerson')
})
onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  await pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.pageList-right {
  border-left: 1rpx solid #cccaca;
}
.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  background: #4399ff;
  border-radius: 20rpx;
}
</style>
