/* 清除默认边距和填充 */
view,
text {
  box-sizing: border-box;
  // padding: 0;
  // margin: 0;
  // font-family: 'PingFang', 'SC-Medium', 'TsangerYuYang', 'STSong';
  // font-size: 28rpx;
  // color: #333333;
}
/* 设置全局字体和行高 */
page {
  // line-height: 1.5;
  // text-align: left;
  background-color: #fcfcfc;
}
/* 清除列表默认符号 */
ul,
ol {
  padding-left: 0;
  list-style: none;
}
/* 链接默认样式重置 */
a {
  color: inherit; /* 继承父级颜色 */
  text-decoration: none;
}
/* 按钮默认样式重置 */
button {
  padding: 0;
  margin: 0;
  font-size: inherit;
  line-height: inherit;
  background-color: transparent;
  border: none;
}
/* 图片自适应处理 */
image {
  display: block; /* 避免底部间隙 */
  max-width: 100%;
  height: auto;
}
/* 禁用点击高亮 */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
/* 禁用长按菜单（如iOS的弹出菜单） */
* {
  -webkit-touch-callout: none;
}
/* 禁止文字选中 */
view,
text {
  -webkit-user-select: none;
  user-select: none;
}
/* 输入框和文本域 */
input,
textarea {
  padding: 0;
  margin: 0;
  font-size: inherit;
  color: inherit;
  /* 允许输入框和文本域的文字选中 */
  -webkit-user-select: text;
  user-select: text;
  background-color: transparent;
  border: none;
  outline: none;
}
/* wot-design-uni 组件内部输入元素 */
.wd-textarea textarea,
.wd-input input,
.wd-textarea__inner,
.wd-input__inner {
  -webkit-user-select: text !important;
  user-select: text !important;
}
/* 去掉输入框的浏览器默认填充背景 */
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px white inset;
}
/* 清除浮动 */
.clearfix::after {
  display: table;
  clear: both;
  content: '';
}
