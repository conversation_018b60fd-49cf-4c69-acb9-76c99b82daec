<template>
  <wd-collapse
    v-for="(item, index) in listArry"
    :key="index"
    v-model="value"
    accordion
    custom-class="record-collapse"
    @change="handleChange"
  >
    <wd-collapse-item :name="`${index + 1}`">
      <template #title="{ expanded }">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <wd-img :src="titleIcons[index]" class="m-r-10rpx" height="20" width="20" />
            <wd-img :src="titleImgs[index]" height="13" width="101" />
          </view>
          <wd-img :src="expanded ? iconTop : iconBottom" class="m-r-20rpx" height="9" width="11" />
        </view>
      </template>
      <view
        v-for="(ele, index) in displayRecordList"
        :key="ele.id"
        @click="handleClick(ele.id, ele.propCategoryName)"
      >
        <view
          class="flex items-center justify-between border-b-1rpx border-b-#EDEDED border-b-solid p-b-20rpx p-t-20rpx"
        >
          <view class="flex-1">
            <view class="c-#333 text-28rpx font-medium">
              {{ ele.propCategoryName }} -
              {{ formatDuration(ele.durationTime, ele.durationType) }}
            </view>
            <view class="c-#888 text-24rpx m-t-8rpx">{{ ele.endTime }}</view>
          </view>
          <wd-icon name="chevron-right" size="22px"></wd-icon>
        </view>
        <!-- 在第6条记录后显示查看更多按钮 -->
        <view
          v-if="index === 5 && useRecordList.length > 6"
          class="text-24rpx c-#2E6CFA text-center p-t-20rpx p-b-10rpx"
          @click.stop="moreList"
        >
          查看更多
        </view>
      </view>
      <!--      <template v-else>-->
      <!--        <view class="c-#888 text-24rpx m-t-8rpx">暂无使用记录</view>-->
      <!--      </template>-->
    </wd-collapse-item>
  </wd-collapse>
</template>

<script lang="ts" setup>
import title1 from '@/sub_business/static/prop/title1.png'
import title2 from '@/sub_business/static/prop/title2.png'
import title3 from '@/sub_business/static/prop/title3.png'
import title4 from '@/sub_business/static/prop/title4.png'
import iconTop from '@/sub_business/static/prop/icon-top.png'
import iconBottom from '@/sub_business/static/prop/icon-bottom.png'
import unlimitedChatIcon from '@/sub_business/static/prop/unlimited-chat-icon.png'
import speedTopIcon from '@/sub_business/static/prop/speed-top-icon.png'
import exposureRefreshIcon from '@/sub_business/static/prop/exposure-refresh-icon.png'
import magicBombIcon from '@/sub_business/static/prop/magic-bomb-icon.png'
import { payPropUseRecord } from '@/service/payPropUseRecord'

interface UseRecordItem {
  id: string | number
  propCategoryName: string
  durationTime: number
  durationType: number
  endTime: string
}

const value = ref<string[]>([])
const useRecordList = ref<UseRecordItem[]>([])
const titleImgs = [title1, title2, title3, title4]
const titleIcons = [unlimitedChatIcon, speedTopIcon, exposureRefreshIcon, magicBombIcon]
const listArry = ref([{}, {}, {}, {}])
const propId = ref<number | null>(null)

const displayRecordList = computed(() => {
  return useRecordList.value.slice(0, 6)
})
const params = reactive({
  entity: {
    propId: 1,
  },
  orderBy: {},
  page: 1,
  size: 10,
})
const handleChange = async (e: { value: string }): Promise<void> => {
  console.log(e, 'handleChange event')
  const selectedPropId = parseInt(e.value)
  if (!selectedPropId || selectedPropId < 1 || selectedPropId > 4) {
    return
  }
  params.entity.propId = selectedPropId
  propId.value = selectedPropId
  await getuseRecordList()
}

const formatDuration = (durationTime: number, durationType: number): string => {
  const timeUnits = ['小时', '天', '月', '年']
  const unit = timeUnits[durationType] || ''
  return `${durationTime} ${unit}`
}

// 处理点击事件
function handleClick(id: string | number, name: string): void {
  uni.navigateTo({
    url: `/sub_business/pages/prop/redetail?id=${id}&name=${name}`,
  })
}

// 查看更多记录
function moreList(): void {
  if (!propId.value) {
    console.warn('propId is not set')
    return
  }

  uni.navigateTo({
    url: `/sub_business/pages/prop/remorelist?propId=${propId.value}`,
  })
}

// 获取使用记录列表
const getuseRecordList = async (): Promise<void> => {
  try {
    const res = await payPropUseRecord({ ...params })
    console.log(res, 'getuseRecordList response')

    // 处理响应数据
    if (res && res.data && res.data.list) {
      useRecordList.value = res.data.list
    } else {
      console.warn('Invalid response format:', res)
      useRecordList.value = []
    }
  } catch (error) {
    console.error('Failed to fetch use record list:', error)
    useRecordList.value = []
  }
}
onMounted(() => {
  getuseRecordList()
})
</script>

<style lang="scss"></style>
