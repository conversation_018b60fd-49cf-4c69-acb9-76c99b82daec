<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="通用设置"></CustomNavBar>
    <view class="setting">
      <view class="setting-list border-b" @click="handleClearCache">
        <view class="flex-between">
          <view class="list-item-text text-32rpx">储存空间管理</view>
          <view class="subText flex-c">
            <view class="subText">清理缓存{{ cacheSize }}</view>
            <wd-icon
              name="chevron-right"
              size="20px"
              color="#888888"
              class="arrow-right-icon"
            ></wd-icon>
          </view>
        </view>
        <view class="subText">手机可用空间{{ availableSpace }}，易直聘占有手机不足1%储存空间</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getCacheInfo, clearAllCache } from '@/utils/cacheManager'
import { ref, onMounted } from 'vue'

const cacheSize = ref('0B')
const availableSpace = ref('未知')

// 获取缓存信息
const loadCacheInfo = async () => {
  try {
    const cacheInfo = await getCacheInfo()
    cacheSize.value = cacheInfo.totalSize
    availableSpace.value = cacheInfo.availableSpace
  } catch (error) {
    console.error('获取缓存信息失败:', error)
    cacheSize.value = '0B'
    availableSpace.value = '未知'
  }
}

// 清理缓存
const handleClearCache = async () => {
  try {
    uni.showLoading({
      title: '清理中...',
    })

    const success = await clearAllCache()

    uni.hideLoading()

    if (success) {
      uni.showToast({
        title: '清理成功',
        icon: 'success',
      })
      // 重新获取缓存信息
      await loadCacheInfo()
    } else {
      uni.showToast({
        title: '清理失败',
        icon: 'error',
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('清理缓存失败:', error)
    uni.showToast({
      title: '清理失败',
      icon: 'error',
    })
  }
}

onMounted(() => {
  loadCacheInfo()
})
</script>

<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
    .list-item-text {
      color: #333;
    }
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx 10rpx;
}

::v-deep .u-cell__value {
  font-size: 22rpx;
  color: #888888;
}

::v-deep .u-cell__label--large {
  font-size: 22rpx;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}

.setting-text {
  font-size: 22rpx;
  color: #888888;
}
</style>
