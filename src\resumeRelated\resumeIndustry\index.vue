<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="containner bg-img">
    <CustomNavBar title="选择行业"></CustomNavBar>
    <view class="u-searchs">
      <view class="content_flex">
        <view class="content_search">
          <view class="content_search_bg">
            <view class="content_search_left">
              <image src="/static/img/<EMAIL>" mode="aspectFill"></image>
            </view>
            <view class="content_search_right">
              <wd-input no-border placeholder="搜索您想要的内容" v-model="keyword"></wd-input>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主列表容器 -->
    <view class="page-list">
      <!-- 左侧父级分类 -->
      <scroll-view
        scroll-y
        :style="{
          height:
            selectedItems.length > 0
              ? 'calc(100vh - ' + (scrollViewHeight + 30) + 'px)'
              : 'calc(100vh - ' + scrollViewHeight + 'px)',
        }"
        class="page-list-left"
      >
        <view v-for="(parent, pIndex) in list" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parent.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子级及孙子级内容 -->
      <scroll-view
        scroll-y
        :style="{
          height:
            selectedItems.length > 0
              ? 'calc(100vh - ' + (scrollViewHeight + 30) + 'px)'
              : 'calc(100vh - ' + scrollViewHeight + 'px)',
        }"
        class="page-list-right"
      >
        <view>
          <view class="page-list-right-p">
            <view class="page-tag-list">
              <view
                v-for="(grandchild, cIndex) in currentChildren"
                :key="cIndex"
                class="tag-select-r"
                :class="grandchild.active ? 'myStyle-box' : ''"
                @click="changeActive(cIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import industryDataList from '@/utils/json/onlineindustry.json'
import { getCustomBar } from '@/utils/storage'
import { useLoginStore } from '@/store'
const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const list = ref([])
const scrollViewHeight = ref(0)

const currentChildren = computed(() => {
  return list.value[activeFIndex.value]?.childerIndustryData || []
})

const selectedItems = computed({
  get() {
    const items = []
    list.value.forEach((parent) => {
      parent.childerIndustryData &&
        parent.childerIndustryData.forEach((grandchild) => {
          if (grandchild.active) {
            items.push(grandchild)
          }
        })
    })
    return items
  },
  set(value) {
    // 清空所有选中状态
    list.value.forEach((parent) => {
      parent.childerIndustryData &&
        parent.childerIndustryData.forEach((child) => {
          child.active = false
        })
    })

    // 同步新选中的状态
    value.length &&
      value.forEach((item) => {
        item.active = true
      })
  },
})

const activeF = (index) => {
  activeFIndex.value = index
}

const changeActive = (cIndex) => {
  const target = list.value[activeFIndex.value].childerIndustryData[cIndex]
  const obj = {
    industry: list.value[activeFIndex.value].childerIndustryData[cIndex].name,
    industryId: list.value[activeFIndex.value].childerIndustryData[cIndex].code,
  }
  loginStore.setjobObjOnline(obj)
  uni.navigateBack()
}

// 根据存储的数据设置选中状态
const setSelectedFromStore = () => {
  console.log(loginStore.jobObjOnline, 'loginStore.jobObjOnline=======')
  if (!loginStore.jobObjOnline) return

  list.value.forEach((parent) => {
    parent.childerIndustryData &&
      parent.childerIndustryData.forEach((child) => {
        // 检查当前子项是否与存储的数据匹配
        if (child.code === loginStore.jobObjOnline.industryId) {
          child.active = true
          // 找到对应的父级索引，自动滚动到该位置
          const parentIndex = list.value.findIndex(
            (item) =>
              item.childerIndustryData &&
              item.childerIndustryData.some((c) => c.code === child.code),
          )
          if (parentIndex !== -1) {
            activeFIndex.value = parentIndex
          }
        }
      })
  })
}

onLoad(() => {
  scrollViewHeight.value = getCustomBar() + 75
  const industryList = industryDataList.industryData
  industryList.forEach((parent) => {
    parent.childerIndustryData &&
      parent.childerIndustryData.forEach((child: AnyObject) => {
        child.active = false
      })
  })
  list.value = industryList
  setSelectedFromStore() // 调用反显方法
})
</script>

<style lang="scss">
/* 新增顶部标签样式 */
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}
::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #333333 !important;
}
::v-deep .wd-input__inner {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #333333 !important;
}
.qr-btn {
  width: 100rpx;
  padding: 5rpx 0rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 10rpx;
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 0rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #333333;
      background: #ffffff;
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('../img/Mask_group(2).png');
}

.page-list-right-title {
  padding-bottom: 25rpx;
  font-weight: bold;
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
}

.activeBg {
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #f5f4f4;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 230rpx;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      // 相邻元素的圆角
      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 26rpx;
    color: #333;
    text-align: center;
    background: #ffffff;
    border-radius: 10rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
