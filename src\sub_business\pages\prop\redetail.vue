<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '使用记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          custom-class="px-25rpx"
          left-arrow
          safe-area-inset-top
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">使用记录</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="m-[40rpx]">
      <!-- 卡片 -->
      <view class="m-t-40rpx">
        <view class="bg-#fff rounded-20rpx shadow-[0px_4px_13.5px_0px_rgba(0,0,0,0.1)] p-40rpx">
          <view class="flex items-center justify-between lh-10">
            <view class="text-28rpx c-#000">道具名称</view>
            <view class="flex items-center">
              <view class="text-28rpx c-#555">{{ name }}</view>
            </view>
          </view>
          <view v-if="detailObj?.startTime" class="flex items-center justify-between lh-10">
            <view class="text-28rpx c-#000">使用时间</view>
            <view class="flex items-center">
              <view class="text-28rpx c-#555">{{ detailObj.startTime }}</view>
            </view>
          </view>
          <view v-if="detailObj?.integer" class="flex items-center justify-between lh-10">
            <view class="text-28rpx c-#000">结束时间</view>
            <view class="flex items-center">
              <view class="text-28rpx c-#555">{{ detailObj.integer }}</view>
            </view>
          </view>
          <view v-if="detailObj?.targetName" class="flex items-center justify-between lh-10">
            <view class="text-28rpx c-#000">岗位名称</view>
            <view class="flex items-center">
              <view class="text-28rpx c-#555">{{ detailObj.targetName }}</view>
              <!-- <wd-icon
                name="chevron-right"
                size="22px"
                v-if="detailObj.targetName"
                color="#555"
              ></wd-icon> -->
            </view>
          </view>
        </view>
      </view>
      <!-- 图表 -->
      <view class="m-t-20rpx">
        <Charts :targetId="targetId" />
      </view>
      <!-- 信息 -->
      <view class="m-t-40rpx flex items-center flex-wrap w-100 justify-between box-border">
        <view
          v-for="item in cardList"
          :key="item.name"
          class="bg-#fff rounded-20rpx border-1rpx border-dashed border-#B9B9B9 w-320rpx p-20rpx relative m-b-40rpx"
        >
          <view class="text-28rpx c-#000 p-b-10rpx flex items-center">
            <view class="text-28rpx c-#000 m-r-6rpx">{{ item.name }}</view>
            <view class="text-22rpx c-#888">近一周</view>
          </view>
          <view class="text-36rpx c-#2E6CFA">{{ item.value }}</view>
          <view v-if="item.value !== 0" class="absolute bottom-20rpx right-40rpx">
            <wd-img :src="shangsheng" height="21" width="21"></wd-img>
          </view>
        </view>
      </view>
      <!-- 卡片 -->
      <view class="m-t-20rpx">
        <view class="bg-#fff rounded-20rpx shadow-[0px_4px_13.5px_0px_rgba(0,0,0,0.1)] p-40rpx">
          <view v-if="detailObj?.userCouponId" class="flex items-center justify-between lh-10">
            <view class="text-28rpx c-#000">优惠券兑换</view>
          </view>
          <view v-else>
            <view v-if="detailObj?.actualAmount" class="flex items-center justify-between lh-10">
              <view class="text-28rpx c-#000">价格</view>
              <view class="flex items-center">
                <view class="text-28rpx c-#555">{{ detailObj.actualAmount / 1000 }}元</view>
              </view>
            </view>
            <view v-if="detailObj?.startTime" class="flex items-center justify-between lh-10">
              <view class="text-28rpx c-#000">时间</view>
              <view class="flex items-center">
                <view class="text-28rpx c-#555">{{ detailObj.startTime }}</view>
              </view>
            </view>
            <view v-if="detailObj?.outTradeNo" class="flex items-center justify-between lh-10">
              <view class="text-28rpx c-#000">订单号</view>
              <view class="flex items-center">
                <view class="text-28rpx c-#555">{{ detailObj.outTradeNo }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import Charts from '@/sub_business/components/charts.vue'
import { payPropUseDetail, payPropUseRecordTable } from '@/service/payPropUseRecord'
import shangsheng from '@/sub_business/static/prop/shangsheng.png'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const id = ref(null)
const detailObj = ref(null)
const name = ref(null)
const targetId = ref(null) // 岗位ID
const currentTime = ref<string>('')
const pastTime = ref<any>('')

// 日期格式化函数
const formatDate = (date: Date): string => {
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}

const list1 = ref([
  {
    name: '道具名称',
    value: '道具刷新卡 - 周卡',
  },
  {
    name: '使用时间',
    value: '2025-07-14 10:00:00',
  },
  {
    name: '结束时间',
    value: '2025-07-14 10:00:00',
  },
  {
    name: '岗位名称',
    value: '计算机',
  },
])
const list2 = ref([
  {
    name: '价格',
    value: '￥10000',
  },
  {
    name: '时间',
    value: '2025-07-14 10:00:00',
  },
  {
    name: '订单号',
    value: '1234567890',
  },
])
const cardList = ref([
  {
    name: '看过我的',
    value: 0,
  },
  {
    name: '有交换的',
    value: 0,
  },

  {
    name: '沟通过的',
    value: 0,
  },
  {
    name: '收获简历',
    value: 0,
  },
])
const queryList = async () => {
  const res: any = await payPropUseDetail({ id: id.value })
  console.log(res, 'res=================')
  if (res.code === 0) {
    detailObj.value = res.data
    targetId.value = res.data.targetId || null
  }
}

// 获取表格数据
const queryTable = async () => {
  const res: any = await payPropUseRecordTable({
    startTime: pastTime.value,
    endTime: currentTime.value,
    positionId: targetId.value,
  })
  if (res.code === 0) {
    cardList.value[0].value = res.data.imSession || 0
    cardList.value[1].value = res.data.exchangeTotal || 0
    cardList.value[2].value = res.data.seeMe || 0
    cardList.value[3].value = res.data.meetings || 0
  }
}

function handleClickLeft() {
  uni.navigateBack()
}

onLoad(async (options) => {
  await uni.$onLaunched
  id.value = options.id
  name.value = options.name
  const today = new Date()
  currentTime.value = formatDate(today)
  const pastDate = new Date()
  pastDate.setDate(today.getDate() - 7)
  pastTime.value = formatDate(pastDate)

  await queryList()
  await queryTable()
})
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
</script>

<style lang="scss" scoped></style>
