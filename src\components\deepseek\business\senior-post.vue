<template>
  <z-paging
    ref="pagingRef"
    :fixed="false"
    v-model="pageData"
    :paging-style="pageStyle"
    paging-class="senior-post-paging"
    @query="queryList"
    refresher-theme-style="white"
    :empty-view-super-style="{ background: '#ffffff', borderRadius: '30rpx 30rpx 0 0' }"
    :loading-more-custom-style="{ background: '#ffffff' }"
    :show-loading-more-no-more-view="false"
    auto-clean-list-when-reload
    auto-scroll-to-top-when-reload
    show-refresher-when-reload
  >
    <template #empty>
      <view class="flex flex-col items-center justify-center">
        <wd-img :src="releasePostEmpty" width="412rpx" height="412rpx" />
        <text class="c-#000000 text-28rpx">快去发布岗位，候选人马上就到</text>
      </view>
    </template>
    <view class="p-[44rpx_30rpx] h-full flex flex-col gap-40rpx" v-if="pageData.length">
      <personal-list
        v-for="(item, key) in pageData"
        :key="key"
        :list="item"
        :position="releaseSeniorPostActivePost"
      />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { hrResumeResumeUserSeniorList } from '@/service/hrResume'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import type { hrResumeSeniorPostModelInt } from '@/service/hrResume/types'
import personalList from '@/components/common/personal-list.vue'
import releasePostEmpty from '@/static/home/<USER>/release-post-empty.png'

const { releaseSeniorPostActivePost, releaseIsHaveSeniorPost } = useReleasePost()
const { pagingRef, pageInfo, pageSetInfo, pageData, pageStyle } =
  usePaging<hrIndexResumeUserListInt>({
    style: {
      background: '#383838',
    },
  })
const seniorPostModel = ref<hrResumeSeniorPostModelInt>({})

async function fetchHrResumeResumeUserSeniorList() {
  if (!releaseIsHaveSeniorPost.value) {
    pagingRef.value.completeByTotal([], 0)
    return
  }
  const { data } = await hrResumeResumeUserSeniorList({
    entity: {
      positionInfoId: releaseSeniorPostActivePost.value.id,
      ...seniorPostModel.value,
    },
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}
function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchHrResumeResumeUserSeniorList()
}
async function reload() {
  pagingRef.value.reload()
}
function seniorPostReload(model: hrResumeSeniorPostModelInt) {
  seniorPostModel.value = model
  reload()
}
defineExpose({
  seniorPostReload,
  reload,
})
</script>

<style lang="scss" scoped>
.senior-post-paging {
  :deep(.zp-paging-container) {
    background: #ffffff;
    border-radius: 30rpx 30rpx 0 0;
    .zp-paging-container-content {
      height: auto !important;
    }
  }
  :deep(.zp-view-super) {
    margin: 0 !important;
  }
}
</style>
