import { ref } from 'vue'
import { defineStore } from 'pinia'
import { currentValueOf, timeDiffValueOf, valueOfToDate } from '@/utils/days'

type AddressInfo = {
  lat: number
  lng: number
}
export const useAddressStore = defineStore(
  'address',
  () => {
    const addressInfo: AddressInfo = {
      lat: 0,
      lng: 0,
    }
    return {
      addressInfo,
    }
  },
  {
    persist: true,
  },
)
