<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘简历用户协议</text>
      </view>
      <view class="protocol-meta">
        <text class="version">版本：ver202505</text>
        <text class="date">生效日期：2025年05年24日</text>
      </view>
      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-icon"></text>
          <text class="list-text">{{ item }}</text>
        </view>
      </view>
      <text class="welcome-text">
        尊敬的用户，欢迎您使用易直聘简历服务！\n本协议由您与重庆中誉易职网络信息科技有限公司（以下简称"我们"）就使用易直聘简历服务签订的合法文件。为保障用户权益并提供优质服务体验，在使用相关功能前，请务必仔细阅读《易直聘简历用户协议》（下称"本协议"）。用户完成易直聘简历的注册或登录操作，即代表您已确认并同意遵守本协议的全部条款。
      </text>
      <view
        class="section"
        v-for="(section, index) in sections"
        :key="'section' + index"
        :id="'section-' + index"
      >
        <view class="section-title article-title">{{ section.title }}</view>
        <text class="section-content">{{ section.content }}</text>
      </view>
      <view class="footer">
        <text class="footer-text">生效日期：2025年05年24日</text>
        <text class="footer-text">版本：ver202505</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

const contentList = ref([
  '一、用户的注册与登录',
  '二、服务说明',
  '三、用户的使用义务',
  '四、知识产权',
  '五、隐私保护',
  '六、有限责任条款',
  '七、违约责任和违规处置',
  '八、协议变更和通知',
  '九、协议的中止或终止',
  '十、法律适用和管辖',
  '十一、通知与送达',
  '十二、其他',
  '十三、定义',
])

const sections = ref([
  {
    title: '一、用户的注册与登录',
    content: `1. 使用易直聘简历服务前，需先完成账号注册并遵守平台规则。如未注册或账号状态异常，将无法正常登录简历系统。
2.注册及登录过程中需提供本人有效手机号码，平台将通过发送短信验证码的方式验证身份的真实性。平台有权根据法律法规、监管要求或平台风险管理需要，要求用户进一步补充实名信息并进行身份验证，包括但不限于上传有效身份证件、进行人脸识别或实名认证等。用户未能如实、及时配合的，平台有权拒绝提供部分或全部服务。用户应保证所提供信息的真实性、准确性、完整性，如因信息不实导致平台或第三方损失，用户应承担全部责任。
3.用户确认，在开始使用易直聘简历服务前须年满16周岁，且具备与使用行为相匹配的民事行为能力。平台有权通过技术手段（如身份认证、数据比对等）核查用户年龄及民事行为能力，用户应配合提供必要证明材料。如发现用户不符合要求，平台有权暂停或终止服务。因用户未如实提供身份信息或不具备相应民事行为能力导致的后果，由用户及其监护人依法承担相应责任。
4. 用户须依据本协议及《易直聘用户协议》妥善管理并维护账号安全。`,
  },
  {
    title: '二、 服务说明',
    content: `1.易直聘简历通过互联网，向用户提供线上及线下相关网络服务。用户需自行准备互联网接入设备（包括计算机或移动设备、调制解调器等终端装置），并承担相关网络接入费用。
2.针对部分网络服务项目，易直聘简历可能收取相应费用，相关页面将进行显著提示。用户拒绝支付费用将无法使用对应服务。付费业务将另行制定专门服务条款规范权利义务关系，用户应在购买前通过弹窗确认方式阅读并同意该条款，购买即视为同意该条款内容。平台有权根据市场变化调整服务收费标准，并在显著位置明示付费服务有效期、服务内容与退款条件。因用户原因中止、解除、注销、账户被封禁等情形，费用原则上不予退还。如有其他特别退款约定，以平台页面或服务协议具体约定为准。
3.所有免费与付费服务均设有固定有效期，有效期届满后服务自动终止且不可暂停或延期，但因用户主动申请且经平台书面同意的情形除外。除特别约定外，付费服务费用不予退还。用户明确并同意，其购买的易直聘简历付费服务属于数字化商品，用户自愿放弃基于《中华人民共和国消费者权益保护法》第25条关于网络购买商品七日无理由退货规则的适用主张。 因用户自身原因导致服务无法使用、账号被封禁、注销或终止的，已支付费用不予退还，平台不承担任何补偿责任。但若因平台原因未实际提供付费服务或服务存在瑕疵导致无法正常使用，用户有权要求相应退款。
4.平台保留通过邮件、短信、电话及其他方式向用户发送商业广告及活动信息的权利，但须事先获得用户的明示同意。用户有权随时拒绝接收商业广告及活动信息，平台应在每次推送时提供便捷的退订方式。用户可通过账户设置页面的消息管理模块或每次推送信息中提供的退订方式进行退订，退订后平台应及时停止向用户推送相关信息。退订不影响用户接收系统通知及重要公告。
5. 为优化服务体验，用户同意平台将其在平台上已公开的简历信息（不包括敏感个人信息）共享至关联平台及经用户授权的第三方合作平台（具体共享范围和对象以《易直聘简历隐私政策》及用户授权为准。`,
  },
  {
    title: '三、用户的使用义务',
    content: `1. 用户在使用易直聘简历服务时，应当严格遵守现行法律法规，禁止借助该平台制作、传播、复制、发布或散布任何违反法律规范的内容，包括但不限于以下情形：
1.1 违背宪法确立的根本原则；
1.2危害国家主权安全，泄露国家机密，破坏政权稳定及国家统一；
1.3 损害国家形象与利益；
1.4 煽动民族对立与歧视，破坏民族团结；
1.5 违反宗教政策，传播邪教及封建迷信思想；
1.6散布不实信息，扰乱社会秩序及稳定；
1.7传播淫秽色情、赌博暴力、恐怖犯罪等违法内容；
1.8 侵害他人名誉权、隐私权等合法权益；
1.9包含虚假欺骗、侵权胁迫、骚扰诽谤等违背公序良俗的信息；
1.10其他违反中国法律法规及具有法律效力规范性文件的内容。

2. 用户承诺在使用本服务过程中，不得实施下列违法违规或损害平台及他人权益的行为，如有违反，平台有权采取包括但不限于警告、限制、暂停、终止服务、删除数据、追究法律责任等措施，且无需承担任何责任。
2.1发布违法信息或损害平台商誉的内容；
2.2 采用非正当手段或违反诚信原则使用本服务，包括但不限于通过技术漏洞、绕过风控措施、伪造信息等方式使用本服务。
2.3 出于商业经营目的进行非授权使用，仅限于个人求职用途，禁止将简历信息用于人才中介、猎头服务、数据爬取等第三方商业场景；
2.4 未经书面授权进行账号交易、共享或提供自动登录凭证；
2.5 上传侵犯知识产权或隐私权的资料；
2.6 传播计算机病毒等恶意程序；
2.7 干扰平台系统及功能的正常运行；
2.8 非法获取、篡改或使用用户数据；
2.9 违反注册认证规则及使用限制；
2.10 未经许可使用第三方工具接入服务；
2.11 通过非正常手段获取平台数据用于商业用途；
2.12 对平台数据进行逆向工程或超出授权范围使用，用户承诺不得使用自动化脚本、爬虫程序或其他技术手段批量获取平台数据；
2.13 引发平台与第三方纠纷的行为；
2.14 违反本协议及相关隐私政策的其他行为。
3. 平台对涉嫌违规内容具有独立判断和处置权，包括但不限于删除、屏蔽相关信息，暂停或终止部分及全部服务功能，且无需事先通知用户或承担任何责任。`,
  },
  {
    title: '四、知识产权',
    content: `1. 您通过易直聘简历上传或填写的所有信息均须为原创作品或已取得合法授权（包括转授权），且不得侵犯任何第三方的合法权益，相关内容的著作权归您或原始权利人所有。若因您上传、发布或分享内容导致平台涉诉或被监管调查，您应配合平台调查、举证及维权。如第三方提出知识产权异议，我们有权直接删除争议内容、暂停相关账号或采取技术措施限制其使用，并追究相关责任。若因此造成我方或第三方损失，您需承担全部赔偿义务，包括但不限于平台因此支出的律师费、诉讼费、公证费、行政处罚款等。
2. 完成易直聘简历注册后，除特别约定外，您确认将平台上传/发布的所有内容在全球范围内、在本协议有效期内、与本平台服务直接相关的合理用途下，非独家地授予我们及关联企业信息网络传播权、展览权、翻译权、复制权、商业推广权及其再许可权，平台有权据此对内容进行技术处理、排版调整、内容优化等操作。您有权随时通过平台公示渠道撤回该授权。自您撤回授权之日起，平台应在合理期限内停止对该内容的新展示和新推广，但对于授权期内已生成的内容备份、缓存或由于技术原因无法即时删除的部分，平台应在技术可行范围内及时处理。
3. 使用易直聘简历服务时，您将获得非独占、不可转授、可撤销的使用许可，仅限于求职目的使用平台简历模板的版式设计、文字内容及内置素材。
4. 易直聘简历相关知识产权（包括但不限于本平台开发、拥有或被合法授权的软件技术、网页设计、商标标识、排版方案等）均归属于我方所有。未经明确书面授权，用户不得以任何形式对上述内容进行复制、使用或侵犯相关权利。
用户不得就平台提供的版式设计、交互逻辑、数据算法等要素主张衍生作品著作权或专利申请权。未经平台明确书面许可，任何机构或个人不得擅自使用、展示、注册或以其他方式主张权利，亦不得对平台界面设计、功能模块进行反向工程、反编译、反汇编等行为。若违反上述规定，应按法律规定承担相应责任，并补偿给平台造成的损失。
5. 如发现他人存在违反本协议或侵害知识产权的行为，您可通过平台举报通道反映，我们将依据规则采取相应处理措施。`,
  },
  {
    title: '五、隐私保护',
    content: `我们严格依照相关法律法规对用户的个人信息及隐私数据进行保护，具体内容请参考《易直聘简历隐私政策》中的相关规定。隐私政策为本协议不可分割的组成部分，对个人信息的处理、授权、共享、查询和删除权利等事项，对双方具有同等法律效力。用户同意并授权平台按照隐私政策收集、处理、存储、使用及共享其个人信息，且用户已充分知悉并同意相关规则。如隐私政策更新并与本协议冲突时，以隐私政策为准。`,
  },
  {
    title: '六、有限责任条款',
    content: `1.易直聘简历承诺为用户提供安全、及时、准确的高质量服务，但不对服务效果、时效性、安全性及准确性作任何保证，亦不承诺服务永不中断。除另有约定外，因非因本公司故意或重大过失导致的服务不可用或未达预期而产生的直接或间接损失，本公司不承担相关责任。若因本公司故意或重大过失造成用户损失的，本公司应依法承担相应责任。
2.平台展示的第三方广告信息、链接及资讯等内容（如有），其准确性、合法性或可靠性由广告主负责。平台对第三方内容、服务、产品不作任何明示或暗示的保证，用户因使用第三方内容、服务、产品产生的任何争议、损失或责任，均由用户与第三方自行解决，平台不承担任何责任。同时，平台仅作为信息发布或技术中介，不对广告主或第三方的承诺承担担保责任，用户应自行判断并自担风险。
3. 用户上传的证件、影像、图文资料等内容，虽经平台基于现有技术条件采取必要的自动化过滤措施，仍无法确保其准确性、合法性及可靠性。若相关内容违法、侵权，或被第三方主张权利，由内容提供方自行承担全部责任。
4. 用户须自行判断并承担使用本平台所获信息资料的风险，包括但不限于对内容正确性、完整性或实用性的误判风险。对于因依赖平台信息导致的任何直接或间接损失，本公司依法不承担责任。
5. 用户下载或获取任何资料应自担风险，因下载行为导致的设备损坏、数据丢失、病毒感染或信息丢失等后果，本公司不承担任何技术、经济及法律责任。
6. 用户知悉并同意，因包括但不限于政府行为、自然灾害、公共卫生事件、电力供应中断、网络故障、基础电信服务异常、黑客攻击、战争等导致的服务中断或缺陷，平台将在能力范围内尽力减少因上述行为造成的损失，平台依法免除相关责任，不保证服务的持续性、安全性及绝对准确性。
7. 平台保留对产品功能、服务内容、服务条款及界面设计进行任何合理范围内调整、优化、修订或暂停的权利。用户承诺对因合理调整造成的服务波动予以充分理解，我们将最大程度降低调整对用户体验的影响。
8. 下列情形导致服务中止或终止的，本公司不承担法律责任：
8.1 实施系统维护（包括但不限于升级、修复、数据迁移、服务器迁移）等必要技术操作导致的临时性服务中断；
8.2 互联网通讯服务商故障引发的网络接入异常；
8.3 遭遇非法网络攻击造成的服务器运行异常；
8.4 因网络特性导致的区域性临时访问障碍，包括网络拥塞、短暂中断等现象。`,
  },
  {
    title: '七、违约责任和违规处置',
    content: `1.若您存在违反本协议或相关法律法规的情形，我们有权根据独立判断随时采取包括但不限于警告、功能限制、账户冻结、服务终止及永久封禁等措施，具体处置措施与违规行为严重程度相匹配。您明确知悉并同意，平台不承担用户数据的长期保管义务。被删除的内容将无法恢复，用户应自行对重要数据进行备份，由此产生的相关责任及后果均由用户自行承担。
2.您充分理解并同意，平台应依法承担相应责任，并有权将处理决定进行公示（公示内容不得包含用户身份证号、银行账户等敏感信息）。平台保留恢复账户功能的裁量权，并有权拒绝向违规主体继续提供服务。对于涉嫌违法或犯罪的行为，平台依法有权采取证据保全措施，并根据法律要求向监管部门报告或配合司法机关调查。
3.若因您违反协议或侵害第三方权益导致投诉或诉讼，平台将在收到有效权利主张后对涉嫌违规内容进行删除处理。用户因同一违约行为被多次投诉的，平台有权累计计算违约次数并升级处置措施。 收到平台书面通知之日起10个工作日内，您须自行应对所有权利主张并承担相应法律责任。因您的不当行为致使平台承担赔偿责任或受到行政处罚的，您应承担全部赔偿责任。用户因违约行为需承担赔偿责任的，平台有权从用户账户余额中直接扣除相应款项，不足部分平台保留追偿权利。您应补偿平台因此遭受的直接损失及合理维权费用（包括律师费、诉讼费、公证费及因追偿发生的全部合理费用等）。`,
  },
  {
    title: '八、协议变更和通知',
    content: `根据产品功能升级、运营调整或法律政策变化的需要，平台有权对本协议条款进行更新或调整。
协议条款变更后，平台将通过平台内公告、系统通知或页面提示等方式予以发布，并通过用户注册手机号推送关键变更内容。
若您对条款修改有异议，应在变更生效前停止使用平台服务，并可申请删除数据或注销账号。选择注销账号的用户，其未消耗的付费权益将一并清除。
用户注销账号或终止服务后，平台有权不再保留用户相关数据或内容，也无义务向用户或第三方返还或导出数据，除非依法律法规规定必须保留。
为符合法律法规、风险控制、维权取证或政策监管等要求，平台可在合理期限内继续保存部分用户数据，无需承担用户损失或被主张删除的责任。
用户继续使用平台服务，则视为接受修订后的协议条款。如涉及用户重大权益变更，且未明示通知或未取得用户同意，则该等变更对用户不发生效力，直至取得用户明确同意。
用户注销账户后，平台应在30日内完成个人数据删除或匿名化处理，但依法需留存的身份验证信息除外。`,
  },
  {
    title: '九、协议的中止或终止',
    content: `在以下情形中，平台有权中止或终止向您提供服务：
1、根据相关法律法规、政策规定或监管要求，平台需暂停或停止相关服务；
2. 因业务调整需要，我们将提前通过公告、系统通知、邮件或短信等方式通知服务的暂停或终止。但在紧急情况下（如政策变动、平台安全等），平台可临时暂停服务，并在事后补发通知。
3. 若您存在违反法律法规或协议约定的行为，我们有权按照协议规定暂停或终止服务。
4.服务终止后，用户已上传的非公开信息将保留30日，期满后自动清除，平台不承担长期存储义务。`,
  },
  {
    title: '十、法律适用和管辖',
    content: `1. 本协议的生效条件、履行方式、条款解释及争议处理均适用中华人民共和国现行有效之法律。
2. 若因本协议引发争议，双方应优先通过友好协商解决；若协商未果，任一方均有权向公司所在地重庆市九龙坡区人民法院提起诉讼。`,
  },
  {
    title: '十一、通知与送达',
    content: `用户确认其注册手机号、电子邮箱为有效送达地址，平台发送即视为送达。`,
  },
  {
    title: '十二、其他',
    content: `1. 本协议条款标题仅为便于阅读而设置，不可作为条款释义的依据。本协议所有条款的最终解释权归平台所有，若对条款内容、权利义务产生疑问，由平台负责释义与适用。
2. 本协议与《易直聘用户协议》共同组成法律文件，作为您与平台合作的规范依据。
3. 若本协议条款与中华人民共和国法律存在冲突导致部分条款无效或无法执行，相关条款应在法律允许范围内进行最大程度的有效性解释，且不影响其他条款效力，缔约双方应继续履行其他条款义务。`,
  },
  {
    title: '十三、定义',
    content: `1. "用户"或"您"特指通过注册流程使用重庆中誉易职网络信息科技有限公司运营产品及服务的自然人主体。
2. "易直聘简历"涵盖易直聘简历网页平台及其微信小程序应用服务模块。
3. "易直聘平台"指由重庆中誉易职网络信息科技有限公司及其关联企业运营的网页、移动应用、小程序。
4. "易直聘账号"系指能够登录并操作易直聘平台服务体系的通用账号系统。`,
  },
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  console.log('点击定位到第', index, '个章节')

  const sectionId = `section-${index}`
  console.log('目标章节ID:', sectionId)

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      console.log('目标元素位置:', targetRect)

      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop
            console.log('计算滚动位置:', targetScrollTop)

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {
                    console.log('uni.pageScrollTo 成功')
                  },
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.protocol-meta {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.welcome-text {
  display: block;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}
.section-content {
  display: block;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}
.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.agreement-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
  margin-bottom: 120rpx;

  .header {
    margin-bottom: 20rpx;
    text-align: center;

    .title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .version,
    .date {
      display: block;
      font-size: 24rpx;
      line-height: 1.6;
      color: #999;
    }
  }

  .content-list {
    padding: 20rpx 0rpx;
    margin-bottom: 20rpx;
    border-radius: 12rpx;

    .content-title {
      display: block;
      margin-bottom: 15rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }

    .list-item {
      display: flex;
      align-items: flex-start;
      padding: 10rpx;
      margin-bottom: 10rpx;
      cursor: pointer;
      border-radius: 8rpx;

      .list-icon {
        margin-right: 10rpx;
        font-size: 24rpx;
      }

      .list-text {
        flex: 1;
        font-size: 26rpx;
        color: #007aff;
      }
    }
  }

  .content {
    flex: 1;
    padding: 20rpx;
    margin-bottom: 120rpx;
    border-radius: 12rpx;

    .section {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        margin-bottom: 15rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
      }
    }
  }
  .footer {
    margin-top: 40rpx;
    text-align: right;
  }

  .footer-text {
    display: block;
    margin-bottom: 10rpx;
    font-size: 24rpx;
    color: #666666;
  }

  .action-bar {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100rpx;
    padding: 0 20rpx;

    .btn {
      width: 45%;
      height: 80rpx;
      margin: 0;
      font-size: 28rpx;
      line-height: 80rpx;
      border-radius: 40rpx;

      &.disagree {
        color: #666;
      }

      &.agree {
        color: #fff;
      }
    }
  }
}
</style>
