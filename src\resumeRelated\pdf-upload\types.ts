// PDF上传相关类型定义

export interface PdfUploadConfig {
  maxFileSize: number
  allowedTypes: string[]
  uploadUrl: string
  headers: Record<string, string>
  timeout: number
  additionalData?: Record<string, any>
}

export interface UploadResult {
  success: boolean
  data?: any
  error?: string
}

export interface WebViewMessage {
  type: string
  data?: any
}

export interface UsePdfUploadOptions {
  onLoadingChange?: (loading: boolean) => void
  onErrorChange?: (error: string) => void
  onUrlChange?: (url: string) => void
  onUploadSuccess?: (result: UploadResult) => void
  onUploadError?: (error: string) => void
}
