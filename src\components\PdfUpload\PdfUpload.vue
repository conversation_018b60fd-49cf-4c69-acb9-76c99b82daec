<template>
  <view class="pdf-upload-component">
    <!-- 上传按钮 -->
    <view class="upload-btn" @click="handleUpload">
      <wd-img :width="20" :height="20" :src="addpdf" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import addpdf from '@/resumeRelated/img/addpdf.png'

// Props
interface PdfUploadConfig {
  uploadUrl: string
  maxFileSize: number
  headers: Record<string, string>
  timeout: number
  additionalData?: Record<string, any>
}

interface Props {
  config: PdfUploadConfig
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  success: [result: any]
  error: [error: string]
  progress: [progress: number]
}>()

// 响应式数据
const uploading = ref(false)

// 处理上传
const handleUpload = () => {
  if (uploading.value) {
    return
  }

  uploading.value = true

  // 跳转到PDF上传页面
  uni.navigateTo({
    url: '/resumeRelated/pdf-upload/pdf-upload',
    success: () => {
      // 传递配置
      const config = {
        maxFileSize: props.config.maxFileSize,
        allowedTypes: ['application/pdf'],
        uploadUrl: props.config.uploadUrl,
        headers: props.config.headers,
        timeout: props.config.timeout,
        additionalData: props.config.additionalData,
      }

      // 监听上传结果
      uni.$on('pdfUploadResult', (result: any) => {
        uploading.value = false
        if (result.success) {
          emit('success', result)
        } else {
          emit('error', result.error || '上传失败')
        }
        // 移除监听器
        uni.$off('pdfUploadResult')
      })

      // 监听上传进度
      uni.$on('pdfUploadProgress', (progress: number) => {
        emit('progress', progress)
      })

      // 发送配置到PDF上传页面
      uni.$emit('pdfUploadConfig', config)
    },
    fail: () => {
      uploading.value = false
      emit('error', '页面跳转失败')
    },
  })
}
</script>

<style lang="scss" scoped>
.pdf-upload-component {
  display: inline-block;
}

.upload-btn {
  display: inline-block;
  cursor: pointer;
  opacity: 1;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.7;
  }
}
</style>
