<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="公司照片"></CustomNavBar>
    </template>
    <view class="corporateName">
      <view class="flex items-center justify-center">
        <wd-upload
          :file-list="fileList"
          image-mode="aspectFill"
          :limit="20"
          :header="header"
          :action="baseUrl"
          @success="successFun"
          :before-upload="beforeUpload"
          :before-remove="beforeRemove"
          accept="image"
          custom-class="custom-class"
        ></wd-upload>
      </view>
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { quickHandlers } from '@/utils/compressImage'
import { hrCompanyStyleAdd, hrCompanyStyleList, hrCompanyStyleDel } from '@/service/hrCompany'
const { getToken, userIntel } = useUserInfo()
const message = useMessage()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
// 参数
const fileList = ref([])
// 图片地址
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// before-upload 处理函数 - 使用预设的头像压缩配置
const beforeUpload = quickHandlers.highQuality()
// 图片上传成功,
const successFun = ({ fileList }) => {
  const res = JSON.parse(fileList[fileList.length - 1].response)
  if (res.code === 0) {
    getimgInfo(res.data[0].fileId)
  }
}
// 删除图片
const beforeRemove = async ({ file, fileList, resolve }) => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除企业风采吗?',
    })
    .then(async () => {
      hrCompanyStyleDel({ id: file.id }).then((res) => {
        if (res.code === 0) {
          uni.$emit('welfare-updated')
          getCompanyStyleList()
          resolve(true)
        }
      })
    })
}
// 上传公司风采
const getimgInfo = async (fileId: any) => {
  console.log(fileId, 'fileId===')
  const res: any = await hrCompanyStyleAdd({ attachId: fileId, attachType: 0 })
  if (res.code === 0) {
    uni.$emit('welfare-updated')
    await getCompanyStyleList()
    console.log(res)
  }
}
// 查询相册列表
const getCompanyStyleList = async () => {
  console.log(userIntel.value?.companyId, 'userIntel.value?.companyId')
  const res: any = await hrCompanyStyleList({ id: userIntel.value?.companyId })
  if (res.code === 0) {
    fileList.value = res.data.map((item) => {
      return {
        url: item.attachIdUrl,
        attachId: item.attachId,
        name: 'tupian',
        attachType: item.attachType,
        id: item.id,
      }
    })
  }
}
onLoad(async (options) => {
  await nextTick()
  await getCompanyStyleList()
})
</script>

<style scoped lang="scss">
.corporateName {
  margin: 40rpx 40rpx 40rpx 60rpx;
}
::v-deep .custom-class {
  margin: 0rpx !important;
  background: transparent !important;
}

:deep(.wd-upload__evoke) {
  width: 190rpx;
  height: 190rpx;
  background: #fff;
  border-radius: 10rpx !important;
  box-shadow: 5px 10px 30px 0px rgba(0, 0, 0, 0.05);
}
:deep(.wd-upload__preview) {
  width: 190rpx;
  height: 190rpx;
}
:deep(.wd-upload__evoke-num) {
  display: none;
}
</style>
