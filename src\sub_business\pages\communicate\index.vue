<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="沟通过"></CustomNavBar>
    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    >
      <view>
        <view class="pageList">
          <view class="page_list">
            <view v-for="(group, groupIndex) in pageData" :key="groupIndex">
              <view class="content-data">
                {{ group.yearMonth }}
              </view>
              <mylist
                :list="item"
                :position="{}"
                v-for="(item, i) in group.items"
                :key="i"
                class="border-b border-#E0E0E0 border-b-solid"
              />
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { ditchThroughList } from '@/service/hrBiographical'
import { getCustomBar } from '@/utils/storage'
import imgPrent from '@/resumeRelated/img/imgPrent.png'
import { numberTokw } from '@/utils/common'
import mylist from '@/sub_business/components/mylist.vue'
const customBar = ref(null)
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    marginTop: '40rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
  },
})
const params = reactive({
  entity: {},
  orderBy: {},
  size: pageInfo.size,
  page: pageInfo.page,
})

const groupByYearMonth = (data: any[]) => {
  if (!data || !Array.isArray(data)) return []

  const groups: Record<string, any[]> = {}

  data.forEach((item) => {
    if (!item.refreshTime) return

    const date = new Date(item.refreshTime)
    const year = date.getFullYear()
    const month = date.getMonth() + 1
    const yearMonth = `${year}年${month}月`

    if (!groups[yearMonth]) {
      groups[yearMonth] = []
    }
    groups[yearMonth].push(item)
  })

  // Sort groups by date (newest first)
  const sortedGroups = Object.keys(groups).sort((a, b) => {
    const dateA = new Date(a.replace('年', '-').replace('月', ''))
    const dateB = new Date(b.replace('年', '-').replace('月', ''))
    return dateB.getTime() - dateA.getTime()
  })

  return sortedGroups.map((key) => ({
    yearMonth: key,
    items: groups[key],
  }))
}

const processListData = (list: any[]) => {
  if (!list) return []

  return list.map((item) => {
    return {
      ...item,
      positionKey: (() => {
        // 处理positionKey，确保正确分割
        let positionKeyValue = item.positionKey

        // 如果是数组，直接使用
        if (Array.isArray(positionKeyValue)) {
          return positionKeyValue.slice(0, 2)
        }

        // 如果是字符串，按逗号分割
        if (typeof positionKeyValue === 'string') {
          // 移除可能的空格
          positionKeyValue = positionKeyValue.trim()
          if (positionKeyValue) {
            return positionKeyValue
              .split(',')
              .map((item) => item.trim())
              .slice(0, 2)
          }
        }

        // 其他情况返回空数组
        return []
      })(),
      workSalaryBegin: item.workSalaryBegin === 0 ? '面议' : numberTokw(item.workSalaryBegin + ''),
      workSalaryEnd: item.workSalaryEnd === 0 ? '' : numberTokw(item.workSalaryEnd + ''),
      distanceMeters: item.distanceMeters
        ? Math.floor(parseInt(item.distanceMeters) / 1000) + 'km'
        : item.districtName,
      hrPositionUrl: item.hrPositionUrl
        ? item.hrPositionUrl
        : item.sex === 1
          ? '/static/header/hrheader1.png'
          : '/static/header/hrheader2.png',
    }
  })
}

const queryList = async (page: number, size: number) => {
  try {
    pageSetInfo(page, size)
    const res: any = await ditchThroughList({ ...params, page: pageInfo.page, size: pageInfo.size })
    console.log(res, 'res==========')
    if (res.code === 0) {
      pageData.value = groupByYearMonth(res.data?.list)
      console.log(pageData.value, 'pageData.value==========')
      pagingRef.value.complete(pageData.value)
    }
  } catch (error) {
    console.error('ditchThroughListFun error:', error)
    pagingRef.value.complete(false)
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
}
.content-data {
  padding: 0rpx 40rpx 0rpx;
  font-size: 28rpx;
  line-height: 60rpx;
  color: #888;
  background: #f5f5f5;
}
.salary {
  color: #4399ff !important;
}
.content_search-p {
  padding: 30rpx 40rpx;
}

.bg_left_icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50rpx;
}
.page_left_1 {
  font-size: 22rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #888888;
}
.my-jl-card {
  .my-jl-card-left {
    justify-content: flex-start;
    width: 50%;

    .my-jl-card-left-header {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .my-jl-card-left-item {
      margin-left: 10rpx;

      .card-name {
        font-weight: 500;
        color: #555;
      }
    }
  }

  .my-jl-card-right {
    flex: 1;
    flex-wrap: wrap;
    align-items: flex-start;

    .my-jl-card-right-btn {
      display: flex;
      display: inline-block;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: right;
      padding: 2rpx 20rpx;
      margin: 10rpx 10rpx 10rpx 0rpx;
      background-color: #f3f3f3;
      border-radius: 10rpx;

      .pic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }

  .my-jl-card-cunstrct {
    line-height: 52rpx;

    .my-jl-card-cunstrct-img {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;

  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 152rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 22rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 20rpx;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 4rpx 40rpx;
    margin-top: 14rpx;
    margin-right: 22rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
}
.onlineRes {
  border-bottom: 1rpx solid #e0e0e0;
}
.content_search_list_flex {
  box-sizing: border-box;
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  padding: 0rpx 40rpx 40rpx;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 60rpx;
  font-weight: 500;
  color: #000000;
}

.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_adress {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 24rpx;
    font-weight: 400;
    color: #555555;
  }

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}

.content_list_left_for {
  display: flex;
  flex-direction: row;
  padding-right: 50rpx;
}

.content_list_left_color {
  font-size: 28rpx;
  font-weight: 600;
  color: #000000;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 10rpx 20rpx;
      white-space: nowrap;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_list_for {
        .content_list_border_1 {
          padding-left: 30rpx;
        }
      }
    }
  }
}

.select_border {
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.select_noBorder {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #3e3e56;
  text-align: center;
}

.content_list_left_xian {
  width: 90rpx;
  height: 4rpx;
  padding-top: 1rpx;
  font-weight: bold;
  background: #4399ff;
  border-radius: 2rpx 2rpx 2rpx 2rpx;
}
.content_list_left_color1 {
  font-size: 28rpx;
  color: #888;
}
.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 10rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.content {
  width: 100%;
}

.start_ICON {
  ._icon {
    width: 56rpx;
    height: 56rpx;
  }
}
</style>
