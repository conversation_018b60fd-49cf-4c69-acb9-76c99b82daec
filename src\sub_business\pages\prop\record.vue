<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '使用记录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">使用记录</text>
          </template>
        </wd-navbar>

        <recordList />
      </wd-config-provider>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import recordList from '@/sub_business/components/recordList.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})

function handleClickLeft() {
  uni.navigateBack()
}
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
  navbarColor: '#000',
}
</script>

<style lang="scss" scoped>
:deep(.record-collapse) {
  margin: 40rpx;
  background: #ffffff;
  border: 0px solid #ededed;
  border-radius: 20px;
  box-shadow: 0px 4px 13.5px 0px rgba(0, 0, 0, 0.15);
}
:deep(.wd-collapse-item__body) {
  padding: 0rpx 30rpx 30rpx 30rpx;
}
:deep(.wd-collapse-item__header.is-expanded[data-v-27ee4d1d]::after) {
  background: transparent !important;
}
</style>
