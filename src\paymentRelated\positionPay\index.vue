<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view>
    <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
    <image :src="pay" class="pany"></image>
    <view class="btn-fixed">
      <view class="btn_box">
        <view class="btn_bg">确认</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import pay from '@/paymentRelated/img/pany.png'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(155deg, #FFDEDE 0%, #EBEFFA 18%, #FFFFFF 44%, #FFFFFF 100%)',
  },
})
const back = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.pany {
  width: 100%;
  height: 100vh;
}
.back-button {
  position: fixed;
  top: 20rpx;
  left: 40rpx;
  z-index: 10001;
}
.btn-fixed {
  position: fixed;
  bottom: 40rpx;
  box-sizing: border-box;
  justify-content: center;
  width: 100%;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
