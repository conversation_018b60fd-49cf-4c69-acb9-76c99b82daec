export default {
  appName: 'ChatIM',
  image: 'Image',
  audio: 'Voice Message',
  video: 'Video Message',
  file: 'File',
  userCard: 'Contact Card',
  videoBtn: 'Video',
  custom: 'Custom Message',
  combine: 'Combine Messages',
  conversationSearchPlaceholder: 'Search conversations',
  conversationEmptyTip: 'No conversations yet, start chatting now~',
  deleteConv: 'Delete',
  confirmDeleteConv: 'Confirm delete and clear the records',
  pinConv: 'Pin Conversation',
  muteConv: 'Mute Notifications',
  markRead: 'Mark as Read',
  justNow: 'Just Now',
  yesterday: 'Yesterday',
  beforeYesterday: 'The Day Before Yesterday',
  sunday: 'Sunday',
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  loadMore: 'Load More',
  sendMessagePlaceholder: 'Enter message...',
  imageUpload: 'Image',
  videoUpload: 'Video',
  about: 'About',
  logout: 'Log Out',
  groupList: 'Group Chats',
  contact: 'Contacts',
  addContact: 'Add Contacts',
  addGroup: 'Join Group',
  searchPlaceholder: 'Search',
  searchNoContact: 'No results found',
  add: 'Request',
  applyAddFriend: 'Request to add you as a friend',
  agreedFriend: 'Accepted your friend request',
  refusedFriend: 'Declined your friend request',
  addedFriend: 'Added you as a friend',
  deletedFriend: 'Unfriended',
  emptyNoticeTip: 'No new notifications',
  createGroup: 'Create Group',
  selectContactTip: 'Select a contact',
  createGroupSuccess: 'Group chat created successfully',
  contactDetail: 'Contact Details',
  remark: 'Remark',
  remarkPlaceholder: 'Enter remark',
  editRemark: 'Edit',
  deleteFriend: 'Delete Friend',
  blockList: 'Blocked List',
  sendMessage: 'Send Message',
  deleteSuccess: 'Deleted successfully',
  groupDetail: 'Group Details',
  enterGroup: 'Enter Group',
  destroyGroup: 'Disband Group',
  destroyGroupSuccess: 'Disbanded successfully',
  uploadFailed: 'Upload failed',
  acceptFriend: 'Accept',
  refuseFriend: 'Decline',
  groupNamePlaceholder: 'Enter group name',
  groupDescPlaceholder: 'Enter group description',
  groupName: 'Group Name',
  groupDesc: 'Group Description',
  groupMember: 'Group Members',
  cancel: 'Cancel',
  groupNotice: 'Group Notifications',
  publicGroup: 'Public Group',
  privateGroup: 'Private Group',
  requestSended: 'Request sent successfully',
  joinedGroupNotice: 'Joined the group successfully',
  destroyGroupNotice: 'The group has been disbanded',
  groupDetailButton: 'Group Details',
  owner: 'Group Owner',
  inviteUser: 'Invite to Group',
  removeUser: 'Remove from Group',
  feedback: 'Feedback',
  personalInfo: 'Personal Info',
  personalSetting: 'Personal Settings',
  avatar: 'Avatar',
  nickname: 'Nickname',
  nickNamePlaceholder: 'Enter nickname',
  sign: 'Signature',
  signPlaceholder: 'Enter signature',
  edit: 'Edit',
  updateUserInfo: 'Update',
  newNoticeTip: 'You have a new notification',
  viewAllGroupMembers: 'View all group members',
  disabledAddSelf: 'You cannot add yourself as a friend',
  alreadyContact: 'Already a friend',
  systemNotice: 'System Notifications',
  holdToTalk: 'Hold to talk',
  loose: 'Loose',
  sendAudio: 'Send',
  recall: 'Recall',
  recallNotice: 'recalled a message',
  recallFailed: 'Recall failed',
  audioDurationIsShort: 'The audio duration is too short',
  leaveGroup: 'Leave Group',
  leaveGroupSuccess: 'Left the group successfully',
  group: 'the group',
  wxConfigTip: 'Setting successful, please reopen the mini program',
  inviteToGroup: 'invited you to join the group',
  myGroup: 'My Groups',
  mute: 'Mute',
  unmute: 'Unmute',
  pin: 'Pin',
  unpin: 'Unpin',
  selfRecallTip: 'You recalled a message',
  otherRecallTip: 'Someone recalled a message',
  you: 'You',
  reply: 'replying',
  messageNotFound: 'Message not found',
  messageEdited: 'Edited',
  messageEditing: 'Editing',
  copyBtn: 'Copy',
  deleteBtn: 'Delete',
  replyBtn: 'Reply',
  recallBtn: 'Recall',
  editBtn: 'Edit',
  atAll: 'All',
  atTag: '[Someone@You] ',
  atAllTag: '[@All] ',
  tapRecord: 'Tap to record',
  recording: 'Recording',
  playing: 'Playing',
  tapPlay: 'Tap to play',
  noMoreMessage: 'No more messages',
  newRequest: 'New Request',
  mention: '@Mention',
  mentionAll: 'All',
  shareContact: 'Share Contact',
  searchContact: 'Search Contact',
  createGroupBtn: 'Create',
  newChatTitle: 'New Chat',
  newChatButton: 'New Chat',
  contactAddSuccess: 'Request sent successfully',
  contactAddFailed: 'Request failed',
  contactAddTitle: 'Add Contact',
  contactAddBtn: 'Add',
  contactAddInputPlaceholder: "Enter the user's ID",
  contactRequestListTitle: 'New Requests',
  contactRequestListTip: 'Request to add you as a friend',
  contactRequestAgreeButton: 'Agree',
  modalConfirm: 'Confirm',
  modalCancel: 'Cancel',
  openDocumentFailed: 'File open failed',
}
