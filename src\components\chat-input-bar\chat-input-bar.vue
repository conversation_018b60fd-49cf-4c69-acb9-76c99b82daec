<!-- z-paging聊天输入框 -->

<template>
  <view class="chat-input-bar-container">
    <view class="chat-input-bar">
      <view class="chat-input-container">
        <!-- :adjust-position="false"必须设置，防止键盘弹窗自动上顶，交由z-paging内部处理 -->
        <textarea
          :focus="focus"
          class="chat-input"
          v-model="msg"
          :adjust-position="false"
          confirm-type="send"
          type="text"
          placeholder="请输入内容"
          @confirm="sendClick"
        />
      </view>
      <!-- 表情图标（如果不需要切换表情面板则不用写） -->
      <!-- <view class="emoji-container">
				<image
					class="emoji-img"
					:src="`/static/${emojiType || 'emoji'}.png`"
					@click="emojiChange"
				></image>
			</view> -->
      <view @click="sendClick">
        <view class="box_font" :class="{ 'active-bg': sendEnabled, 'no-active-bg': !sendEnabled }">
          <image class="box_image" src="/static/img/<EMAIL>" mode="aspectFill"></image>
        </view>
      </view>
    </view>
    <!--  表情面板，这里使用height控制隐藏显示是为了有高度变化的动画效果（如果不需要切换表情面板则不用写） -->
    <view
      class="emoji-panel-container"
      :style="[{ height: emojiType === 'keyboard' ? '400rpx' : '0px' }]"
    >
      <scroll-view scroll-y style="flex: 1; height: 100%">
        <view class="emoji-panel">
          <text
            class="emoji-panel-text"
            v-for="(item, index) in emojisArr"
            :key="index"
            @click="emojiClick(item)"
          >
            {{ item }}
          </text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'chat-input-bar',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      msg: '',

      // 表情数组（如果不需要切换表情面板则不用写）
      emojisArr: [
        '😊',
        '😁',
        '😀',
        '😃',
        '😣',
        '😞',
        '😩',
        '😫',
        '😲',
        '😟',
        '😦',
        '😜',
        '😳',
        '😋',
        '😥',
        '😰',
        '🤠',
        '😎',
        '😇',
        '😉',
        '😭',
        '😈',
        '😕',
        '😏',
        '😘',
        '😤',
        '😡',
        '😅',
        '😬',
        '😺',
        '😻',
        '😽',
        '😼',
        '🙈',
        '🙉',
        '🙊',
        '🔥',
        '👍',
        '👎',
        '👌',
        '✌️',
        '🙏',
        '💪',
        '👻',
      ],
      // 当前input focus（如果不需要切换表情面板则不用写）
      focus: false,
      // 当前表情/键盘点击后的切换类型，为空字符串代表展示表情logo但是不展示不展示表情面板（如果不需要切换表情面板则不用写）
      emojiType: '',
    }
  },
  computed: {
    sendEnabled() {
      return !this.disabled
    },
  },
  methods: {
    // 更新了键盘高度（如果不需要切换表情面板则不用写）
    updateKeyboardHeightChange(res) {
      if (res.height > 0) {
        // 键盘展开，将emojiType设置为emoji
        this.emojiType = 'emoji'
      }
    },
    // 用户尝试隐藏键盘，此时如果表情面板在展示中，应当隐藏表情面板，如果是键盘在展示中不用处理，z-paging内部已经处理（如果不需要切换表情面板则不用写）
    hidedKeyboard() {
      if (this.emojiType === 'keyboard') {
        this.emojiType = ''
      }
    },
    // 点击了切换表情面板/键盘（如果不需要切换表情面板则不用写）
    emojiChange() {
      this.$emit('emojiTypeChange', this.emojiType)
      if (this.emojiType === 'keyboard') {
        // 点击了键盘，展示键盘
        this.focus = true
      } else {
        // 点击了切换表情面板
        this.focus = false
        // 隐藏键盘
        uni.hideKeyboard()
      }
      this.emojiType = !this.emojiType || this.emojiType === 'emoji' ? 'keyboard' : 'emoji'
    },
    // 点击了某个表情，将其插入输入内容中（如果不需要切换表情面板则不用写）
    emojiClick(text) {
      this.msg += text
    },

    // 点击了发送按钮
    sendClick() {
      console.log('sendClick============================')
      if (!this.sendEnabled) return
      this.$emit('send', this.msg)
      this.msg = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.active-bg {
  background-color: #4399ff;
}
.no-active-bg {
  background-color: #bbbbbb;
}
.chat-input-bar {
  display: flex;
  flex-direction: row;
  align-items: center;

  padding: 10rpx 20rpx;
}
.box_font {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 140rpx;
  margin-left: 20rpx;

  border-radius: 20rpx;

  .box_image {
    width: 45rpx;
    height: 45rpx;
  }
}
.chat-input-container {
  /* #ifndef APP-NVUE */
  display: flex;
  flex: 1;
  /* width: 100%; */
  height: 140rpx;
  /* #endif */
  padding: 40rpx 30rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 10rpx;
  border-radius: 10px 10px 10px 10px;
  box-shadow: 4px 4px 17px 0px rgba(0, 0, 0, 0.1);
}
.chat-input {
  flex: 1;
  font-size: 28rpx;
  /* padding-top: 10rpx; */
}
.emoji-container {
  width: 54rpx;
  height: 54rpx;
  margin: 10rpx 0rpx 10rpx 20rpx;
}
.emoji-img {
  width: 54rpx;
  height: 54rpx;
}
.chat-input-send {
  /* #ifndef APP-NVUE */
  display: flex;
  align-items: center;
  /* #endif */
  justify-content: center;
  /* width: 110rpx; */
  height: 60rpx;
  margin: 10rpx 10rpx 10rpx 0rpx;

  border-radius: 10rpx;
}
.chat-input-send-disabled {
  background-color: #bbbbbb;
}
.chat-input-send-text {
  font-size: 26rpx;
  color: white;
}
.emoji-panel-container {
  overflow: hidden;
  background-color: #f8f8f8;
  transition-duration: 0.15s;
  transition-property: height;
  /* #ifndef APP-NVUE */
  will-change: height;
  /* #endif */
}
.emoji-panel {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  flex-wrap: wrap;
  padding-right: 10rpx;
  padding-bottom: 10rpx;
  padding-left: 15rpx;
  font-size: 30rpx;
}
.emoji-panel-text {
  margin-top: 20rpx;
  margin-left: 15rpx;
  font-size: 50rpx;
}
</style>
