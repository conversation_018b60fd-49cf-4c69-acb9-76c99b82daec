/* 格式化后的 pdfh5.css */
.pdfjs {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #fff;
}

.pdfjs .viewerContainer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  transition: all 0.3s;
}

.pdfjs .pdfViewer {
  position: relative;
  top: 0;
  left: 0;
  padding: 10px 8px;
}

.pdfjs .pdfViewer .pageContainer {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  margin: 0px auto 8px auto;
  overflow: visible;
  background-color: white;
  -webkit-box-shadow: darkgrey 0px 1px 3px 0px;
  -moz-box-shadow: darkgrey 0px 1px 3px 0px;
  box-shadow: darkgrey 0px 1px 3px 0px;
}

.pdfjs .pdfViewer .pageContainer img {
  position: relative;
  z-index: 100;
  width: 100%;
  height: 100%;
  /* user-select:none; */
}

.pdfjs .pageNum {
  position: absolute;
  top: 20px;
  left: 15px;
  z-index: 997;
  display: none;
  height: 26px;
  padding: 0px 7px;
  border-radius: 8px;
  transition: all 0.3s;
}

.pdfjs .pageNum-bg,
.pdfjs .pageNum-num {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  font-size: 16px;
  line-height: 26px;
  color: #fff;
  text-align: center;
  border-radius: 8px;
}

.pdfjs .pageNum-bg {
  background: rgba(0, 0, 0, 0.5);
}

.pdfjs .pageNum-num {
  position: relative;
}

.pdfjs .pageNum span {
  font-size: 16px;
  color: #fff;
}

.pdfjs .loadingBar {
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 99;
  width: 100%;
  height: 4px;
  background: #fff !important;
  transition: all 0.3s;
}

.pdfjs .loadingBar .progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  overflow: hidden;
  background: #fff !important;
  transition: width 200ms;
}

.pdfjs .loadingBar .progress .glimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: calc(100% + 150px);
  height: 100%;
  background: #7bcf34;
}

.pdfjs .backTop {
  position: absolute;
  right: 15px;
  bottom: 90px;
  z-index: 999;
  display: none;
  width: 50px;
  height: 50px;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  background: rgba(0, 0, 0, 0.4)
    url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAAA+klEQVRYw+2WUQ2DMBCG2TIBSJiESkACEpCAg83BcLBJmIQ5gClgDpiDby9tciGkoaUtZOESXuhdv7+X/pdm2dYC6IgX7Zh3THy+w9oN/rMASqBcE26iSA1XwCAEDIBKBc8F/KE/gB7IU8BbDXyJf2Z2tFFFAE8N6iRIi/jotXssuGn1FzhPrCu9BtCEhlcCrix5hbiYVSh46bKpELvcniO71Q51zWJ7ju3mUe9vzym7eR7Az57CbohTXBzAt9GknG9PoLY8KK4z6htLfeXTTXMZAfoZuWYWKC+YZWMAQuWZSP0k2wXsAnYB2xNwci1wGTKhO/COlLtu/ABVfTFsxwwYRgAAAABJRU5ErkJggg==)
    no-repeat center;
  background-size: 50% 50%;
  border-radius: 50%;
  transition: all 0.3s;
}

.pdfjs .loadEffect {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 99;
  width: 100px;
  height: 100px;
  margin-top: -50px;
  margin-left: -50px;
  background: url(data:image/gif;base64,R0lGODlhgACAAKIAAP///93d3bu7u5mZmQAA/wAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFBQAEACwCAAIAfAB8AAAD/0i63P4wygYqmDjrzbtflvWNZGliYXiubKuloivPLlzReD7al+7/Eh5wSFQIi8hHYBkwHUmD6CD5YTJLz49USuVYraRsZ7vtar7XnQ1Kjpoz6LRHvGlz35O4nEPP2O94EnpNc2sef1OBGIOFMId/inB6jSmPdpGScR19EoiYmZobnBCIiZ95k6KGGp6ni4wvqxilrqBfqo6skLW2YBmjDa28r6Eosp27w8Rov8ekycqoqUHODrTRvXsQwArC2NLF29UM19/LtxO5yJd4Au4CK7DUNxPebG4e7+8n8iv2WmQ66BtoYpo/dvfacBjIkITBE9DGlMvAsOIIZjIUAixliv9ixYZVtLUos5GjwI8gzc3iCGghypQqrbFsme8lwZgLZtIcYfNmTJ34WPTUZw5oRxdD9w0z6iOpO15MgTh1BTTJUKos39jE+o/KS64IFVmsFfYT0aU7capdy7at27dw48qdS7eu3bt480I02vUbX2F/JxYNDImw4GiGE/P9qbhxVpWOI/eFKtlNZbWXuzlmG1mv58+gQ4seTbq06dOoU6vGQZJy0FNlMcV+czhQ7SQmYd8eMhPs5BxVdfcGEtV3buDBXQ+fURxx8oM6MT9P+Fh6dOrH2zavc13u9JXVJb520Vp8dvC76wXMuN5Sepm/1WtkEZHDefnzR9Qvsd9+/wi8+en3X0ntYVcSdAE+UN4zs7ln24CaLagghIxBaGF8kFGoIYV+Ybghh841GIyI5ICIFoklJsigihmimJOLEbLYIYwxSgigiZ+8l2KB+Ml4oo/w8dijjcrouCORKwIpnJIjMnkkksalNeR4fuBIm5UEYImhIlsGCeWNNJphpJdSTlkml1jWeOY6TnaRpppUctcmFW9mGSaZceYopH9zkjnjUe59iR5pdapWaGqHopboaYua1qije67GJ6CuJAAAIfkEBQUABAAsCgACAFcAMAAAA/9Iutz+ML5Ag7w46z0r5WAoSp43nihXVmnrdusrv+s332dt4Tyo9yOBUJD6oQBIQGs4RBlHySSKyczVTtHoidocPUNZaZAr9F5FYbGI3PWdQWn1mi36buLKFJvojsHjLnshdhl4L4IqbxqGh4gahBJ4eY1kiX6LgDN7fBmQEJI4jhieD4yhdJ2KkZk8oiSqEaatqBekDLKztBG2CqBACq4wJRi4PZu1sA2+v8C6EJexrBAD1AOBzsLE0g/V1UvYR9sN3eR6lTLi4+TlY1wz6Qzr8u1t6FkY8vNzZTxaGfn6mAkEGFDgL4LrDDJDyE4hEIbdHB6ESE1iD4oVLfLAqPETIsOODwmCDJlv5MSGJklaS6khAQAh+QQFBQAEACwCAAIAfAB8AAAD/0i63P4wykmrvTjrzbv/YCiOZGmeaKqubOu+cCzPdArcQK2TOL7/nl4PSMwIfcUk5YhUOh3M5nNKiOaoWCuWqt1Ou16l9RpOgsvEMdocXbOZ7nQ7DjzTaeq7zq6P5fszfIASAYUBIYKDDoaGIImKC4ySH3OQEJKYHZWWi5iZG0ecEZ6eHEOio6SfqCaqpaytrpOwJLKztCO2jLi1uoW8Ir6/wCHCxMG2x7muysukzb230M6H09bX2Nna29zd3t/g4cAC5OXm5+jn3Ons7eba7vHt2fL16tj2+QL0+vXw/e7WAUwnrqDBgwgTKlzIsKHDh2gGSBwAccHEixAvaqTYcFCjRoYeNyoM6REhyZIHT4o0qPIjy5YTTcKUmHImx5cwE85cmJPnSYckK66sSAAj0aNIkypdyrSp06dQo0qdSrWq1atYs2rdyrWr169gwxZJAAA7)
    no-repeat center;
  background-size: 30% 30%;
  transition: all 0.3s;
}

.pdfjs .pdfViewer .pageContainer img.pdfLogo {
  /* user-select:none; */
  position: absolute;
  z-index: 101;
}

.pdfjs .textLayer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 101;
  overflow: hidden;
  line-height: 1;
  opacity: 0.2;
}

.pdfjs .textLayer > span {
  position: absolute;
  color: transparent;
  white-space: pre;
  cursor: text;
  transform: translate(-99px, -80px) scaleX(1) !important;
  transform-origin: 0% 0%;
}

.pdfjs .textLayer .highlight {
  padding: 1px;
  margin: -1px;
  background-color: rgba(180, 0, 170, 1);
  border-radius: 4px;
}

.pdfjs .textLayer .highlight.begin {
  border-radius: 4px 0px 0px 4px;
}

.pdfjs .textLayer .highlight.end {
  border-radius: 0px 4px 4px 0px;
}

.pdfjs .textLayer .highlight.middle {
  border-radius: 0px;
}

.pdfjs .textLayer .highlight.selected {
  background-color: rgba(0, 100, 0, 1);
}

.pdfjs .textLayer ::selection {
  background: rgba(0, 0, 255, 1);
}

.pdfjs .textLayer .endOfContent {
  position: absolute;
  top: 100%;
  right: 0px;
  bottom: 0px;
  left: 0px;
  z-index: -1;
  display: block;
  cursor: default;
  /* user-select: none; */
}

.pdfjs .textLayer .endOfContent.active {
  top: 0px;
}
