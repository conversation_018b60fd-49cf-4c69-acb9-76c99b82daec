<template>
  <wd-config-provider :themeVars="themeVars" class="h-full">
    <slot />
    <wd-toast />
    <wd-message-box />
  </wd-config-provider>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'

const themeVars: ConfigProviderThemeVars = {
  // colorTheme: 'red',
  buttonPrimaryBgColor: '#0096FF',
  tabbarHeight: '64px',
  // buttonPrimaryColor: '#07c160',
}
</script>
