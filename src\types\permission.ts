// 权限类型定义
export type PermissionType =
  | 'location'
  | 'camera'
  | 'photoLibrary'
  | 'record'
  | 'push'
  | 'contact'
  | 'calendar'
  | 'memo'
// Android权限映射
export const ANDROID_PERMISSIONS = {
  location: 'android.permission.ACCESS_FINE_LOCATION',
  camera: 'android.permission.CAMERA',
  photoLibrary: 'android.permission.READ_EXTERNAL_STORAGE',
  record: 'android.permission.RECORD_AUDIO',
  push: '', // Android推送不需要权限
  contact: 'android.permission.READ_CONTACTS',
  calendar: 'android.permission.READ_CALENDAR',
  memo: '', // Android没有对应权限
} as const
// 权限状态
export enum PermissionStatus {
  GRANTED = 1, // 已授权
  DENIED = 0, // 拒绝
  DENIED_ALWAYS = -1, // 永久拒绝
}
// iOS权限状态值
export const IOS_PERMISSION_STATUS = {
  // 推送权限
  PUSH_DENIED: 0,
  PUSH_GRANTED: 1,
  // 定位权限
  LOCATION_DENIED: 2,
  LOCATION_GRANTED: 3,
  // 麦克风权限
  RECORD_DENIED: [1684369017, 1970168948],
  RECORD_GRANTED: 1735552628,
  // 相机/相册权限
  CAMERA_GRANTED: 3,
  PHOTO_GRANTED: 3,
  // 通讯录/日历权限
  CONTACT_GRANTED: 3,
  CALENDAR_GRANTED: 3,
} as const

export interface PermissionResult {
  granted: boolean
  status: PermissionStatus
  message?: string
}

export interface AndroidPermissionResult {
  code?: number
  message?: string
}
