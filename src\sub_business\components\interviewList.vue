<template>
  <view class="pageList-item flex items-start">
    <view class="pageList-item-left">
      <view class="c-#000 text-38rpx text-right">
        {{ item.agreeTime ? item.agreeTime.substring(11, 13) : '' }}点
      </view>
    </view>
    <view class="pageList-item-right m-l-30rpx pageList-right p-l-40rpx p-b-40rpx">
      <view
        class="pageList-item-right-card relative"
        :class="item.status === 1 ? 'bg-#4399ff' : 'bg-#F2F2F2'"
      >
        <view class="flex items-center">
          <view class="w-90rpx">
            <image
              class="w-76rpx h-76rpx rounded-full"
              :src="item.userUrl"
              mode="aspectFill"
            ></image>
          </view>
          <view class="flex-1">
            <view class="flex items-center justify-between">
              <view
                class="text-32rpx p-b-5rpx u-line-1"
                :class="item.status === 1 ? 'c-#fff' : 'c-#000'"
              >
                {{ item.userName }}
              </view>
            </view>

            <view class="flex justify-between">
              <view class="text-28rpx font-400" :class="item.status === 1 ? 'c-#fff' : 'c-#000'">
                {{ item.positionName }}
              </view>
              <view class="text-28rpx" :class="item.status === 1 ? 'c-#fff' : 'c-#000'">
                {{ item.salaryStart }}{{ item.salaryEnd ? '-' + item.salaryEnd : '' }}
              </view>
            </view>
          </view>
        </view>
        <view class="absolute top-[7rpx] right-[85rpx] z-100" v-if="item.status === 1">
          <wd-img :width="15" :height="15" :src="starIcon" />
        </view>
        <view class="absolute top-[10rpx] right-[7rpx] z-100" v-if="item.status === 1">
          <wd-img :width="35" :height="35" :src="interview" />
        </view>
        <view
          v-if="item.status === 2 || item.status === 3 || item.status === 4"
          class="c-#FF7648 text-24rpx border-1rpx border-solid border-#FF7648 rounded-[10rpx] px-15rpx absolute top-[30rpx] right-[20rpx] z-1000"
        >
          {{ item.status === 2 ? '已取消' : item.status === 3 ? '已取消' : '已取消' }}
        </view>
        <view v-if="item.status === 0">
          <view
            class="c-#4399FF text-24rpx border-1rpx border-solid border-#4399FF rounded-[10rpx] px-15rpx absolute top-[30rpx] right-[20rpx] z-1000"
          >
            待处理
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import interview from '@/sub_business/static/interview/interview.png'
import starIcon from '@/sub_business/static/interview/star_icon.png'

const props = defineProps<{
  item: any
}>()
</script>
<style lang="scss" scoped>
.pageList-right {
  border-left: 1rpx solid #cccaca;
}
.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  border-radius: 20rpx;
}

.text-28rpx {
  transition: all 0.3s ease;
}
</style>
