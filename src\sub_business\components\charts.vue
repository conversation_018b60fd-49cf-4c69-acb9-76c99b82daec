<template>
  <view>
    <view class="m-b-20rpx">
      <wd-tabs v-model="tab" custom-class="custom-class" swipeable @change="handleTabChange">
        <block v-for="item in tabList" :key="item">
          <wd-tab :title="item.name"></wd-tab>
        </block>
      </wd-tabs>
    </view>
    <view class="w-100 h-[400rpx] bg-#2E6CFA rounded-20rpx p-r-80rpx p-l-0rpx p-t-20rpx p-b-20rpx">
      <qiun-data-charts v-if="isChartReady" :chartData="chartData" :opts="opts" type="line" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { payPropUseRecordView } from '@/service/payPropUseRecord'

const props = defineProps({
  targetId: {
    type: [Number, String],
    default: null,
  },
})

const chartData = ref({})
const tab = ref(0)
const isChartReady = ref(false)

const currentTime = ref<string>('')
const pastTime = ref<any>('')

// 日期格式化函数
const formatDate = (date: Date): string => {
  const y = date.getFullYear()
  let m: any = date.getMonth() + 1
  m = m < 10 ? '0' + m : m
  let d: any = date.getDate()
  d = d < 10 ? '0' + d : d
  return y + '-' + m + '-' + d
}

const tabList = ref([
  {
    name: '看过我的',
    value: 1,
  },
  {
    name: '有交换的',
    value: 2,
  },
  {
    name: '沟通过的',
    value: 3,
  },
  {
    name: '收获简历',
    value: 4,
  },
])
const opts = ref({
  color: [
    '#ffffff',
    '#dashed',
    '#FAC858',
    '#EE6666',
    '#73C0DE',
    '#3CA272',
    '#FC8452',
    '#9A60B4',
    '#ea7ccc',
  ],
  padding: [10, 10, 0, 10],
  enableScroll: false,
  legend: {
    show: false,
  },
  xAxis: {
    show: true,
    axisLine: true,
    disableGrid: false,
    gridType: 'dash',
    title: '',
    fontSize: 12, // 减小字体大小
    fontColor: '#ffffff',
    rotateLabel: false, // 旋转标签防止重叠
    itemCount: 7, // 限制显示的标签数量
    margin: 8, // 增加边距
  },
  yAxis: {
    show: false,
    disableGrid: true,
    gridType: 'dash',
    dashLength: 2,
    data: [
      {
        fontSize: 0,
        fontColor: 'transparent',
        axisLine: false,
      },
    ],
  },
  extra: {
    line: {
      type: 'curve',
      width: 2,
      activeType: 'hollow',
    },
  },
})

function handleTabChange() {
  isChartReady.value = false
  nextTick(() => {
    getServerData()
  })
}

const getServerData = async () => {
  const tabIndex = tab.value >= 0 && tab.value < tabList.value.length ? tab.value : 0
  const selectedTabValue = tabList.value[tabIndex]?.value
  const res: any = await payPropUseRecordView({
    type: selectedTabValue,
    positionId: props.targetId || '',
    startTime: pastTime.value,
    endTime: currentTime.value,
  })
  if (res.code === 0) {
    const categories: string[] = []
    const startDate = new Date(pastTime.value)
    const endDate = new Date(currentTime.value)

    for (let i = 0; i <= 6; i++) {
      const date = new Date(startDate)
      date.setDate(startDate.getDate() + i)
      const formattedDate = formatDate(date)
      const dateWithoutYear = formattedDate.substring(5) // 去掉前5个字符（年份和连字符）
      categories.push(dateWithoutYear)
    }

    // 根据日期匹配数据，没有匹配的设为0
    const seriesData: number[] = []
    categories.forEach((date) => {
      const matchedData = res.data?.find((item: any) => {
        const serverDate = item.dates
        const serverDateWithoutYear = serverDate ? serverDate.substring(5) : ''
        return serverDateWithoutYear === date
      })
      seriesData.push(matchedData ? matchedData.counts : 0)
    })

    // 更新图表数据
    const resData = {
      categories,
      series: [
        {
          axisLine: false,
          textColor: 'transparent',
          pointShape: 'none',
          name: '',
          data: seriesData,
        },
      ],
    }
    chartData.value = JSON.parse(JSON.stringify(resData))
    isChartReady.value = true
  }
}

onMounted(async () => {
  await uni.$onLaunched
  const today = new Date()
  currentTime.value = formatDate(today)
  const pastDate = new Date()
  pastDate.setDate(today.getDate() - 7)
  pastTime.value = formatDate(pastDate)

  // 等待 targetId 有值后再调用接口
  if (props.targetId) {
    getServerData()
  }
})

// 监听 targetId 变化
watch(
  () => props.targetId,
  (newVal) => {
    if (newVal && currentTime.value && pastTime.value) {
      getServerData()
    }
  },
  { immediate: false },
)
</script>

<style lang="scss" scoped>
:deep(.custom-class) {
  background-color: transparent;

  .wd-tabs__nav {
    background-color: transparent;
  }
}
</style>
