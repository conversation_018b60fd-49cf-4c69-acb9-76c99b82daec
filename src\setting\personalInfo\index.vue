<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img-h">
    <CustomNavBar title="个人中心" />
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="mainText labelName">头像</view>
        <view>
          <view
            class="text-32rpx font-w-500 flex-1 text-r flex justify-right items-center flex-col"
          >
            <wd-upload
              reupload
              image-mode="aspectFill"
              :before-upload="beforeUpload"
              v-model:file-list="fileList"
              :limit="1"
              :header="header"
              :action="baseUrl"
              @success="successFun"
              custom-class="custom-class-img"
              accept="image"
            ></wd-upload>
          </view>
          <view class="c-#888 text-24rpx text-center" v-if="infoObj.headImgStatus === 0">
            审核中
          </view>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">姓名</view>
        <view class="text-32rpx font-w-500 flex-1 text-r">
          {{ infoObj.trueName }}
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">身份认证</view>
        <view class="text-32rpx font-w-500 flex-1 text-r" v-if="infoObj.idCard">
          {{ infoObj.idCard }}
        </view>
        <view
          class="text-32rpx font-w-500 flex-1 text-r activeColor"
          @click="goIdentityAuth"
          v-else
        >
          未认证
          <!-- <wd-icon name="warning" size="15px" color="#FF0000"></wd-icon> -->
        </view>
      </view>
      <view class="form-list relative">
        <view>
          <view class="flex-between">
            <view class="mainText labelName">手机号</view>
            <view class="flex justify-right items-center">
              <view class="text-32rpx font-w-500 flex-1 text-r" v-if="infoObj.phone">
                {{ infoObj.phone }}
              </view>
              <view
                class="text-32rpx font-w-500 flex-1 text-r activeColor"
                @click="goPhoneUpdata"
                v-else
              >
                未填写
              </view>
            </view>
          </view>
          <view class="flex-c p-t-10rpx">
            <wd-icon name="info-circle" size="14px" color="#999999"></wd-icon>
            <view class="subText">手机号只有在您与HR交换时候，才会告知对方</view>
          </view>
        </view>
        <!-- <view class="flex-c position-r" v-if="!infoObj.phone" @click="goPhoneUpdata">
          <view class="text-24rpx red-color">未填写,去填写</view>
          <wd-icon name="info-circle" size="14px" color="#ff0000"></wd-icon>
        </view> -->
      </view>
      <view class="form-list">
        <view class="">
          <view class="flex-between">
            <view class="mainText labelName">微信号</view>
            <view
              class="text-32rpx font-w-500 flex-1 text-r"
              v-if="infoObj.wxCode"
              @click="goWxCode"
            >
              {{ infoObj.wxCode }}
            </view>
            <view class="text-32rpx font-w-500 flex-1 text-r activeColor" v-else @click="goWxCode">
              去完善
            </view>
          </view>
          <view class="flex-c p-t-10rpx">
            <wd-icon name="info-circle" size="14px" color="#999999"></wd-icon>
            <view class="subText">手机号只有在您与HR交换时候，才会告知对方</view>
          </view>
        </view>
      </view>
      <view class="form-list">
        <view class="flex-between items-center">
          <view class="mainText labelName-1">首次参加工作时间</view>
          <view class="flex justify-right items-center">
            <wd-datetime-picker
              type="year-month"
              v-model="firstJobDate"
              placeholder="请选择"
              class="w-200rpx"
              custom-value-class="custom-label-class"
              @confirm="confirm"
              :minDate="minDate"
              :default-value="defaultTime"
              :maxDate="maxDate"
              custom-class="custom-class"
            />
          </view>
        </view>

        <view v-if="massageInfo" class="text-24rpx c-#ff0000">{{ massageInfo }}</view>
        <!-- <view class="text-32rpx font-w-500 flex-1 text-r activeColor" v-else>请选择</view> -->
      </view>

      <view class="form-list flex-between">
        <view class="mainText labelName">居住地址</view>
        <view
          class="text-32rpx font-w-500 flex-1 text-r"
          @click="goAdress"
          v-if="infoObj.addressManager"
        >
          {{ infoObj.addressManager }}
        </view>
        <view class="text-32rpx font-w-500 flex-1 text-r activeColor" v-else @click="goAdress">
          去完善
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { updateWorkTime, userImageAudit } from '@/interPost/my'
import { queryMyMessage } from '@/interPost/home'
import { quickHandlers } from '@/utils/compressImage'
import { formatTime, get50YearsAgoTimestamp, dateToTimestamp } from '@/utils/common'

const { getToken } = useUserInfo()
// 获取信息
const infoObj = ref({})
const massageInfo = ref('')
const minDate = ref(null)
const maxDate = ref(null)
// 计算往前推3年的日期
const threeYearsAgo = new Date()
threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3)
const defaultTime = threeYearsAgo.getTime()
const firstJobDate = ref('')
const homeLocation = ref('')
const goIdentityAuth = () => {
  const idCard = infoObj.value?.idCard ? infoObj.value?.idCard : ''
  const trueName = infoObj.value?.trueName ? infoObj.value?.trueName : ''
  uni.navigateTo({
    url: `/setting/identityAuth/index?idCard=${idCard}&trueName=${trueName}`,
  })
}
// 图片id
const fileIdObj = ref({
  fileId: '',
})
const fileList = ref([])
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// 工作经验
const workYear = ref(null)
// 图片地址/attachment/uploadImgThum
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 地址
const goAdress = () => {
  const lon = infoObj.value?.lon ? infoObj.value?.lon : ''
  const lat = infoObj.value?.lat ? infoObj.value?.lat : ''
  const homeLocation = infoObj.value?.addressManager ? infoObj.value?.addressManager : ''
  uni.navigateTo({
    url: `/setting/AdressMange/index?lon=${lon}&lat=${lat}&homeLocation=${homeLocation}`,
  })
}
// before-upload 处理函数 - 使用预设的头像压缩配置
const beforeUpload = quickHandlers.avatar()
// 图片上传成功
const successFun = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  console.log(res, 'res====')
  if (res.code === 0) {
    getimgInfo(res.data[0].fileId)
  }
  console.log(res, '成功的返回')
}
// 上传头像
const getimgInfo = async (fileId: any) => {
  console.log(fileId, 'fileId===')
  const res: any = await userImageAudit({ fileId })
  if (res.code === 0) {
    getList()
  }
}
// 微信号
const goWxCode = () => {
  uni.navigateTo({
    url: `/setting/wxUpdata/index?wxCode`,
  })
}
// 修改手机号
const goPhoneUpdata = () => {
  const phone = infoObj.value?.phone ? infoObj.value?.phone : ''
  uni.navigateTo({
    url: `/setting/phoneUpdata/index?phone=${phone}`,
  })
}
// 获取信息
const getList = async () => {
  const res: any = await queryMyMessage()
  console.log(res, 'res====')
  if (res.code === 0) {
    infoObj.value = res.data
    if (infoObj.value?.firstJobDate) {
      firstJobDate.value = dateToTimestamp(infoObj.value?.firstJobDate)
    }
    if (infoObj.value?.headImgUrl) {
      fileList.value = [
        {
          url: infoObj.value?.headImgUrl,
        },
      ]
    } else {
      fileList.value = [
        {
          url:
            infoObj.value?.sex === 1
              ? '/static/header/jobhunting1.png'
              : '/static/header/jobhunting2.png',
        },
      ]
    }
    // const lat = res.data.location.lat
    // const lon = res.data.location.lon
    homeLocation.value = res.data.homeLocation
  }
}
onLoad(async () => {
  await uni.$onLaunched
  minDate.value = get50YearsAgoTimestamp()
  maxDate.value = new Date().getTime()
  // defaultTime.value = formatDateDay()
  // console.log(defaultTime.value, 'defaultTime.value')
})
onShow(async () => {
  getList()
})
// 确认
const confirm = async (e) => {
  // firstJobDate.value = e.value
  const firstTime = formatTime(e.value)
  const birthday = infoObj.value?.birthday
  const firstDate = new Date(firstTime)
  const birthDate = new Date(birthday)
  let age = firstDate.getFullYear() - birthDate.getFullYear()
  const monthDiff = firstDate.getMonth() - birthDate.getMonth()
  // 如果生日还没到，年龄减1
  if (monthDiff < 0 || (monthDiff === 0 && firstDate.getDate() < birthDate.getDate())) {
    age--
  }

  if (firstDate < birthDate) {
    massageInfo.value = '参加工作时间与年龄不匹配'
  } else if (age < 16) {
    massageInfo.value = '参加工作时间与年龄不匹配'
    // console.log(`参加工作时间与年龄不匹配（工作时年龄为${age}岁，小于16岁）`)
  } else {
    massageInfo.value = ''
  }
  // console.log(firstJobDate.value, 'firstJobDate===')
  const res: any = await updateWorkTime({ firstJobDate: firstJobDate.value })
  if (res.code === 0) {
    getList()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
  // console.log(massageInfo.value, 'massageInfo.value===')
}
</script>

<style scoped lang="scss">
::v-deep .custom-label-class {
  font-size: 32rpx !important;
  font-weight: 500;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-top: 30rpx;
  padding-right: 0rpx !important;
  text-align: right;
  background: transparent !important;
}
::v-deep .wd-picker__value {
  margin-right: 0rpx;
}
::v-deep .wd-picker__arrow {
  display: none;
}
::v-deep .wd-upload__picture {
  border-radius: 50%;
}
::v-deep .wd-picker__placeholder {
  color: #4d8fff;
}
::v-deep .wd-upload {
  justify-content: right;
}
::v-deep .custom-class {
  width: 300rpx;
  height: 100rpx;
}
::v-deep .custom-class-img {
  width: 100rpx;
  height: 100rpx;
}
::v-deep .wd-upload__close {
  display: none;
}
::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  border-radius: 50% !important;
}
.activeColor {
  color: #4d8fff;
}
.labelName {
  width: 200rpx;
  text-align: left;
}
.labelName-1 {
  width: 300rpx;
  text-align: left;
}
.containner-group-img {
  width: 100rpx;
  height: 100rpx;
}
.pageContaner-img {
  width: 100rpx;
  height: 100rpx;
  margin: 0 0 0 auto;
  border-radius: 50%;
}

.position-r {
  position: absolute;
  top: 10rpx;
  right: 0rpx;
}

.pageContaner {
  padding: 0rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
