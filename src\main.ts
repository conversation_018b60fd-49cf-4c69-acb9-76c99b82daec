import '@/style/index.scss'
import zPaging from './config/zPaging'
import { initRequest } from '@/interceptors/request'
import 'virtual:uno.css'
import { createSSRApp } from 'vue'

import App from './App.vue'
import { prototypeInterceptor, routeInterceptor } from './interceptors'
import store from './store'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(zPaging)
  app.use(routeInterceptor)
  app.use(prototypeInterceptor)
  app.use(initRequest)
  uni.$onLaunched = new Promise((resolve) => {
    uni.$isResolve = resolve
  })
  return {
    app,
  }
}
