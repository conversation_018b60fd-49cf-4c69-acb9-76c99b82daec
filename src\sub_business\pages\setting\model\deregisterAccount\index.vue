<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="注销账号"></CustomNavBar>
    <view class="resignation-wrap">
      <image
        src="../../../../static/setting/bg-warning.png"
        mode="widthFix"
        class="resignation-img"
      />

      <view class="content-text">
        <span class="content-text-item">
          尊敬的用户，当您选择注销易直聘App账号，将产生如下影响：
        </span>
        <span class="content-text-item">
          1、账号将无法登录，不能再使用本平台任何服务。后续即便用相同手机号重新注册，也无法找回已注销账号添加或绑定的任何内容
        </span>
        <span class="content-text-item">
          2、账号内的个人资料（如昵称、头像、联系方式）、求职或招聘历史信息（投递记录、聊天记录、收藏岗位等）都将被删除或匿名化处理（依法需留存的除外），后续无法检索和恢复。
        </span>
        <span class="content-text-item">
          3、若账号内有未使用完的虚拟资产、付费服务权益，将视作您自动放弃 。
        </span>
        <span class="content-text-item">
          注销是不可逆操作，请您谨慎考虑，确认无未完成事项后再进行。
        </span>
      </view>

      <view class="btn-fixed">
        <view class="btn-wrap">
          <view class="btn_box">
            <view class="btn_bg">确认注销</view>
          </view>
          <view class="btn_box btn_box_3">
            <view class="not_leave">我再用用</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
</script>

<style lang="scss" scoped>
.resignation-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 110rpx;

  .resignation-img {
    width: 200rpx;
    height: 200rpx;
  }
  .resignation-text {
    margin-top: 30rpx;
    font-size: 32rpx;
    color: #000000;
  }
  .resignation-text-sub {
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #333333;
  }
  .content-text {
    width: 85%;
    padding: 20rpx 0;
    margin-top: 80rpx;
    font-size: 28rpx;
    color: #333333;
    // border: 1px solid #333333;
    border-radius: 14rpx 14rpx 0 0;
    .content-text-item {
      display: block;
      margin-bottom: 10rpx;
    }
  }
  .btn-fixed {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 999;
    padding: 20rpx 40rpx;
  }
  .btn-wrap {
    display: flex;
    flex-direction: column;
    gap: 20rpx; // 按钮间距
  }
  .btn_box {
    width: 100%;
  }
  .btn_box_3 {
    margin-top: -10rpx;
  }
  .btn_bg {
    width: 100%;
    padding: 20rpx 0;
    font-size: 32rpx;
    color: #333;
    text-align: center;
    background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
    border-radius: 14px;
  }
  .btn_bg_1 {
    background: linear-gradient(90deg, #ffc2c2 0%, #ff5151 100%);
  }
  .not_leave {
    width: 100%;
    padding: 0 0 20rpx 0;
    font-size: 32rpx;
    color: #333;
    text-align: center;
    border-radius: 14px;
  }
}
</style>
