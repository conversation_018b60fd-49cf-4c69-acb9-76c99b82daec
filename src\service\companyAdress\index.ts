import { POST, POSTPaging } from '../index'
import { hrCompanyWorkAddresstype, queryListAdresstype, adressDeltype } from './types'
import { HttpRequestConfig } from 'luch-request'
// 添加地址
export const hrCompanyWorkAddressAdd = (
  data: hrCompanyWorkAddresstype,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/hrCompanyWorkAddress/add', data, config)
// 查询地址列表
export const queryListAdress = (data: queryListAdresstype, config?: HttpRequestConfig) =>
  POSTPaging<number>('/easyzhipin-api/hrCompanyWorkAddress/queryList', data, config)

// 删除
export const deleteAdress = (data: adressDeltype, config?: HttpRequestConfig) =>
  POST<number>('/easyzhipin-api/hrCompanyWorkAddress/delete', data, config)

// 查询审核已通过的地址

export const queryPassList = (data: queryListAdresstype, config?: HttpRequestConfig) =>
  POSTPaging<number>('/easyzhipin-api/hrCompanyWorkAddress/queryPassList', data, config)
