import { POST, POSTPaging } from '../index'
import {
  sysUserCommonPhraseQueryListDataInt,
  sysUserCommonPhraseQueryListInt,
  sysUserCommonPhraseDeleteByIdDataInt,
  sysUserCommonPhraseReSortDataInt,
  sysUserCommonPhraseAddInt,
  sysUserCommonPhraseUpdateInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** 查询用户常用语列表 */
export const sysUserCommonPhraseQueryList = (
  data: Api.Request.IResPagingDataParamsInt<sysUserCommonPhraseQueryListDataInt>,
  config?: HttpRequestConfig,
) =>
  POSTPaging<sysUserCommonPhraseQueryListInt>(
    '/easyzhipin-api/sysUserCommonPhrase/queryList',
    data,
    config,
  )
/** 删除用户常用语 */
export const sysUserCommonPhraseDeleteById = (
  data: sysUserCommonPhraseDeleteByIdDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/sysUserCommonPhrase/deleteById', data, config)
/** 重排用户常用语 */
export const sysUserCommonPhraseReSort = (
  data: sysUserCommonPhraseReSortDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/sysUserCommonPhrase/reSort', data, config)

/** 添加用户常用语 */
export const sysUserCommonPhraseAdd = (
  data: sysUserCommonPhraseAddInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/sysUserCommonPhrase/add', data, config)
/** 更新用户常用语 */
export const sysUserCommonPhraseUpdate = (
  data: sysUserCommonPhraseUpdateInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/sysUserCommonPhrase/update', data, config)
